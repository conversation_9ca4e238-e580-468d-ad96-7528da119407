# 🗄️ Database Integration Plan for SCUM Economy Chooser

## 📋 Overview
This document outlines the database integration strategy for the SCUM Economy Chooser playground app, including setup for popular database tools and credential management.

## 🎯 Database Options & Tools

### 1. **SQLite (Recommended for Development)**
- **Tool:** DB Browser for SQLite, SQLiteStudio
- **Pros:** No server setup, file-based, perfect for testing
- **Cons:** Single-user, limited concurrent access
- **Use Case:** Development, testing, single-user deployments

### 2. **MySQL (Recommended for Production)**
- **Tool:** MySQL Workbench, phpMyAdmin, DBeaver
- **Pros:** Robust, scalable, excellent community support
- **Cons:** Requires server setup
- **Use Case:** Multi-user production environments

### 3. **PostgreSQL (Advanced Option)**
- **Tool:** pgAdmin, DBeaver, DataGrip
- **Pros:** Advanced features, excellent JSON support
- **Cons:** More complex setup
- **Use Case:** Complex data relationships, advanced queries

## 🔧 Database Schema Design

### Core Tables

#### 1. **economy_items**
```sql
CREATE TABLE economy_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tradeable_code VARCHAR(255) UNIQUE NOT NULL,
    base_purchase_price INTEGER NOT NULL,
    base_sell_price INTEGER NOT NULL,
    max_purchase_price INTEGER NOT NULL,
    max_sell_price INTEGER NOT NULL,
    min_purchase_price INTEGER NOT NULL,
    min_sell_price INTEGER NOT NULL,
    category VARCHAR(100),
    subcategory VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. **change_history**
```sql
CREATE TABLE change_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tradeable_code VARCHAR(255) NOT NULL,
    field_name VARCHAR(50) NOT NULL,
    old_value INTEGER,
    new_value INTEGER,
    change_type VARCHAR(50),
    description TEXT,
    user_id VARCHAR(100),
    session_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tradeable_code) REFERENCES economy_items(tradeable_code)
);
```

#### 3. **custom_buckets**
```sql
CREATE TABLE custom_buckets (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    bucket_name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    filters JSON,
    user_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. **bucket_items**
```sql
CREATE TABLE bucket_items (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    bucket_id INTEGER NOT NULL,
    tradeable_code VARCHAR(255) NOT NULL,
    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (bucket_id) REFERENCES custom_buckets(id),
    FOREIGN KEY (tradeable_code) REFERENCES economy_items(tradeable_code)
);
```

#### 5. **user_sessions**
```sql
CREATE TABLE user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    user_id VARCHAR(100),
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    changes_count INTEGER DEFAULT 0,
    app_version VARCHAR(50)
);
```

## 🔐 Credential Management

### 1. **Environment Variables (.env file)**
```env
# Database Configuration
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_NAME=scum_economy
DB_USER=scum_admin
DB_PASSWORD=your_secure_password

# SQLite (alternative)
SQLITE_DB_PATH=./data/economy.db

# Security
SECRET_KEY=your_secret_key_here
ENCRYPTION_KEY=your_encryption_key

# API Keys (if needed)
BACKUP_SERVICE_KEY=your_backup_key
CLOUD_STORAGE_KEY=your_cloud_key
```

### 2. **Configuration Class**
```python
import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class DatabaseConfig:
    db_type: str = "sqlite"
    host: str = "localhost"
    port: int = 3306
    database: str = "scum_economy"
    username: str = ""
    password: str = ""
    sqlite_path: str = "./data/economy.db"
    
    @classmethod
    def from_env(cls):
        return cls(
            db_type=os.getenv('DB_TYPE', 'sqlite'),
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', '3306')),
            database=os.getenv('DB_NAME', 'scum_economy'),
            username=os.getenv('DB_USER', ''),
            password=os.getenv('DB_PASSWORD', ''),
            sqlite_path=os.getenv('SQLITE_DB_PATH', './data/economy.db')
        )
```

## 🛠️ Implementation Strategy

### Phase 1: SQLite Foundation
1. **Create SQLite database manager**
2. **Implement basic CRUD operations**
3. **Add change tracking**
4. **Test with existing GUI**

### Phase 2: MySQL Integration
1. **Add MySQL connection manager**
2. **Implement connection pooling**
3. **Add transaction support**
4. **Migration scripts**

### Phase 3: Advanced Features
1. **Real-time change synchronization**
2. **Conflict resolution**
3. **Backup and restore**
4. **Performance optimization**

## 📦 Required Python Packages

```txt
# Database drivers
sqlite3  # Built-in
mysql-connector-python>=8.0.0
psycopg2-binary>=2.9.0  # PostgreSQL

# ORM (optional)
sqlalchemy>=1.4.0
alembic>=1.7.0  # Migrations

# Security
python-dotenv>=0.19.0
cryptography>=3.4.0

# Connection pooling
sqlalchemy-pool>=1.4.0

# Database tools
pandas>=1.3.0  # Data analysis
```

## 🔄 Data Migration Strategy

### 1. **JSON to Database Migration**
```python
def migrate_json_to_db(json_file_path: str, db_manager):
    """Migrate existing JSON data to database"""
    with open(json_file_path, 'r') as f:
        data = json.load(f)
    
    for code, item_data in data.get('tradeable-code', {}).items():
        db_manager.insert_economy_item(
            tradeable_code=code,
            base_purchase_price=item_data.get('base-purchase-price', 0),
            base_sell_price=item_data.get('base-sell-price', 0),
            # ... other fields
        )
```

### 2. **Database to JSON Export**
```python
def export_db_to_json(db_manager, output_file: str):
    """Export database data back to JSON format"""
    items = db_manager.get_all_economy_items()
    
    json_data = {
        "tradeable-code": {
            item.tradeable_code: {
                "base-purchase-price": item.base_purchase_price,
                "base-sell-price": item.base_sell_price,
                # ... other fields
            }
            for item in items
        }
    }
    
    with open(output_file, 'w') as f:
        json.dump(json_data, f, indent=2)
```

## 🚀 Quick Start Guide

### 1. **SQLite Setup (Easiest)**
```bash
# Install DB Browser for SQLite
# Download from: https://sqlitebrowser.org/

# Create database
python -c "
import sqlite3
conn = sqlite3.connect('economy.db')
# Run schema creation scripts
conn.close()
"
```

### 2. **MySQL Setup**
```bash
# Install MySQL Server
# Download MySQL Workbench

# Create database
mysql -u root -p
CREATE DATABASE scum_economy;
CREATE USER 'scum_admin'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON scum_economy.* TO 'scum_admin'@'localhost';
FLUSH PRIVILEGES;
```

### 3. **Environment Setup**
```bash
# Create .env file
echo "DB_TYPE=sqlite" > .env
echo "SQLITE_DB_PATH=./economy.db" >> .env

# Install packages
pip install mysql-connector-python python-dotenv sqlalchemy
```

## 📊 Benefits of Database Integration

### 1. **Performance**
- Faster queries with indexing
- Efficient data filtering
- Optimized joins for complex operations

### 2. **Data Integrity**
- ACID transactions
- Foreign key constraints
- Data validation at database level

### 3. **Scalability**
- Multiple concurrent users
- Large dataset handling
- Horizontal scaling options

### 4. **Advanced Features**
- Full-text search
- Complex aggregations
- Real-time change tracking
- Automated backups

## 🎯 Next Steps

1. **Choose database type** (SQLite for development, MySQL for production)
2. **Install database tools** (DB Browser, MySQL Workbench, etc.)
3. **Set up credentials** using .env file
4. **Create database schema** using provided SQL scripts
5. **Implement database manager class**
6. **Test migration from existing JSON files**
7. **Integrate with existing GUI**

This plan provides a solid foundation for database integration while maintaining compatibility with existing JSON-based workflows.
