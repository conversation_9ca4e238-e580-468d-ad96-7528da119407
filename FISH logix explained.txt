**F.I.S.H. Logic – XconomyChooser Implementation Document**

---

## 1. Context & Purpose

Within the XconomyChooser project, **F.I.S.H.** logic (Filtered, Indexed, Scored, Hierarchical) serves as a decision framework to dynamically govern how economy files are selected and uploaded to game servers. It ensures automated behavior that’s intelligent, contextual, and override-capable — particularly when running in randomized, scheduled, or rule-based deployment modes.

Unlike traditional config uploaders, F.I.S.H. logic in XconomyChooser enables the tool to react to conditions like time of day, file category, server state, admin intent, or seasonal factors.

---

## 2. The Four F.I.S.H. Layers in XconomyChooser

### 🧹 **Filtered**

* Filters out inappropriate files before evaluation (e.g., corrupt JSON, test configs, outdated formats)
* Example: Skip files where `"enabled": false` or `"version" < server_required_version`

```python
if not config.get("enabled", True): continue
if config.get("version", 0) < SERVER_REQUIRED_VERSION: continue
```

### 🔍 **Indexed**

* Files and folders are tagged with metadata such as:

  * `type: season`, `type: event`, `priority: high`, `audience: pvp`
* Indexing enables fast narrowing of eligible configs for scheduled deployment
* Tags are extracted from config filenames, headers, or associated `.meta.json` files

```json
{
  "filename": "summer_prices.json",
  "tags": ["season", "hotfix", "pve"],
  "profile": "Lightweight"
}
```

### 🧮 **Scored**

* Configs are evaluated based on a scoring algorithm:

  * Example: `(tag_weight * freshness_score) + random_bias`
* Used when `random_mode` is enabled, but selection is weighted toward preferred outcomes

```python
score = (10 if "event" in tags else 1) + freshness_in_days * 0.2 + random.uniform(0, 1)
```

* The top N scored configs are considered, one selected randomly from weighted pool

### 🗂 **Hierarchical**

* Logic allows fallback rules:

  * Admin overrides always win
  * Global profile logic > folder profile logic > file logic
* Example:

  * Global rule: Never deploy deprecated configs
  * Folder rule: Prefer PvP configs after 18:00
  * File rule: Specific timing or expiration

---

## 3. Logic Categories in XconomyChooser

* **📁 File-Based Logic:** Applies logic to file metadata and naming
* **⏱ Temporal Logic:** Schedules tied to real-time clock or calendar
* **⚠ Priority Logic:** Gives weight to recent edits, admin overrides
* **🧪 Testing Logic:** Optionally flags sandbox/test uploads with mock actions

---

## 4. Execution Flow (Random Scheduled Upload Mode)

```
START
 → Read all files in selected folder
 → FILTER: Remove incompatible/disabled/expired configs
 → INDEX: Load tags and profile types
 → SCORE: Weight by context (e.g. season, freshness, priority)
 → HIERARCHY: Apply override logic (admin-defined rules win)
 → SELECT file to upload
 → EXECUTE SFTP transfer
 → LOG outcome
```

---

## 5. Example Rule Fragment (XconomyChooser)

```yaml
- id: summer_upload
  filter: file.tags includes "season"
  index: [profile:balanced, event:none]
  score: 10 if filename starts with "summer" else 2
  hierarchy: [global, folder:seasonal_uploads]
  action:
    - upload_to_sftp(file)
    - log("Uploaded seasonal config")
```

---

## 6. Debugging and Trace Output

### Logging Output:

```
[FISH] 14:02:34 | File filtered out: test_config_v1.json (disabled)
[FISH] 14:02:34 | Indexed 3 configs from /seasonal/
[FISH] 14:02:34 | Score: winter_sale.json = 5.8
[FISH] 14:02:34 | Score: summer_event.json = 9.1 ✅ SELECTED
[FISH] 14:02:35 | SFTP Upload OK -> /SCUM/Server/Config/EconomyOverrides.json
```

### Debug Mode Flag:

Set `XCHOOSE_DEBUG=1` to activate F.I.S.H. trace logs

---

## 7. Summary

In XconomyChooser, F.I.S.H. is not just a clever acronym — it's a robust layered logic model that governs automated and context-aware config deployment. Whether prioritizing certain profiles, eliminating broken files, or executing at strategic times, F.I.S.H. makes sure the right config gets sent at the right time — with traceable logic and override safety.
