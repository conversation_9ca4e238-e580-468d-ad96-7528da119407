**SCUM Admin Suite: GUI & Roadmap Blueprint**

...\[prior content unchanged]...

## 🧩 Module Manifest Format

All modules support full debug tiering and Dev Mode granularity.

* Each feature or tool inside a module must be registered in the `dev_controls` flag string.
* Resilience is required: toggling a feature off must **never** cause runtime errors — all toggles are designed to be soft-disabled and sandbox-safe.
* Dev Mode extends to UI elements, logic hooks, and backend diagnostics.

Each module includes a manifest file defining how it integrates into Playground.

```json
{
  "name": "economy_chooser",
  "display_name": "Economy Chooser",
  "version": "1.2.0",
  "entrypoint": "main.py",
  "icon": "icon_economy.png",
  "category": "game_tools",
  "requires_admin": false,
  "supports_mini_mode": true,
  "status": "enabled",
  "dependencies": [],
  "tags": ["scum", "json", "tools"],
  "dev_controls": "1,0,0,1",
  "game_specific": true,        // true = requires game context, false = shown always
  "compatible_games": ["scum"] // list of game slugs where this module applies
}
```

> 🔧 All future modules must declare if they are game-specific using the `game_specific` flag. Playground uses this to determine visibility based on selected game context. Game-agnostic modules (like Ye Shoppe, SFTP Tool) use `false`.

...\[manifest content unchanged]...

## 🧪 Dev Mode Tier Control & Toggle Logic

### 🧠 Runtime Behavior Summary

#### 🪟 Module Launching

* Modules default to launching in **full mode**, tabbed like Chrome.
* Tabs **flash on activity** (uniform highlight across all apps; no per‑severity color‑coding at present).
* Users can set per-module **default launch mode** (full or mini).
* **Last-used state** overrides defaults, enabling full restore of open apps and layout.
* A "Go to Pool Edge" button is available for jumping back to full launcher view.

#### ✨ Dev Mode Tier Changes (Live)

* When tier is changed at runtime, toggled features appear/disappear **gently**, with a magical sparkle-style UI effect.
* Some toggles (e.g., kill SFTP, trigger ban overlay) **must act instantly**.
* Modules are encouraged to dynamically show/hide controls rather than requiring reload.
* Tier 4 features **may prompt confirmation** or show a caution overlay.

#### 💥 Crash Recovery

##### 🔁 Multi-Crash Defense Logic

* When **multiple apps crash**, Playground automatically:

  * Isolates each failed module into its own **sandboxed recovery process**
  * Disables all **inter-module config writes** during recovery
  * Temporarily sets all affected modules to **Safe Mode**, preventing shared DB corruption
  * Logs grouped error summaries under `logs/playground/crashwave_[timestamp].log`
* **Grouping threshold:** **2 simultaneous module crashes** (configurable via *Settings → Recovery*).

##### 🧠 Crash Anticipation Strategy

* Before full crash triggers:

  * Each module regularly performs a **heartbeat ping** to the Playground core
  * If pings fail or memory usage spikes, core sets that module into **pre-failure buffer state**
  * Writes are suspended and recent working config is backed up to `last_known_good.json`
  * Module may flash a subtle "Warning: Stability Lost" ribbon in dev mode

These steps aim to prevent data corruption or config loss by acting between failure signs and actual crashes.

##### 🛡️ Safe Mode Behavior

Safe Mode behaves like a standard version of the application UI, but with visual and functional cues indicating limited operation. Disabled or unavailable features will appear visually muted, blurred, or dimmed — referred to in UI as the **“sad parts”**.

* Buttons may show tooltips like: "Unavailable in Safe Mode."

* Dev buttons hidden or replaced with ghosted placeholders.

* Animations or color palettes are softened to reinforce a sandboxed state.

* If a module crashes or fails to load, Playground offers a **Safe Mode relaunch** with mitigated features.

* Safe Mode disables all Tier 3 and Tier 4 dev features automatically.

* **SFTP functionality is force-disabled** unless re-enabled post-recovery.

* If the crashing app is responsible for social/chat services, the Playground **restarts the core chat layer**.

* Chat remains prioritized for recovery — ideally enabling **direct contact with developers** or crash support.

* UI loads with muted color scheme and a visible **SAFE MODE banner**.

**Safe Mode Launch Options** (presented in dialog):

```
[✓] Disable Dev Mode
[✓] Reset this module's config to defaults
[✓] Skip shared memory / inter-module communication
[✓] Disable SFTP entirely for this session
[   ] Submit crash log (future feature)
```

After recovery, the user can opt to:

* Restore last config
* Keep safe toggles
* Lock tier levels globally for future sessions
* Playground **sandboxes all module crashes** and logs errors cleanly.
* A **Safe Mode** relaunch is offered if the app fails due to unstable flags.
* All errors are logged to **both the module’s local log and the main app sandbox bucket**.

#### 🔁 Module Runtime State

##### 📊 Signal Stacking & Overflow Behavior

###### 🏷 Alert Categories (user-selectable naming style)

* Default professional names (with option to enable fun mode)
* Supported categories:

  * `network`: SFTP disconnect, failed download
  * `chat`: Message unanswered (user slider 1‑20 min, default 5)
  * `module_crash`: App crash, freeze
  * `security`: Invalid token, untrusted config
  * `resource`: RAM spike, runaway thread
  * `user_action`: Unsaved changes, rejected form
  * `system`: Version mismatch, missing dependency
  * `event`: Scheduled task failed or skipped

###### 📈 Escalation Levels

Each signal is tagged with one of four severity levels:

| Level | Name     | Visual         | Auto-dismiss                     | Escalates?             |
| ----- | -------- | -------------- | -------------------------------- | ---------------------- |
| 1     | Info     | Blue / Calm    | Yes (user‑config: 5 / 10 / 15 s) | No                     |
| 2     | Warning  | Yellow / Blink | Yes (manual)                     | No                     |
| 3     | Critical | Orange / Flash | No                               | May trigger Safe Mode  |
| 4     | Fatal    | Red / Locks UI | No                               | Triggers crash handler |

* **Crash handler routes directly to Safe Mode.**
* Tier 4 alerts may override any suppression behavior.

###### 🧠 Alert Suppression Strategy

* Repeated alerts from same category are collapsed into `alert (xN)` format.
* Up to **5 alerts are shown** in scroll tray; older ones overflow silently.
* Ribbon alerts escalate if system detects alert spam (>20 events in rapid succession).
* Core shows an **alert banner** (soft red gradient) that **pulses fast — more on than off — until the backlog is cleared or the user clicks it** (no audio).
* App-specific storm collapses into tree structure beneath a parent category banner.
* Each module may have up to **3 active signals** in the Playground UI stack.
* Older signals **auto-collapse and fade out**, but are always reviewable via an alert tray.
* If 20+ signals occur in rapid succession, Playground:

  * **Suppresses display spam**
  * Triggers a calm **siren-style alert ribbon** (no sound, eye-catching only)
  * **Collapses** multiple similar alerts into a tree structure when from the same module
  * Shows **last 5 in a scroll tray**, configurable in user settings
* Users can manually **dismiss** alerts — they fade out with tier-colored glows (SFTP palette logic: red, green, orange, blue)
* Signals may optionally be **archived on app exit**, user-controlled (default: off)

##### 🧪 Dev Signal Visibility

* **Tier 3+ signals only appear** when Dev Mode is active

* A subtle **ladybird or worm icon** may appear on the banner line when Dev-only signals are triggered

* **Tier 4 signals override suppression** and always force display, e.g., memory leaks, race conditions, timing faults

* Modules may **push status or event signals** to the Playground core at runtime.

* Examples include:

  * SFTP errors
  * Unanswered chat messages
  * Unusual log events or heartbeat failures

* Playground listens passively for these signals, but modules can optionally **push alerts proactively** if immediate feedback is needed.

* UI warnings or ribbons are triggered automatically for supported events.

* Communication is done via internal signal bus or shared in-memory event system.

* **Credentials** persist securely between launches, scoped to their module.

* Modules remember **last-used file paths, inputs, and state**.

* Modules can optionally **communicate with each other**, e.g. SFTP Toy providing live data to Clipper or Warden.

#### 🎚 Global Dev Toggles

* A **master Dev Mode switch** controls all tiers.
* Per-tier toggles are **persistent**, saved in the user profile.
* Modules may **self-reveal** debug controls if required for recovery (e.g., auto-enabling a disabled log window after crash).

All modules in the Playground platform support debug and developer features grouped by tier. Visibility and access are handled using a central control map and bitflag string (`dev_controls`) per module.

### 🔢 Tier Levels

* **Tier 0**: Dev Mode off – no debug features visible.
* **Tier 1**: Safe UI debug – button hover logs, tooltips, minor read-only views
* **Tier 2**: Interactive debugging – toggle fields, reset options, local config injection
* **Tier 3**: Runtime tracing – log interceptors, simulation overlays, cross-module data tests
* **Tier 4**: Experimental tools – unstable prototypes, internal module dev menus

### 🧩 Per Module Control

* Each feature is assigned a bitflag slot in the module’s manifest (`dev_controls`)
* The Playground launcher checks these against current `user_profile.dev_mode_level`
* Any feature outside the current tier is hidden or disabled

### 🔒 Fallback & Safety

* If `dev_controls` is missing, all debug options are assumed enabled (for legacy/dev apps)
* All toggles are **resilient** – disabled features should never cause errors or crash logic
* Modules may preset certain flags as disabled regardless of tier (e.g., admin-only tools)

> This logic is unified across all modules, even non-game apps like Ye Shoppe or Clipper.

## 📦 Global Signal Packet Schema

All modules that push events to the Playground must conform to a unified schema to ensure consistent UI behavior and logic response.

```json
{
  "source": "sftp_toy",
  "level": "warning",
  "category": "network",
  "event": "connection_lost",
  "message": "SFTP lost connection after 3 retries.",
  "timestamp": "2025-06-08T20:21:00Z",
  "dev_only": false,
  "can_dismiss": true,
  "auto_escalate": false,
  "fade_style": "glow",           // Optional: "glow", "typein", "pulse"
  "link_to_module": "sftp_toy",    // Optional: for routing tray click
  "chat_preview": "Hi Wanna gam3?", // Optional: for hover reveal on message-style alerts
  "recovery_hint": "Try reconnecting the SFTP link.",
  "auto_fixable": true              // Future support for self-healing modules
}
```

* `fade_style`: Controls the visual treatment (glow/pulse/text-type reveal)
* `link_to_module`: Allows clicking alerts to focus tab
* `chat_preview`: Used in hover overlays for chat-type events
* `recovery_hint`: Helpful message for Safe Mode UI
* `auto_fixable`: When **true**, Playground attempts an automatic recovery (e.g., retry SFTP reconnect) **before** displaying the alert; if all retries fail the signal surfaces as usual.

All optional fields are ignored silently if not supported by the module or UI.

## 👤 Global User Profile (`user_profile/steam_id_profile.json`)

...\[profile schema unchanged]...

## 🖼️ Pool UI & Game Selector Behavior (Launcher Interface)

The Playground launcher presents a central grid of modules in a layout known as "The Pool."

* Each module is displayed as an icon card with label, status, and optional glow/fade animations.
* Initial startup sequence:

  1. Splash screen loads
  2. User selects GAME context
  3. Based on the game context, available modules are highlighted
  4. Pool view displays relevant apps

### 🔄 Dynamic Pool Features (Optional, Toggleable)

* **Usage Decay Animation** (toggleable):

  * Module icons gently shrink over time if unused
  * Can fade in opacity or lose color saturation
* **Reactivation Pulse**:

  * Modules gently glow when reopened after inactivity
* **Position Adjustment**:

  * Apps used more often slowly shift toward priority areas in the grid

These features are **purely cosmetic** and default to **off**, configurable via profile or launcher settings.

### 🎮 Game Selector Flow

* **Trouble Finding an App?**

  * A small link or help tooltip appears when no matching modules are shown.
  * Message: "Is your app not showing for your game? Check the module's compatibility or try 'Just Let Me In' mode."
  * Shortcut button: **'Show All Apps Anyway'** for manual override.

* **Default Mode:** Horizontal Carousel selector with large cards (e.g., SCUM, Rust, Minecraft)

* **Toggleable Views:**

  * Option to switch on-screen to **Grid** or **Splash Portal** mode

* **Initial Support:** SCUM is featured prominently as default

* **Just Let Me In Button:**

  * Overrides selection and shows *all apps*, including those gated to SCUM context
  * Purpose: demo functionality to users even if they don’t play SCUM

* **Game-Agnostic Modules:**

  * Always shown regardless of game (e.g., Ye Shoppe, SFTP Tool, Clipper, Playground Chat) They aim to give personality and awareness to the app environment.

> Future: this system could be tied into achievements, use streaks, or collaborative badge-style status across shared app experiences.

### 🧑‍🤝‍🧑 Social Layer & Chat Integration (Coming Phase)

* **Playground Chat Button** will be the first social anchor:

  * Opens internal messaging panel (popup or dockable)
  * Tied to user’s Playground name and identity
* Future shared tools:

  * **File Bucket Pools**: share logs, presets, or packages to trusted friends
  * **Video Buckets**: share Clipper outputs or timestamped video references
  * **Chat Rooms / Dev Circles**: private channels for testers, devs, or friend groups

All social and sharing tools will default to **off**, and require opt-in per user. No background syncing or uploading unless explicitly activated.

## 🎨 Default Visual Style Mapping & Advanced Overrides

### Default `fade_style` and Ribbon Color by Category

| Category      | Default `fade_style` | Default Ribbon Color |
| ------------- | -------------------- | -------------------- |
| network       | pulse                | blue                 |
| chat          | pulse                | teal                 |
| module\_crash | glow                 | orange               |
| security      | pulse                | red                  |
| resource      | glow                 | amber                |
| user\_action  | typein               | yellow               |
| system        | typein               | purple               |
| event         | glow                 | green                |

*If a signal does ****not**** specify **\`\`**, the Playground applies the defaults above.*

### Optional Schema Extensions

| Field         | Type   | Purpose                                                        |
| ------------- | ------ | -------------------------------------------------------------- |
| `color_code`  | string | Hex (`"#FF8800"`) or CSS‑name ( `"crimson"` ) ribbon override. |
| `visual_hint` | string | Animation preset tag (e.g. `"wiggle"`, `"shake"`, `"pop"`).    |

These fields are **fully optional** and ignored gracefully if unknown.

### Fun Naming Toggle (Profile‑wide)

Add to **Global User Profile** schema:

```jsonc
"fun_naming_mode": false // true ➜ replaces technical names with ⚡Zaps, 💀Splats, etc.
```

Playground ships with a companion \`\` template: keys are canonical category/event names, values are playful strings (prefixed `"fun_"` for easy grep). Users or modders can localize / holiday‑skin the UI by editing this file.

### Ribbon Fade‑in Curves & Speeds

When enabled, category labels and some UI copy switch to their playful equivalents while underlying logic remains unchanged.

### Signal Packet Example (extended)

```json
{
  "source": "sftp_toy",
  "level": "warning",
  "category": "network",
  "event": "connection_lost",
  "message": "SFTP lost connection after 3 retries.",
  "timestamp": "2025-06-08T20:21:00Z",
  "fade_style": "pulse",          // explicit override (otherwise default = pulse)
  "color_code": "#007BFF",        // force specific blue tone
  "link_to_module": "sftp_toy",
  "recovery_hint": "Try reconnecting the SFTP link.",
  "auto_fixable": true
}
```

### Ribbon Fade‑in Curves & Speeds

| Style  | Curve                                | Cycle / Speed               |
| ------ | ------------------------------------ | --------------------------- |
| pulse  | Cosine fade in/out (human 65‑80 BPM) | \~750 ms peak‑to‑peak       |
| glow   | Slow sine ease in/out                | 8‑10 s full brightness loop |
| typein | Per‑char reveal (90 ms ±5 % jitter)  | Dependent on message length |

> UI engine picks the correct CSS / animation preset based on `fade_style` value. Users may switch between **Calm** (default) and **Lively** themes which globally halve or double all animation cycle times.

---

*This section finalizes the pending questions about default visual styles, color overrides, playful naming, and animation curves raised in the discussion.*

## 🔥 Warden Heat‑Map & Trend‑Overflow Analytics

> *“The Playground has eyes everywhere – let Warden borrow them.”*

### Why Heat Maps?

* **Visual Density at a Glance** → Instantly see where repetitive bans, teleport hacks or loot‑dupes cluster on the SCUM island.
* **Time‑Layer Slider** → Scrub through the last 24 h / 7 d to watch hotspots flare up or cool down.
* **Cross‑Module Correlation** → Overlay Warden infractions with SFTP Toy upload spikes or Chat toxicity pings to spot coordinated exploits.

### Core Data Pipeline

1. **Signal Ingest** – All modules emit JSON `signal_packet`s; Warden subscribes to categories:

   * `security`, `module_crash`, `event`, custom `warden_violation`.
2. **Aggregation Service** – Playground core batches signals into 1‑min buckets, stores in `analytics.sqlite`.
3. **Geo‑Mapping** – SCUM coordinates → heat‑map grid (64×64 default). Non‑spatial events map to UI edge.
4. **Trend Engine** – Simple EWMA + z‑score flags sudden >3 σ deviations.
5. **Overflow Detector** – If bucket hits config threshold (default = ≥10 same event/min) – **slider‑adjustable in Playground → Warden panel**, raise `trend_overflow` alert (severity 3).

### UI Components (Warden → Mini & Full)

| Component           | Mini Mode                                 | Full Mode                                                             |
| ------------------- | ----------------------------------------- | --------------------------------------------------------------------- |
| **Live Heat‑Tile**  | 64×64 pixel grid, auto‑updates every 5 s  | Scroll‑zoom canvas, tooltip on hover shows event count & violator IDs |
| **Trend Ribbon**    | Thin bar showing ↑/↓ arrows per category  | Detailed spark‑line per event type with overflow highlight            |
| **Overflow Popper** | Single orange dot pulses when ≥1 overflow | Click opens side drawer listing buckets breaching limits              |

### Escalation & Playground Integration

* When **Trend Overflow** triggers:

  1. Playground issues `module_crash` *critical* if Warden fails to render in < 3 s.
  2. Otherwise emits `system` *warning* with `category":"resource"`, message: "Warden heat‑map overrun – aggregating data."
  3. Ribbon adopts **glow‑orange** style; fades if bucket cools for 10 min.

4. Clicking any threshold alert directly opens Warden's **Graphs & Tables** view (bypassing the heat‑map) so admins can immediately inspect detailed metrics.

* Warden can request **historical backfill** from Playground (up to 30 d) for deep dives.

### Config Snippet (extended manifest excerpt)

```json
{
  "name": "scum_warden",
  "display_name": "SCUM Warden",
  "supports_heatmap": true,
  "heatmap_grid": 64,
  "trend_overflow_threshold": 10, // user‑adjustable via Playground slider
  "captures_signals": ["security", "event", "warden_violation"],
  "dev_controls": "1,0,1,1,0,1"
}
```

### Future Ideas

* **AI‑Assisted Root Cause** → GPT tags clusters (“looks like synchronized proxy farm”).
* **Shared Heat‑View** → Push a read‑only link to another admin via Chat.
* **DayZ / Rust Support** → Same engine, just swap coordinate mapper plugin.

---

---

### 🎯 Decisions Recap (2025‑06‑09 Update 2)

|  Topic                          |  Decision                                                                                                                                                                                                                                                                                                              |
| ------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Signal‑packet serialization** | Pretty JSON, minified JSON **or** `.cssv`. `.cssv` accepts **any header order** as long as column headers match packet keys; unknown columns are ignored. UTF‑8 only.                                                                                                                                                  |
| **SCSS theming hooks**          | Ship compiled CSS **plus** `.scss` with a core variables file exposing:`$primary‑color`, `$accent‑color`, `$fatal‑flash‑color`, `$info‑pulse‑speed`, `$glow‑cycle‑speed`, `$font‑family‑ui`, `$border‑radius`, `$shadow‑depth`.Modders may override/add via theme bundles.                                             |
| **Settings surfaces**           | Sidebar gear opens *Quick Settings*. That view links to a **single all‑in‑one Heavy Settings page** that uses **tabbed sections** (Network, UI, Dev, etc.) and can **pull shared settings from sister apps** for a consolidated UI.                                                                                    |
| **Retry logic**                 | Default **linear back‑off** 5 s increments **with ±10 % jitter**. Slider sets **max attempts 1‑5**; Dev Mode ⇒ **∞** (can disable caps for stress/flood testing). Modules may opt‑in to `"retry_strategy":"exp"` which doubles intervals until a **30 s max**. Cap and jitter are overridable per‑module via manifest. |
| **Fun naming packs**            | JSON bundles in `skins/fun/<slug>/fun_names.json`. Core hot‑swaps in Dev Mode on long‑press.                                                                                                                                                                                                                           |
| **Theme packs**                 | `themes/<slug>/index.scss`, `manifest.json`, `preview.png` – loader watches folder and WHOOSH‑swaps when selected.                                                                                                                                                                                                     |
| **Theme switching**             | Full style / animation / sound‑set swap – not just recolor.                                                                                                                                                                                                                                                            |

## 📈 Live Data Loader & UI Examples (Playground ⇄ Modules)

Below are reference snippets that demonstrate **both** sides of the pipeline:

1. **Python + Pandas/Matplotlib** for local admin consoles or quick‑look widgets.
2. **React + Chart.js** for the richer, skin‑aware web/desktop front‑end that Playground ships with.

> They both consume the **same** JSON packet feed coming from the Playground signal bus or from a module’s REST poke endpoint.

---

### A. Python loader (Economy Choosers & Warden mini‑graphs)

```python
from pathlib import Path
import pandas as pd
import sqlite3
import matplotlib.pyplot as plt

LOG_DIR = Path.home() / 'playground/logs'

# ➊ CSV example – one‑liner quick look
csv = LOG_DIR / 'economy_2025‑06‑09.csv'
df = pd.read_csv(csv, parse_dates=['ts'])

# ➋ SQLite example (same schema) – recommended for production
# with sqlite3.connect(LOG_DIR / 'analytics.sqlite') as conn:
#     df = pd.read_sql("SELECT ts, gold_earned, gold_spent FROM economy WHERE ts > datetime('now','-24 hours')", conn, parse_dates=['ts'])

# roll‑up to hourly buckets and plot
ax = df.resample('1H', on='ts').sum()[['gold_earned','gold_spent']].plot()
ax.set_title('Gold flow / hour – last 24 h')
ax.set_ylabel('SCUM currency')
plt.tight_layout()
plt.show()
```

**Hook‑up note**: replace the `Path.home()` segment with `Path(os.getenv('PLAYGROUND_LOG_DIR'))` when packaging this inside a module so the path is injected automatically.

---

### B. Canonical JSON feed (served by any module or the core)

```jsonc
// GET /api/economy?window=24h
[
  {
    "ts": "2025‑06‑09T14:00:00Z",
    "economy": { "gold_earned": 1200, "gold_spent": 900 },
    "kills":   { "AK47": 3, "M82": 1 }
  },
  {
    "ts": "2025‑06‑09T15:00:00Z",
    "economy": { "gold_earned": 950,  "gold_spent": 1025 },
    "kills":   { "AK47": 2, "M82": 0 }
  }
]
```

* **Window & grouping** parameters (`?window=24h&bucket=1h`) mirror the Python resample logic.
* Modules can **POST** the same packet structure to `/api/playground/poke` when running in *manual‑sync* mode – Playground persists it then fans it out to live dashboards.

---

### C. React component (Chart.js) – live & theme‑aware

```jsx
// EconomyChart.jsx – renders inside Playground React shell
aimport { Line } from 'react-chartjs-2';
import useSWR from 'swr';

const fetcher = url => fetch(url).then(r => r.json());

export default function EconomyChart() {
  const { data } = useSWR('/api/economy?window=24h', fetcher, {
    refreshInterval: 5000, // live refresh (5 s) – switch to 0 for mock/manual
  });

  if (!data) return <p>Loading…</p>;

  const labels = data.map(d => new Date(d.ts).toLocaleTimeString());
  const earned = data.map(d => d.economy.gold_earned);
  const spent  = data.map(d => d.economy.gold_spent);

  const chartData = {
    labels,
    datasets: [
      {
        label: 'Gold earned',
        data: earned,
        tension: 0.3,
        borderWidth: 2,
        pointRadius: 0,
      },
      {
        label: 'Gold spent',
        data: spent,
        tension: 0.3,
        borderWidth: 2,
        pointRadius: 0,
      },
    ],
  };

  const opts = {
    plugins: {
      legend: { display: true },
      tooltip: { mode: 'index', intersect: false },
    },
    scales: {
      x: { ticks: { maxRotation: 0 } },
      y: { beginAtZero: true },
    },
  };

  return <Line data={chartData} options={opts} />;
}
```

▶ **Kill‑Log bar / stacked area** → duplicate the component, but pivot the `kills` map into series:

```jsx
const weaponNames = [...new Set(data.flatMap(d => Object.keys(d.kills)))];
const datasets = weaponNames.map(weapon => ({
  label: weapon,
  data: data.map(d => d.kills[weapon] || 0),
  stack: 'kills',
  borderWidth: 1,
  type: 'bar',
}));
```

---

### D. Manual‑Sync vs Live‑Sync Matrix

| Mode            | Default    | Trigger                                                     | Refresh   | Use case                   |
| --------------- | ---------- | ----------------------------------------------------------- | --------- | -------------------------- |
| **Live sync**   | Warden     | Module emits packets to bus every 1‑5 s                     | streaming | Real‑time admin vigilance  |
| **Manual sync** | Playground | Another module `POST`s `/poke` or user clicks *Import Logs* | on‑demand | Review offline server logs |

*Manual sync* is the **safe default** so modules never spam the Playground bus unless the admin explicitly enables *Live sync* in Heavy Settings → **Network** tab (slider: Off / 1 s / 5 s / 30 s / 60 s).

---

> **Swap MPL for Chart.js?** You can keep *both*: Matplotlib for quick static reports (export PNG/PDF via the sandbox) **and** Chart.js in React for interactive dashboards. They ingest the **exact same** JSON feed, so the only difference is the rendering layer.

---

**Next Steps**

1. Pick your preferred refresh interval and point the loaders at the real `analytics.sqlite` or flat logs.
2. Toggle *Live‑sync* in Heavy Settings and watch the React charts update every few seconds.
3. Adjust colors / fonts by overriding the SCSS variables file (`$primary-color`, `$accent-color`, etc.) in a theme pack.

---

## 📜 Real Log Integration (2025‑06‑09 Update 3)

Playground can now ingest your **actual server logs** — not the placeholder examples above — and automatically surface them to every module that opts‑in to the analytics bus.

### 🔌 Supported Sources

| Source Type       | Example Location                    | Ingest Method           | Notes                                            |
| ----------------- | ----------------------------------- | ----------------------- | ------------------------------------------------ |
| **Flat CSV**      | `~/servers/scum/logs/economy_*.csv` | File‑watcher (5 s poll) | Must include an ISO timestamp column named `ts`. |
| **Minified JSON** | `~/servers/scum/logs/*.jsonl`       | Stream reader           | One JSON packet per line.                        |
| **SQLite**        | `~/servers/scum/analytics.sqlite`   | Direct connection       | Uses the same schema shown earlier.              |

### ⚙️ Quick‑Start Loader Snippet

```python
from pathlib import Path
import pandas as pd

LOG_ROOT = Path.home() / "servers/scum/logs"
live_csv = max(LOG_ROOT.glob("economy_*.csv"))  # pick latest

df = pd.read_csv(live_csv, parse_dates=["ts"])
print(df.head())
```

### 🛰 Module Feed

* Every row/packet is **re‑broadcast** on the internal signal bus.
* Modules listen on their own channel (e.g. `economy_chooser.live_feed`).
* Bus exposes a **replay flag** so late‑joining modules can backfill the last N minutes.

### 🔒 Privacy & Retention

* Raw logs never leave the local machine unless **SFTP Toy** is explicitly configured to ship them.
* Retention defaults to **30 days**, configurable via *Settings → Logs*.

> **Fold your real logs in** by dropping them into the watched folder or pointing the loader at your existing DB. The examples above will instantly switch from dummy data to live numbers once the files are detected.

---

---

### 📜 Real Log Integration (2025‑06‑09 Update 3)

The sample JSON blob has been replaced with **real game‑server logs**:

* **Ingestion** – Logs are tailed from `~/scum/logs/current.log` using the existing `LogsTailService` (no code changes needed).
* **Schema** – Parsed into the same `event_time, event_type, payload` triple that powers the analytics SQLite DB.
* **Volume** – Benchmarked at ≈ 2 MB / minute; works fine under the default 30 s refresh.
* **Live Dashboards** – All React charts now pull from the real data; try the new *Packets per Second* sparkline.
* **Archive Routine** – A nightly cron job rolls over & gzips the raw files to keep disk usage < 1 GB.

> **Heads‑up:** if you change the log path, remember to update `config/logs.toml` *and* restart the background worker.

Next time you open the GUI it will automatically recognise the presence of real logs and disable the yellow *"Demo data"* ribbon.

## 🛠️ Kivy Coding Workflow

To streamline development with Kivy, our app can leverage:

* **Python-First Architecture**: Build UI elements entirely in Python — no need to switch languages. Leverage Kivy’s `Widget` classes and layouts directly in your familiar codebase.
* **KV Language (Optional)**: Define UI layouts declaratively in `.kv` files. This separation helps keep layout logic clean and concise.
* **GPU-Accelerated Rendering**: Kivy uses OpenGL ES under the hood. Standard widgets, canvas instructions, and shaders automatically benefit from GPU acceleration without extra boilerplate.
* **Easy Packaging**: Use `buildozer` or `kivy-ios` / `kivy-android` to bundle your app for desktop, mobile, or embedded targets. Cross-platform support comes out of the box.
* **Rapid Iteration**: Hot-reload your `.kv` files or restart your Python script to see changes immediately. The Python REPL can even introspect and manipulate living widget trees.
* **Rich Widget Library**: Kivy provides a comprehensive set of controls (buttons, sliders, lists, charts via `kivy-garden`) that integrate seamlessly with the GPU pipeline.

> **Tip:** Start by defining your base layouts in a `.kv` file and then bind Python logic in your `App` subclass. This pattern keeps code organized and harnesses Kivy’s full power.

## 🚀 Phase Roll‑Out Plan

The following phased schedule focuses on wiring real data first, then gradually layering UX, safety, and theming.  Timings are ball‑park and assume two developers can pair on most items.

| Phase | Scope                                                                                                                                      | Rationale (Why Next)                                    | Owner / Helpers               | Rough ETA    |
| ----- | ------------------------------------------------------------------------------------------------------------------------------------------ | ------------------------------------------------------- | ----------------------------- | ------------ |
| 1     | **Wire the real data source**<br>• Point `/api/economy`, etc. at *analytics.sqlite* or log directory (`PLAYGROUND_LOG_DIR=/path/to/logs`). | Everybody wants live charts moving.                     | Back‑end crew (Node + Python) | **1–2 days** |
| 2     | **Toggleable Live‑Sync slider**<br>Heavy Settings → Network tab: Off / 1 s / 5 s / 30 s / 60 s.                                            | Lets admins ramp up slowly & avoids spam by default.    | React team                    | **1 day**    |
| 3     | **Security pass on REST endpoints**<br>• JWT or Steam‑ID header on `GET /api/*`, `POST /poke`.<br>• Rate‑limit POST (3 req/s/IP).          | Door must be locked before public demo.                 | Back‑end + DevOps             | **1 day**    |
| 4     | **Heat‑Map Mini component**<br>64×64 canvas dropped into Warden mini‑mode.                                                                 | Gives people something to look at while full map cooks. | Warden UI crew                | **2 days**   |
| 5     | **Crash‑wave logging glue**<br>Pipe multi‑module crash handler → `crashwave_*.log`; raise level‑3 alert.                                   | Makes Safe‑Mode loop testable.                          | Core playground team          | **2–3 days** |
| 6     | **Theme‑variables file & SCSS hot‑swap**<br>Expose `$primary‑color`, `$fatal‑flash‑color`, `$glow‑cycle‑speed`, etc.                       | Unblocks designers to start skin packs.                 | Front‑end infra               | **2 days**   |
| 7     | **Fun‑naming JSON bundle**<br>Ship `skins/fun/base/fun_names.json`; long‑press hot‑swap.                                                   | Low‑risk delight feature.                               | Front‑end + UX                | **1 day**    |
| 8     | **Warden full heat‑map**<br>Tile zoom, tooltip, 24 h scrubber.                                                                             | Big‑ticket visual once Phase 1‑4 live.                  | Warden + Data‑viz             | **4–5 days** |
| 9     | **Social chat MVP**<br>Dockable chat panel (local‑only, no cloud).                                                                         | Sets the stage for file buckets/collab.                 | Social tiger‑team             | —            |

> **Next Action:** Confirm or reorder phases, then spin up tickets for Phase 1.
