
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
from datetime import datetime

class EconomyChooserApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Xconomy Chooser v1.01")
        self.root.geometry("1000x600")

        self.data = {}
        self.current_file = None

        self.setup_widgets()

    def setup_widgets(self):
        menubar = tk.Menu(self.root)
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="Open JSON", command=self.load_json)
        file_menu.add_command(label="Save As...", command=self.save_json)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        menubar.add_cascade(label="File", menu=file_menu)
        self.root.config(menu=menubar)

        self.tree = ttk.Treeview(self.root)
        self.tree.heading("#0", text="Outpost > Trader > Item")
        self.tree.pack(side="left", fill="both", expand=True)

        self.detail_frame = tk.Frame(self.root, padx=10, pady=10)
        self.detail_frame.pack(side="right", fill="y")

        self.fields = {}
        for i, label in enumerate(["tradeable-code", "base-purchase-price", "base-sell-price", "required-famepoints", "can-be-purchased"]):
            tk.Label(self.detail_frame, text=label).grid(row=i, column=0, sticky="w")
            entry = tk.Entry(self.detail_frame, width=30)
            entry.grid(row=i, column=1)
            self.fields[label] = entry

        tk.Button(self.detail_frame, text="Update Item", command=self.update_item).grid(row=6, column=0, columnspan=2, pady=10)

        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)

    def load_json(self):
        filepath = filedialog.askopenfilename(filetypes=[("JSON files", "*.json")])
        if not filepath:
            return

        with open(filepath, "r") as file:
            try:
                self.data = json.load(file)
                self.current_file = filepath
                self.populate_tree()
            except json.JSONDecodeError:
                messagebox.showerror("Error", "Invalid JSON file.")

    def populate_tree(self):
        self.tree.delete(*self.tree.get_children())
        traders = self.data.get("economy-override", {}).get("traders", {})
        for trader_key, items in traders.items():
            outpost = trader_key.split("_")[0]
            trader = trader_key
            outpost_id = f"{outpost}"
            if not self.tree.exists(outpost_id):
                self.tree.insert("", "end", iid=outpost_id, text=outpost)
            trader_id = f"{trader}"
            self.tree.insert(outpost_id, "end", iid=trader_id, text=trader)
            for idx, item in enumerate(items):
                item_id = f"{trader}_{idx}"
                self.tree.insert(trader_id, "end", iid=item_id, text=item.get("tradeable-code", "Unnamed"))

    def on_tree_select(self, event):
        selected = self.tree.selection()
        if not selected:
            return
        item_id = selected[0]
        if "_" not in item_id:
            return
        parts = item_id.split("_")
        trader_key = "_".join(parts[:-1])
        idx = int(parts[-1])
        item = self.data["economy-override"]["traders"].get(trader_key, [])[idx]
        for key in self.fields:
            self.fields[key].delete(0, tk.END)
            self.fields[key].insert(0, item.get(key, ""))

    def update_item(self):
        selected = self.tree.selection()
        if not selected:
            return
        item_id = selected[0]
        parts = item_id.split("_")
        trader_key = "_".join(parts[:-1])
        idx = int(parts[-1])
        for key in self.fields:
            self.data["economy-override"]["traders"][trader_key][idx][key] = self.fields[key].get()
        messagebox.showinfo("Success", "Item updated.")

    def save_json(self):
        if not self.data:
            return
        save_path = filedialog.asksaveasfilename(defaultextension=".json", filetypes=[("JSON files", "*.json")])
        if not save_path:
            return
        with open(save_path, "w") as file:
            json.dump(self.data, file, indent=4)
        messagebox.showinfo("Saved", f"File saved to {save_path}")

if __name__ == "__main__":
    root = tk.Tk()
    app = EconomyChooserApp(root)
    root.mainloop()
