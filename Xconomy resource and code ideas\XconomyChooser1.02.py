
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
from datetime import datetime

CATEGORY_MAP = {
    'Cal_': 'A',
    'Magazine': 'B',
    'Weapon_': 'C',
    'Crafted': 'D',
    '_AP_CR': 'E'
}

class EconomyChooserApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Xconomy Chooser v1.02")
        self.root.geometry("1100x650")

        self.data = {}
        self.current_file = None
        self.category_filter = None

        self.setup_widgets()

    def setup_widgets(self):
        menubar = tk.Menu(self.root)
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="Open JSON", command=self.load_json)
        file_menu.add_command(label="Save As...", command=self.save_json)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        menubar.add_cascade(label="File", menu=file_menu)

        edit_menu = tk.Menu(menubar, tearoff=0)
        edit_menu.add_command(label="Apply Global % Change", command=self.global_price_edit)
        menubar.add_cascade(label="Edit", menu=edit_menu)

        category_menu = tk.Menu(menubar, tearoff=0)
        for label, cat in CATEGORY_MAP.items():
            category_menu.add_command(label=f"Filter: {label}", command=lambda l=label: self.set_category_filter(l))
        category_menu.add_separator()
        category_menu.add_command(label="Clear Filter", command=lambda: self.set_category_filter(None))
        menubar.add_cascade(label="Categories", menu=category_menu)

        self.root.config(menu=menubar)

        self.tree = ttk.Treeview(self.root)
        self.tree.heading("#0", text="Outpost > Trader > Item")
        self.tree.pack(side="left", fill="both", expand=True)

        self.detail_frame = tk.Frame(self.root, padx=10, pady=10)
        self.detail_frame.pack(side="right", fill="y")

        self.fields = {}
        for i, label in enumerate(["tradeable-code", "base-purchase-price", "base-sell-price", "required-famepoints", "can-be-purchased"]):
            tk.Label(self.detail_frame, text=label).grid(row=i, column=0, sticky="w")
            entry = tk.Entry(self.detail_frame, width=30)
            entry.grid(row=i, column=1)
            self.fields[label] = entry

        tk.Button(self.detail_frame, text="Update Item", command=self.update_item).grid(row=6, column=0, columnspan=2, pady=10)

        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)

    def load_json(self):
        filepath = filedialog.askopenfilename(filetypes=[("JSON files", "*.json")])
        if not filepath:
            return
        with open(filepath, "r") as file:
            try:
                self.data = json.load(file)
                self.current_file = filepath
                self.populate_tree()
            except json.JSONDecodeError:
                messagebox.showerror("Error", "Invalid JSON file.")

    def populate_tree(self):
        self.tree.delete(*self.tree.get_children())
        traders = self.data.get("economy-override", {}).get("traders", {})
        for trader_key, items in traders.items():
            outpost = trader_key.split("_")[0]
            trader = trader_key
            outpost_id = f"{outpost}"
            if not self.tree.exists(outpost_id):
                self.tree.insert("", "end", iid=outpost_id, text=outpost)
            trader_id = f"{trader}"
            self.tree.insert(outpost_id, "end", iid=trader_id, text=trader)
            for idx, item in enumerate(items):
                code = item.get("tradeable-code", "Unnamed")
                if self.category_filter:
                    if self.category_filter == 'Weapon_':
                        if not (code.startswith('Weapon_') and "Weapon_parts" not in code):
                            continue
                    elif self.category_filter not in code:
                        continue
                item_id = f"{trader}_{idx}"
                self.tree.insert(trader_id, "end", iid=item_id, text=code)

    def on_tree_select(self, event):
        selected = self.tree.selection()
        if not selected:
            return
        item_id = selected[0]
        if "_" not in item_id:
            return
        parts = item_id.split("_")
        trader_key = "_".join(parts[:-1])
        idx = int(parts[-1])
        item = self.data["economy-override"]["traders"].get(trader_key, [])[idx]
        for key in self.fields:
            self.fields[key].delete(0, tk.END)
            self.fields[key].insert(0, item.get(key, ""))

    def update_item(self):
        selected = self.tree.selection()
        if not selected:
            return
        item_id = selected[0]
        parts = item_id.split("_")
        trader_key = "_".join(parts[:-1])
        idx = int(parts[-1])
        for key in self.fields:
            self.data["economy-override"]["traders"][trader_key][idx][key] = self.fields[key].get()
        messagebox.showinfo("Success", "Item updated.")

    def save_json(self):
        if not self.data:
            return
        save_path = filedialog.asksaveasfilename(defaultextension=".json", filetypes=[("JSON files", "*.json")])
        if not save_path:
            return
        with open(save_path, "w") as file:
            json.dump(self.data, file, indent=4)
        messagebox.showinfo("Saved", f"File saved to {save_path}")

    def set_category_filter(self, category):
        self.category_filter = category
        self.populate_tree()

    def global_price_edit(self):
        if not self.data:
            return
        win = tk.Toplevel(self.root)
        win.title("Global % Price Editor")

        tk.Label(win, text="Field to Edit:").grid(row=0, column=0, sticky="e")
        field_var = tk.StringVar(value="base-purchase-price")
        ttk.Combobox(win, textvariable=field_var, values=["base-purchase-price", "base-sell-price"]).grid(row=0, column=1)

        tk.Label(win, text="Percentage Change (-99 to 100):").grid(row=1, column=0, sticky="e")
        percent_entry = tk.Entry(win)
        percent_entry.grid(row=1, column=1)

        def apply():
            try:
                percent = int(percent_entry.get())
                if not (-99 <= percent <= 100):
                    raise ValueError
            except ValueError:
                messagebox.showerror("Invalid", "Enter a number from -99 to 100.")
                return

            field = field_var.get()
            traders = self.data.get("economy-override", {}).get("traders", {})
            for trader_items in traders.values():
                for item in trader_items:
                    try:
                        val = float(item.get(field, "-1"))
                        if val in [-1, None]:
                            continue
                        new_val = val + (val * percent / 100)
                        item[field] = str(int(round(new_val)))
                    except Exception:
                        continue
            self.populate_tree()
            messagebox.showinfo("Done", f"{field} updated globally by {percent}%")
            win.destroy()

        tk.Button(win, text="Apply", command=apply).grid(row=2, column=0, columnspan=2, pady=10)

if __name__ == "__main__":
    root = tk.Tk()
    app = EconomyChooserApp(root)
    root.mainloop()
