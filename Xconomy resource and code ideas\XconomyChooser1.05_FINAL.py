
# Final script will:
# - <PERSON>an all tradeable-code prefixes/suffixes/infixes
# - Create a dynamic category menu
# - On category selection, open a modal:
#     * Show number of matched items
#     * Allow toggle of can-be-purchased
#     * Allow famepoint batch setting
# - Confirm before applying changes
# - Support case-insensitive category match
# - Stub for gold price editing (future)
# - Compatible with future .g or default state inputs
# - Supports undo model in next phase
