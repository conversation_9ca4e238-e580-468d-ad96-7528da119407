
# This file will include:
# - Dynamic category scanning (prefix/infix/suffix) from all tradeable-codes
# - Category selection menu dynamically built from live data
# - A new modal window for batch editing:
#     - can-be-purchased (true / false / default)
#     - required-famepoints
# - Preview count of items in category
# - Safety confirmation before applying edits
# - Gold price support stub for future toggle

# Building final version. Will auto-launch full GUI after this stage.
