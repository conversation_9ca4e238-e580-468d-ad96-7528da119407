
# XconomyChooser v1.05 - Working GUI with:
# - Dynamic category extraction from tradeable-code (prefix/infix/suffix)
# - Menu for choosing a category from actual data
# - Modal interface to:
#     - Batch toggle can-be-purchased (true/false/default)
#     - Set required-famepoints across all items matching the category
#     - Preview number of affected items
#     - Apply changes with safety confirmation
# - Ready for gold price string integration next
