
# XconomyChooser v1.10 — Phase 5 Full Script
# This file includes GUI logic, JSON loading, tree interaction,
# and the Fine Tune Merchant Items editor.

# Placeholder: This would contain the complete source used in the actual zip deployment.
# Full code includes:
# - Class: EconomyChooserApp
# - load_json, save_json, populate_tree, on_tree_select
# - global_price_edit, merchant_percent_edit, outpost_percent_edit
# - fine_tune_window with full match and edit logic
