
import subprocess

    def launch_cli_window(self):
        if self.current_file:
        args = ["python", "economychooser1_52a_hybridmode.py", self.current_file]
            else:
            args = ["python", "economychooser1_52a_hybridmode.py"]
            subprocess.Popen(args, creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)




            import os
            import time
            from tkinter import simpledialog

                def monitor_file_changes(app, filepath):
                    if not filepath:
                return
                last_modified = os.path.getmtime(filepath)

                    def check_reload():
                    nonlocal last_modified
                        try:
                        current_modified = os.path.getmtime(filepath)
                            if current_modified != last_modified:
                            answer = messagebox.askyesno("Reload Detected",
                            f"The file '{os.path.basename(filepath)}' was modified externally.\n\nReload from disk?")
                                if answer:
                                app.load_json_from_path(filepath)
                                last_modified = current_modified
                                    except Exception:
                                pass
                                app.root.after(3000, check_reload)

                                app.root.after(3000, check_reload)

                                # Patch into EconomyChooserApp.__init__ to auto-launch if current_file exists

                                import tkinter as tk
                                from tkinter import ttk, filedialog, messagebox
                                import json
                                from collections import Counter, defaultdict

                                CATEGORY_LIMIT = 40  # Show only the top N categories in the menu

                                    class EconomyChooserApp:
                                        def __init__(self, root):
                                        self.root = root
                                        self.load_config()
                                        self.root.title("Xconomy Chooser v1.05")
                                        #  self.monitor_file_changes()
                                        self.root.geometry("1500x780")
                                        self.data = {}
                                        self.current_file = None
                                        self.category_filter = None
                                        self.exclude_crafted = tk.BooleanVar(value=False)
                                        self.exclude_improvised = tk.BooleanVar(value=False)
                                        self.dark_mode = False
                                        self.categories = []
                                        self.category_menu_entries = []
                                        self.flat_style = False  # for future
                                        self.setup_widgets()
                                        monitor_file_changes(self, self.current_file)


                                            def convert_to_flat_style(self):
                                                if not self.data or "economy-override" not in self.data:
                                                messagebox.showerror("Error", "No economy data loaded.")
                                            return
                                                try:
                                                nested = self.data["economy-override"]["traders"]
                                                flat = {}
                                                    for key, items in nested.items():
                                                    flat[key] = items
                                                    self.data["economy-override"]["traders"] = flat
                                                    self.flat_style = True
                                                    self.populate_tree()
                                                    messagebox.showinfo("Flat Style", "Converted to flat style format.")
                                                        except Exception as e:
                                                        messagebox.showerror("Conversion Error", str(e))



                                                            def spread_edit(self):
                                                                if not self.data:
                                                            return
                                                            win = tk.Toplevel(self.root)
                                                            win.title("Spread Edit")
                                                            win.geometry("500x300")

                                                            tk.Label(win, text="Search tradeable-code:").pack()
                                                            code_var = tk.StringVar()
                                                            code_entry = tk.Entry(win, textvariable=code_var, width=40)
                                                            code_entry.pack(pady=5)

                                                            tk.Label(win, text="Field to Edit:").pack()
                                                            field_var = tk.StringVar(value="base-purchase-price")
                                                            ttk.Combobox(win, textvariable=field_var, values=[
                                                            "base-purchase-price", "base-sell-price", "can-be-purchased"]).pack(pady=5)

                                                            tk.Label(win, text="New Value:").pack()
                                                            value_var = tk.StringVar()
                                                            value_entry = tk.Entry(win, textvariable=value_var, width=30)
                                                            value_entry.pack(pady=5)

                                                                def apply_spread():
                                                                code = code_var.get().strip()
                                                                field = field_var.get()
                                                                value = value_var.get().strip()
                                                                    if not code or not field or not value:
                                                                    messagebox.showerror("Input Error", "All fields must be filled.")
                                                                return
                                                                changed = 0
                                                                    for trader, items in self.data["economy-override"]["traders"].items():
                                                                        for item in items:
                                                                            if item.get("tradeable-code", "").lower() == code.lower():
                                                                            item[field] = value
                                                                            changed += 1
                                                                            self.populate_tree()
                                                                            messagebox.showinfo("Spread Edit", f"{changed} items updated.")
                                                                            win.destroy()

                                                                            tk.Button(win, text="Apply Spread", command=apply_spread).pack(pady=10)



                                                                                def merchant_level_edit(self):
                                                                                    if not self.data:
                                                                                return
                                                                                win = tk.Toplevel(self.root)
                                                                                win.title("Merchant-Level % Edit")
                                                                                win.geometry("480x200")

                                                                                tk.Label(win, text="Select Merchant:").grid(row=0, column=0, sticky="e")
                                                                                merchant_var = tk.StringVar(value="A_0_Armory")
                                                                                trader_keys = sorted(self.data["economy-override"]["traders"].keys())
                                                                                ttk.Combobox(win, textvariable=merchant_var, values=trader_keys).grid(row=0, column=1)

                                                                                tk.Label(win, text="Field to Edit:").grid(row=1, column=0, sticky="e")
                                                                                field_var = tk.StringVar(value="base-purchase-price")
                                                                                ttk.Combobox(win, textvariable=field_var, values=["base-purchase-price", "base-sell-price"]).grid(row=1, column=1)

                                                                                tk.Label(win, text="Percentage Change (-99 to 100):").grid(row=2, column=0, sticky="e")
                                                                                percent_entry = tk.Entry(win)
                                                                                percent_entry.grid(row=2, column=1)

                                                                                    def apply():
                                                                                        try:
                                                                                        percent = int(percent_entry.get())
                                                                                            if not (-99 <= percent <= 100):
                                                                                        raise ValueError
                                                                                            except ValueError:
                                                                                            messagebox.showerror("Invalid", "Enter a number from -99 to 100.")
                                                                                        return
                                                                                        field = field_var.get()
                                                                                        trader_key = merchant_var.get()
                                                                                        items = self.data["economy-override"]["traders"].get(trader_key, [])
                                                                                            for item in items:
                                                                                                try:
                                                                                                val = self.parse_price_value(item.get(field, "-1"))
                                                                                                    if val in [-1, None]:
                                                                                                continue
                                                                                                new_val = val + (val * percent / 100)
                                                                                                item[field] = str(int(round(new_val)))
                                                                                                    except Exception:
                                                                                                continue
                                                                                                self.populate_tree()
                                                                                                messagebox.showinfo("Done", f"{field} updated for {trader_key} by {percent}%")
                                                                                                win.destroy()

                                                                                                tk.Button(win, text="Apply", command=apply).grid(row=3, column=0, columnspan=2, pady=10)



                                                                                                    def outpost_wide_edit(self):
                                                                                                        if not self.data:
                                                                                                    return
                                                                                                    win = tk.Toplevel(self.root)
                                                                                                    win.title("Outpost-Wide % Edit")
                                                                                                    win.geometry("480x200")

                                                                                                    tk.Label(win, text="Select Outpost (prefix):").grid(row=0, column=0, sticky="e")
                                                                                                    outpost_var = tk.StringVar(value="A_0")
                                                                                                    outposts = sorted({k.split("_")[0] for k in self.data["economy-override"]["traders"]})
                                                                                                    ttk.Combobox(win, textvariable=outpost_var, values=outposts).grid(row=0, column=1)

                                                                                                    tk.Label(win, text="Field to Edit:").grid(row=1, column=0, sticky="e")
                                                                                                    field_var = tk.StringVar(value="base-purchase-price")
                                                                                                    ttk.Combobox(win, textvariable=field_var, values=["base-purchase-price", "base-sell-price"]).grid(row=1, column=1)

                                                                                                    tk.Label(win, text="Percentage Change (-99 to 100):").grid(row=2, column=0, sticky="e")
                                                                                                    percent_entry = tk.Entry(win)
                                                                                                    percent_entry.grid(row=2, column=1)

                                                                                                        def apply():
                                                                                                            try:
                                                                                                            percent = int(percent_entry.get())
                                                                                                                if not (-99 <= percent <= 100):
                                                                                                            raise ValueError
                                                                                                                except ValueError:
                                                                                                                messagebox.showerror("Invalid", "Enter a number from -99 to 100.")
                                                                                                            return
                                                                                                            field = field_var.get()
                                                                                                            target_prefix = outpost_var.get()
                                                                                                                for trader_key, items in self.data["economy-override"]["traders"].items():
                                                                                                                    if trader_key.startswith(target_prefix):
                                                                                                                        for item in items:
                                                                                                                            try:
                                                                                                                            val = self.parse_price_value(item.get(field, "-1"))
                                                                                                                                if val in [-1, None]:
                                                                                                                            continue
                                                                                                                            new_val = val + (val * percent / 100)
                                                                                                                            item[field] = str(int(round(new_val)))
                                                                                                                                except Exception:
                                                                                                                            continue
                                                                                                                            self.populate_tree()
                                                                                                                            messagebox.showinfo("Done", f"{field} updated for outpost {target_prefix} by {percent}%")
                                                                                                                            win.destroy()

                                                                                                                            tk.Button(win, text="Apply", command=apply).grid(row=3, column=0, columnspan=2, pady=10)



                                                                                                                                def fine_tune_item(self):
                                                                                                                                    if not self.data:
                                                                                                                                return
                                                                                                                                win = tk.Toplevel(self.root)
                                                                                                                                win.title("Fine-Tune Item")
                                                                                                                                win.geometry("520x300")

                                                                                                                                tk.Label(win, text="Search tradeable-code:").pack()
                                                                                                                                code_var = tk.StringVar()
                                                                                                                                tk.Entry(win, textvariable=code_var, width=40).pack(pady=5)

                                                                                                                                preview = tk.Text(win, height=8, width=60)
                                                                                                                                preview.pack()

                                                                                                                                    def search():
                                                                                                                                    preview.delete("1.0", tk.END)
                                                                                                                                    code = code_var.get().strip().lower()
                                                                                                                                    found = False
                                                                                                                                        for trader, items in self.data["economy-override"]["traders"].items():
                                                                                                                                            for item in items:
                                                                                                                                                if item.get("tradeable-code", "").lower() == code:
                                                                                                                                                preview.insert(tk.END, json.dumps(item, indent=2))
                                                                                                                                                found = True
                                                                                                                                            break
                                                                                                                                                if found:
                                                                                                                                            break
                                                                                                                                                if not found:
                                                                                                                                                preview.insert(tk.END, "Item not found.")

                                                                                                                                                    def apply_changes():
                                                                                                                                                    selected = tree.selection()
                                                                                                                                                    target_items = []
                                                                                                                                                        for cat, (cid, codes) in self._batch_cat_nodes.items():
                                                                                                                                                            if cid in selected:
                                                                                                                                                            target_items.extend(codes)

                                                                                                                                                                if not target_items:
                                                                                                                                                                messagebox.showinfo("No Selection", "No categories selected.")
                                                                                                                                                            return

                                                                                                                                                                if not self.is_dev_mode():
                                                                                                                                                                confirm = messagebox.askyesno('Confirm', f'Apply changes to {len(target_items)} items?')
                                                                                                                                                                    if not confirm:
                                                                                                                                                                return
                                                                                                                                                                    if not confirm:
                                                                                                                                                                return

                                                                                                                                                                # Ask what to do
                                                                                                                                                                field = simpledialog.askstring("Field", "Enter field to change (e.g., can-be-purchased, fame-points, base-purchase-price):")
                                                                                                                                                                    if not field:
                                                                                                                                                                return

                                                                                                                                                                mode = simpledialog.askstring("Mode", "Enter mode (set, %increase, %decrease):")
                                                                                                                                                                    if not mode:
                                                                                                                                                                return

                                                                                                                                                                value = simpledialog.askstring("Value", "Enter value (e.g., true, false, 100, -, etc.):")
                                                                                                                                                                    if value is None:
                                                                                                                                                                return

                                                                                                                                                                changed = 0
                                                                                                                                                                    for trader_items in self.data.get("economy-override", {}).get("traders", {}).values():
                                                                                                                                                                        for item in trader_items:
                                                                                                                                                                        code = item.get("tradeable-code", "")
                                                                                                                                                                            if code in target_items:
                                                                                                                                                                                try:
                                                                                                                                                                                    if mode == "%increase" and field in item and isinstance(item[field], (int, float)):
                                                                                                                                                                                    item[field] = round(item[field] * (1 + float(value)/100), 2)
                                                                                                                                                                                        elif mode == "%decrease" and field in item and isinstance(item[field], (int, float)):
                                                                                                                                                                                        item[field] = round(item[field] * (1 - float(value)/100), 2)
                                                                                                                                                                                            elif mode == "set":
                                                                                                                                                                                                if value == "-":
                                                                                                                                                                                                item[field] = -1
                                                                                                                                                                                                    elif value.lower() in ("true", "false"):
                                                                                                                                                                                                    item[field] = value.lower() == "true"
                                                                                                                                                                                                        else:
                                                                                                                                                                                                            try:
                                                                                                                                                                                                            item[field] = int(value)
                                                                                                                                                                                                                except:
                                                                                                                                                                                                                item[field] = value
                                                                                                                                                                                                                changed += 1
                                                                                                                                                                                                                    except:
                                                                                                                                                                                                                pass

                                                                                                                                                                                                                messagebox.showinfo("Complete", f"Updated {changed} entries with {field} using mode '{mode}'")
                                                                                                                                                                                                                top.destroy()


                                                                                                                                                                                                                    def build_custom_category(self):
                                                                                                                                                                                                                    import re
                                                                                                                                                                                                                    builder = tk.Toplevel(self.root)
                                                                                                                                                                                                                    builder.title("Build Custom Category")
                                                                                                                                                                                                                    builder.geometry("420x250")

                                                                                                                                                                                                                    tk.Label(builder, text="Category Name:").pack(pady=5)
                                                                                                                                                                                                                    name_entry = tk.Entry(builder, width=40)
                                                                                                                                                                                                                    name_entry.pack()

                                                                                                                                                                                                                    tk.Label(builder, text="Match Rule:").pack(pady=5)
                                                                                                                                                                                                                    rule_entry = tk.Entry(builder, width=40)
                                                                                                                                                                                                                    rule_entry.pack()

                                                                                                                                                                                                                    match_type = tk.StringVar(value="contains")
                                                                                                                                                                                                                    modes = ["contains", "startswith", "regex"]
                                                                                                                                                                                                                        for m in modes:
                                                                                                                                                                                                                        tk.Radiobutton(builder, text=m.title(), variable=match_type, value=m).pack(anchor="w")

                                                                                                                                                                                                                        result_label = tk.Label(builder, text="Matching items: ?")
                                                                                                                                                                                                                        result_label.pack(pady=5)


                                                                                                                                                                                                                        preview_list = tk.Listbox(builder, height=5, width=45)
                                                                                                                                                                                                                        preview_list.pack(pady=5)

                                                                                                                                                                                                                            def preview_match():
                                                                                                                                                                                                                            total = 0
                                                                                                                                                                                                                            all_codes = []
                                                                                                                                                                                                                                for trader_items in self.data.get("economy-override", {}).get("traders", {}).values():
                                                                                                                                                                                                                                    for item in trader_items:
                                                                                                                                                                                                                                    code = item.get("tradeable-code", "")
                                                                                                                                                                                                                                    all_codes.append(code)

                                                                                                                                                                                                                                    pattern = rule_entry.get().strip().lower()
                                                                                                                                                                                                                                    mode = match_type.get()

                                                                                                                                                                                                                                        if not pattern:
                                                                                                                                                                                                                                        result_label.config(text="Please enter a pattern.")
                                                                                                                                                                                                                                    return

                                                                                                                                                                                                                                    matches = []
                                                                                                                                                                                                                                        try:
                                                                                                                                                                                                                                            for code in all_codes:
                                                                                                                                                                                                                                            lc = code.lower()
                                                                                                                                                                                                                                                if mode == "contains" and pattern in lc:
                                                                                                                                                                                                                                                matches.append(code)
                                                                                                                                                                                                                                                    elif mode == "startswith" and lc.startswith(pattern):
                                                                                                                                                                                                                                                    matches.append(code)
                                                                                                                                                                                                                                                        elif mode == "regex" and re.search(pattern, lc):
                                                                                                                                                                                                                                                        matches.append(code)
                                                                                                                                                                                                                                                            except Exception as e:
                                                                                                                                                                                                                                                            result_label.config(text=f"Error: {e}")
                                                                                                                                                                                                                                                        return

                                                                                                                                                                                                                                                        result_label.config(text=f"Matching items: {len(matches)}")
                                                                                                                                                                                                                                                        preview_list.delete(0, tk.END)
                                                                                                                                                                                                                                                            for m in matches[:20]:
                                                                                                                                                                                                                                                            preview_list.insert(tk.END, m + (" ..." if len(matches) > 20 else ""))


                                                                                                                                                                                                                                                                def save_category():
                                                                                                                                                                                                                                                                cat = name_entry.get().strip()
                                                                                                                                                                                                                                                                    if not cat:
                                                                                                                                                                                                                                                                    messagebox.showerror("No name", "Enter a category name.")
                                                                                                                                                                                                                                                                return
                                                                                                                                                                                                                                                                rule = rule_entry.get().strip().lower()
                                                                                                                                                                                                                                                                    if not rule:
                                                                                                                                                                                                                                                                    messagebox.showerror("No rule", "Enter a match rule.")
                                                                                                                                                                                                                                                                return
                                                                                                                                                                                                                                                                mode = match_type.get()

                                                                                                                                                                                                                                                                matches = []
                                                                                                                                                                                                                                                                    for trader_items in self.data.get("economy-override", {}).get("traders", {}).values():
                                                                                                                                                                                                                                                                        for item in trader_items:
                                                                                                                                                                                                                                                                        code = item.get("tradeable-code", "")
                                                                                                                                                                                                                                                                        lc = code.lower()
                                                                                                                                                                                                                                                                            if mode == "contains" and rule in lc:
                                                                                                                                                                                                                                                                            matches.append(code)
                                                                                                                                                                                                                                                                                elif mode == "startswith" and lc.startswith(rule):
                                                                                                                                                                                                                                                                                matches.append(code)
                                                                                                                                                                                                                                                                                    elif mode == "regex":
                                                                                                                                                                                                                                                                                        try:
                                                                                                                                                                                                                                                                                            if re.search(rule, lc):
                                                                                                                                                                                                                                                                                            matches.append(code)
                                                                                                                                                                                                                                                                                                except:
                                                                                                                                                                                                                                                                                            pass

                                                                                                                                                                                                                                                                                                if not hasattr(self, "_batch_cat_map"):
                                                                                                                                                                                                                                                                                                self._batch_cat_map = {}
                                                                                                                                                                                                                                                                                                self._batch_cat_map[cat] = matches
                                                                                                                                                                                                                                                                                                messagebox.showinfo("Saved", f"{cat} category created with {len(matches)} items.")
                                                                                                                                                                                                                                                                                                builder.destroy()

                                                                                                                                                                                                                                                                                                tk.Button(builder, text="Preview Matches", command=preview_match).pack(pady=5)
                                                                                                                                                                                                                                                                                                tk.Button(builder, text="Save Category", command=save_category).pack(pady=5)



                                                                                                                                                                                                                                                                                                    def save_category_preset(self):
                                                                                                                                                                                                                                                                                                        if not hasattr(self, '_batch_cat_map') or not self._batch_cat_map:
                                                                                                                                                                                                                                                                                                        messagebox.showerror("Error", "No categories to save.")
                                                                                                                                                                                                                                                                                                    return
                                                                                                                                                                                                                                                                                                    preset_name = simpledialog.askstring("Preset Name", "Enter a name for this preset:")
                                                                                                                                                                                                                                                                                                        if not preset_name:
                                                                                                                                                                                                                                                                                                    return
                                                                                                                                                                                                                                                                                                    os.makedirs("presets", exist_ok=True)
                                                                                                                                                                                                                                                                                                    filename = f"presets/{preset_name}.json"
                                                                                                                                                                                                                                                                                                        try:
                                                                                                                                                                                                                                                                                                            with open(filename, "w", encoding="utf-8") as f:
                                                                                                                                                                                                                                                                                                            json.dump(self._batch_cat_map, f, indent=2)
                                                                                                                                                                                                                                                                                                            messagebox.showinfo("Saved", f"Preset saved as '{preset_name}'.")
                                                                                                                                                                                                                                                                                                                except Exception as e:
                                                                                                                                                                                                                                                                                                                messagebox.showerror("Error", str(e))

                                                                                                                                                                                                                                                                                                                    def load_category_preset(self, preset_file=None):
                                                                                                                                                                                                                                                                                                                        if not preset_file:
                                                                                                                                                                                                                                                                                                                        preset_file = filedialog.askopenfilename(initialdir="presets", filetypes=[("JSON Files", "*.json")])
                                                                                                                                                                                                                                                                                                                            if not preset_file:
                                                                                                                                                                                                                                                                                                                        return
                                                                                                                                                                                                                                                                                                                            try:
                                                                                                                                                                                                                                                                                                                                with open(preset_file, "r", encoding="utf-8") as f:
                                                                                                                                                                                                                                                                                                                                custom_map = json.load(f)
                                                                                                                                                                                                                                                                                                                                    if not isinstance(custom_map, dict):
                                                                                                                                                                                                                                                                                                                                raise ValueError("Invalid format.")
                                                                                                                                                                                                                                                                                                                                self._batch_cat_map = custom_map
                                                                                                                                                                                                                                                                                                                                messagebox.showinfo("Loaded", f"Preset loaded from {preset_file}")
                                                                                                                                                                                                                                                                                                                                    except Exception as e:
                                                                                                                                                                                                                                                                                                                                    messagebox.showerror("Error", str(e))

                                                                                                                                                                                                                                                                                                                                        def populate_preset_menu(self, menu):
                                                                                                                                                                                                                                                                                                                                        menu.delete(0, "end")
                                                                                                                                                                                                                                                                                                                                            try:
                                                                                                                                                                                                                                                                                                                                            os.makedirs("presets", exist_ok=True)
                                                                                                                                                                                                                                                                                                                                                for fname in os.listdir("presets"):
                                                                                                                                                                                                                                                                                                                                                    if fname.endswith(".json"):
                                                                                                                                                                                                                                                                                                                                                    menu.add_command(label=fname[:-5], command=lambda f=fname: self.load_category_preset(f"presets/{f}"))
                                                                                                                                                                                                                                                                                                                                                        except Exception as e:
                                                                                                                                                                                                                                                                                                                                                        messagebox.showerror("Error", f"Could not load presets: {e}")



                                                                                                                                                                                                                                                                                                                                                            def add_tooltip(self, widget, text):
                                                                                                                                                                                                                                                                                                                                                            if self.is_dev_mode(): return
                                                                                                                                                                                                                                                                                                                                                                def on_enter(event):
                                                                                                                                                                                                                                                                                                                                                                self._tooltip = tk.Toplevel(widget)
                                                                                                                                                                                                                                                                                                                                                                self._tooltip.wm_overrideredirect(True)
                                                                                                                                                                                                                                                                                                                                                                x, y = widget.winfo_pointerxy()
                                                                                                                                                                                                                                                                                                                                                                self._tooltip.wm_geometry(f"+{x + 10}+{y + 10}")
                                                                                                                                                                                                                                                                                                                                                                label = tk.Label(self._tooltip, text=text, background="lightyellow", relief="solid", borderwidth=1, font=("Arial", 9))
                                                                                                                                                                                                                                                                                                                                                                label.pack()
                                                                                                                                                                                                                                                                                                                                                                    def on_leave(event):
                                                                                                                                                                                                                                                                                                                                                                        if hasattr(self, '_tooltip'):
                                                                                                                                                                                                                                                                                                                                                                        self._tooltip.destroy()
                                                                                                                                                                                                                                                                                                                                                                        self._tooltip = None
                                                                                                                                                                                                                                                                                                                                                                        widget.bind("<Enter>", on_enter)
                                                                                                                                                                                                                                                                                                                                                                        widget.bind("<Leave>", on_leave)



                                                                                                                                                                                                                                                                                                                                                                            def load_config(self):
                                                                                                                                                                                                                                                                                                                                                                            self.config_data = {"dev_mode": False, "default_preset": "", "night_mode": False}
                                                                                                                                                                                                                                                                                                                                                                                try:
                                                                                                                                                                                                                                                                                                                                                                                    with open("config.json", "r", encoding="utf-8") as f:
                                                                                                                                                                                                                                                                                                                                                                                    self.config_data.update(json.load(f))
                                                                                                                                                                                                                                                                                                                                                                                        except FileNotFoundError:
                                                                                                                                                                                                                                                                                                                                                                                            with open("config.json", "w", encoding="utf-8") as f:
                                                                                                                                                                                                                                                                                                                                                                                            json.dump(self.config_data, f, indent=2)

                                                                                                                                                                                                                                                                                                                                                                                                def is_dev_mode(self):
                                                                                                                                                                                                                                                                                                                                                                                            return self.config_data.get("dev_mode", False)



                                                                                                                                                                                                                                                                                                                                                                                                def open_config_editor(self):
                                                                                                                                                                                                                                                                                                                                                                                                win = tk.Toplevel(self.root)
                                                                                                                                                                                                                                                                                                                                                                                                win.title("App Configuration")
                                                                                                                                                                                                                                                                                                                                                                                                win.geometry("300x200")

                                                                                                                                                                                                                                                                                                                                                                                                dev_var = tk.BooleanVar(value=self.config_data.get("dev_mode", False))
                                                                                                                                                                                                                                                                                                                                                                                                night_var = tk.BooleanVar(value=self.config_data.get("night_mode", False))

                                                                                                                                                                                                                                                                                                                                                                                                tk.Checkbutton(win, text="Developer Mode (Disables confirmations/tooltips)", variable=dev_var).pack(anchor="w", padx=10, pady=10)
                                                                                                                                                                                                                                                                                                                                                                                                tk.Checkbutton(win, text="Night Mode (WIP)", variable=night_var).pack(anchor="w", padx=10, pady=5)

                                                                                                                                                                                                                                                                                                                                                                                                    def save_and_close():
                                                                                                                                                                                                                                                                                                                                                                                                    self.config_data["dev_mode"] = dev_var.get()
                                                                                                                                                                                                                                                                                                                                                                                                    self.config_data["night_mode"] = night_var.get()
                                                                                                                                                                                                                                                                                                                                                                                                        try:
                                                                                                                                                                                                                                                                                                                                                                                                            with open("config.json", "w", encoding="utf-8") as f:
                                                                                                                                                                                                                                                                                                                                                                                                            json.dump(self.config_data, f, indent=2)
                                                                                                                                                                                                                                                                                                                                                                                                            messagebox.showinfo("Saved", "Configuration updated.")
                                                                                                                                                                                                                                                                                                                                                                                                            win.destroy()
                                                                                                                                                                                                                                                                                                                                                                                                                except Exception as e:
                                                                                                                                                                                                                                                                                                                                                                                                                messagebox.showerror("Error", str(e))

                                                                                                                                                                                                                                                                                                                                                                                                                tk.Button(win, text="Save", command=save_and_close).pack(pady=10)



                                                                                                                                                                                                                                                                                                                                                                                                                    def apply_theme(self, style):
                                                                                                                                                                                                                                                                                                                                                                                                                        if self.config_data.get("night_mode", False):
                                                                                                                                                                                                                                                                                                                                                                                                                        style.theme_use("default")
                                                                                                                                                                                                                                                                                                                                                                                                                        style.configure(".", background="#1e1e1e", foreground="white")
                                                                                                                                                                                                                                                                                                                                                                                                                        style.configure("TLabel", background="#1e1e1e", foreground="white")
                                                                                                                                                                                                                                                                                                                                                                                                                        style.configure("TFrame", background="#1e1e1e")
                                                                                                                                                                                                                                                                                                                                                                                                                        style.configure("TCheckbutton", background="#1e1e1e", foreground="white")
                                                                                                                                                                                                                                                                                                                                                                                                                        style.configure("Treeview", background="#2e2e2e", foreground="white", fieldbackground="#2e2e2e")
                                                                                                                                                                                                                                                                                                                                                                                                                        style.map("Treeview", background=[("selected", "#444444")])
                                                                                                                                                                                                                                                                                                                                                                                                                        self.root.configure(bg="#1e1e1e")
                                                                                                                                                                                                                                                                                                                                                                                                                            else:
                                                                                                                                                                                                                                                                                                                                                                                                                            style.theme_use("default")
                                                                                                                                                                                                                                                                                                                                                                                                                            style.configure(".", background="SystemButtonFace", foreground="black")
                                                                                                                                                                                                                                                                                                                                                                                                                            style.configure("TLabel", background="SystemButtonFace", foreground="black")
                                                                                                                                                                                                                                                                                                                                                                                                                            style.configure("TFrame", background="SystemButtonFace")
                                                                                                                                                                                                                                                                                                                                                                                                                            style.configure("TCheckbutton", background="SystemButtonFace", foreground="black")
                                                                                                                                                                                                                                                                                                                                                                                                                            style.configure("Treeview", background="white", foreground="black", fieldbackground="white")
                                                                                                                                                                                                                                                                                                                                                                                                                            self.root.configure(bg="SystemButtonFace")



                                                                                                                                                                                                                                                                                                                                                                                                                                def show_about(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                msg = (
                                                                                                                                                                                                                                                                                                                                                                                                                                "Xconomy Chooser"
                                                                                                                                                                                                                                                                                                                                                                                                                                "Version: v1.71k_p"
                                                                                                                                                                                                                                                                                                                                                                                                                                "Author: V1nceTD"
                                                                                                                                                                                                                                                                                                                                                                                                                                "Powered by Python + Tkinter\n\n"
                                                                                                                                                                                                                                                                                                                                                                                                                                "Custom economy editor for SCUM servers.\n"
                                                                                                                                                                                                                                                                                                                                                                                                                                "Featuring: GUI + CLI hybrid, Smart Category Logic (F.I.S.H),\n"
                                                                                                                                                                                                                                                                                                                                                                                                                                "Batch Edits, Presets, Night Mode, and more."
                                                                                                                                                                                                                                                                                                                                                                                                                                )
                                                                                                                                                                                                                                                                                                                                                                                                                                messagebox.showinfo("About Xconomy Chooser", msg)



                                                                                                                                                                                                                                                                                                                                                                                                                                    def log_audit(self, message):
                                                                                                                                                                                                                                                                                                                                                                                                                                    import datetime
                                                                                                                                                                                                                                                                                                                                                                                                                                    timestamp = datetime.datetime.now().strftime("[%Y-%m-%d %H:%M:%S]")
                                                                                                                                                                                                                                                                                                                                                                                                                                    log_line = f"{timestamp} {message}\n"
                                                                                                                                                                                                                                                                                                                                                                                                                                        try:
                                                                                                                                                                                                                                                                                                                                                                                                                                            with open("audit_log.txt", "a", encoding="utf-8") as f:
                                                                                                                                                                                                                                                                                                                                                                                                                                            f.write(log_line)
                                                                                                                                                                                                                                                                                                                                                                                                                                                except Exception as e:
                                                                                                                                                                                                                                                                                                                                                                                                                                                print(f"Logging failed: {e}")



                                                                                                                                                                                                                                                                                                                                                                                                                                                    def open_audit_log_viewer(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                    win = tk.Toplevel(self.root)
                                                                                                                                                                                                                                                                                                                                                                                                                                                    win.title("Audit Log")
                                                                                                                                                                                                                                                                                                                                                                                                                                                    win.geometry("600x400")
                                                                                                                                                                                                                                                                                                                                                                                                                                                    text = tk.Text(win, wrap="word")
                                                                                                                                                                                                                                                                                                                                                                                                                                                    text.pack(fill="both", expand=True)
                                                                                                                                                                                                                                                                                                                                                                                                                                                        try:
                                                                                                                                                                                                                                                                                                                                                                                                                                                            with open("audit_log.txt", "r", encoding="utf-8") as f:
                                                                                                                                                                                                                                                                                                                                                                                                                                                            content = f.read()
                                                                                                                                                                                                                                                                                                                                                                                                                                                            text.insert("1.0", content)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                except FileNotFoundError:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                text.insert("1.0", "(No audit log found)")



                                                                                                                                                                                                                                                                                                                                                                                                                                                                    def toggle_preview_mode(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.preview_mode = not getattr(self, 'preview_mode', False)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.status_var.set("[PREVIEW MODE ON]" if self.preview_mode else "Ready")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.root.title("Xconomy Chooser - PREVIEW MODE" if self.preview_mode else "Xconomy Chooser")

                                                                                                                                                                                                                                                                                                                                                                                                                                                                        def convert_to_flat_style(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                        messagebox.showinfo("Flat Style", "Convert to Flat Style coming soon!")



                                                                                                                                                                                                                                                                                                                                                                                                                                                                            def lock_editor(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                try:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                from PIL import Image, ImageTk
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                import threading, time, random, os
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                import tkinter as tk

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    class PreviewLockScreen(tk.Toplevel):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        def __init__(self, master):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        super().__init__(master)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.title("Editor Locked")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.geometry("500x300")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.attributes('-topmost', True)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.grab_set()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.resizable(False, False)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.protocol("WM_DELETE_WINDOW", lambda: None)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.bg_frame = tk.Frame(self)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.bg_frame.pack(fill="both", expand=True)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.bg_label = tk.Label(self.bg_frame)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.bg_label.place(x=0, y=0, relwidth=1, relheight=1)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.set_background_media()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.build_overlay()

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            def set_background_media(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                try:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                source = self.master.get_lockscreen_image_source()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    except:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    source = "__DEFAULT__"
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        if source == "__DEFAULT__":
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        candidates = [f for f in os.listdir('.') if f.lower().startswith("lock_image") and f.lower().endswith(('.png', '.jpg', '.gif'))]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            if candidates:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            source = random.choice(candidates)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                else:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                if source.lower().endswith(".gif"):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                self.play_gif(source)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    else:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        try:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        img = Image.open(source)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        img = img.resize((500, 300), Image.Resampling.LANCZOS)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.bg_img = ImageTk.PhotoImage(img)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.bg_label.config(image=self.bg_img)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            except:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        pass

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            def play_gif(self, path):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                def loop():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    try:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    img = Image.open(path)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    frames = []
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    delays = []
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        try:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            while True:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            frame = img.copy()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            frames.append(ImageTk.PhotoImage(frame.resize((500, 300), Image.Resampling.LANCZOS)))
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            delays.append(img.info.get("duration", 100))
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            img.seek(len(frames))
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                except EOFError:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            pass
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                while self.winfo_exists():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    for i, frame in enumerate(frames):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        if not self.winfo_exists():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.bg_label.config(image=frame)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.bg_label.image = frame
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    time.sleep(delays[i] / 1000.0)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        except:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    pass
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    threading.Thread(target=loop, daemon=True).start()

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        def build_overlay(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        font_opts = ("Segoe UI", 11)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        label = ttk.Label(self.bg_frame, text="Editor is locked. Click to unlock.", font=font_opts)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        label.place(relx=0.5, rely=0.4, anchor="center")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.bg_frame.bind("<Button-1>", lambda e: self.destroy())

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        PreviewLockScreen(self.root)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            except Exception as e:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            messagebox.showerror("Lock Error", f"Could not lock editor: {e}")



                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                def get_lockscreen_image_source(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                # Looks for a default 'lock_image' file in current directory
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                candidates = [f for f in os.listdir('.') if f.lower().startswith("lock_image") and f.lower().endswith(('.png', '.jpg', '.gif'))]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            return candidates[0] if candidates else "__DEFAULT__"

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                def setup_widgets(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                # Search bar frame
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                search_frame = tk.Frame(self.root)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                search_frame.pack(fill="x", padx=10, pady=5)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                tk.Label(search_frame, text="Search Code:").pack(side="left")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                search_entry = tk.Entry(search_frame, width=40)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                search_entry.pack(side="left", padx=5)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                result_var = tk.StringVar()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                result_label = tk.Label(search_frame, textvariable=result_var)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                result_label.pack(side="left")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    def perform_search():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    target = search_entry.get().strip().lower()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        if not target:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        result_var.set("Enter search term.")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    matches = []
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        for trader, items in self.data.get("economy-override", {}).get("traders", {}).items():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            for item in items:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            code = item.get("tradeable-code", "")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                if target in code.lower():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                matches.append(code)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                result_var.set(f"Matches: {len(matches)}")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if matches:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    messagebox.showinfo("First Match", f"First match: {matches[0]}")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        else:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        messagebox.showwarning("No Match", "No matching items found.")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        tk.Button(search_frame, text="Go", command=perform_search).pack(side="left", padx=5)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        view_menu.add_checkbutton(label="Exclude Crafted", variable=self.exclude_crafted, command=self.populate_tree)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        view_menu.add_checkbutton(label="Exclude Improvised", variable=self.exclude_improvised, command=self.populate_tree)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        view_menu.add_separator()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        view_menu.add_command(label="Toggle Dark Mode", command=self.toggle_dark_mode)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        menubar.add_cascade(label="View", menu=view_menu)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        tools_menu = tk.Menu(menubar, tearoff=0)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        tools_menu.add_command(label="Launch CLI Mode", command=self.launch_cli_window)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        tools_menu.add_command(label="Lock Editor", command=self.lock_editor)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        menubar.add_cascade(label="Tools", menu=tools_menu)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.root.config(menu=menubar)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        # LEFT: Treeview of traders
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.left_frame = tk.Frame(self.root)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.left_frame.pack(side="left", fill="y")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.tree = ttk.Treeview(self.left_frame, height=34)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.tree.heading("#0", text="Outpost > Trader > Item")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.tree.pack(fill="y", expand=True)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        # CENTER: JSON preview area
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.center_frame = tk.Frame(self.root, padx=5)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.center_frame.pack(side="left", fill="both", expand=True)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.preview_label = tk.Label(self.center_frame, text="Selected Item JSON", anchor="w")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.preview_label.pack(anchor="nw")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.preview_text = tk.Text(self.center_frame, wrap="none", height=40, width=70)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.preview_text.pack(fill="both", expand=True)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.preview_text.configure(state="disabled", font=("Courier", 10), background="#2e2e2e", foreground="white")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        yscroll = ttk.Scrollbar(self.center_frame, orient="vertical", command=self.preview_text.yview)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        yscroll.pack(side="right", fill="y")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.preview_text.configure(yscrollcommand=yscroll.set)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        # RIGHT: Detail editor
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.detail_frame = tk.Frame(self.root, padx=10, pady=10)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.detail_frame.pack(side="right", fill="y")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.fields = {}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            for i, label in enumerate(["tradeable-code", "base-purchase-price", "base-sell-price", "required-famepoints", "can-be-purchased"]):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            tk.Label(self.detail_frame, text=label).grid(row=i, column=0, sticky="w")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            entry = tk.Entry(self.detail_frame, width=30)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            entry.grid(row=i, column=1)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            self.fields[label] = entry
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            tk.Button(self.detail_frame, text="Update Item", command=self.update_item).grid(row=6, column=0, columnspan=2, pady=10)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                def set_theme(self, style):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if self.dark_mode:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.root.configure(bg="#1e1e1e")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    style.theme_use("clam")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    style.configure("Treeview", background="#2e2e2e", foreground="white", fieldbackground="#2e2e2e")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    style.map("Treeview", background=[("selected", "#444")])
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        else:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.root.configure(bg="SystemButtonFace")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        style.theme_use("default")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            def toggle_dark_mode(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            self.dark_mode = not self.dark_mode
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            self.set_theme(ttk.Style(self.root))
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                def load_json(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                filepath = filedialog.askopenfilename(filetypes=[("JSON files", "*.json")])
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if not filepath:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    with open(filepath, "r") as file:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        try:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.data = json.load(file)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.current_file = filepath
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.flat_style = self.detect_flat_style()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.populate_tree()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.scan_categories()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.build_category_menu()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            except json.JSONDecodeError:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            messagebox.showerror("Error", "Invalid JSON file.")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                def detect_flat_style(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                # Detects if the loaded file is flat or nested.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if "economy-override" in self.data and "traders" in self.data["economy-override"]:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                return False
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                # Add better detection for future.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            return True
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    def convert_to_flat(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    # Placeholder for future conversion to flat format.
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    messagebox.showinfo("Coming Soon", "Flat style conversion will be available in a future update.")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        def scan_categories(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        """Scans all tradeable-codes and builds list of category candidates."""
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        codes = []
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        traders = self.data.get("economy-override", {}).get("traders", {})
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            for items in traders.values():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                for item in items:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                code = item.get("tradeable-code", "")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                codes.append(code)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                prefixes, infixes, suffixes = Counter(), Counter(), Counter()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    for code in codes:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    parts = code.split("_")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        if len(parts) > 1:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        prefixes[parts[0]] += 1
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        suffixes[parts[-1]] += 1
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            for part in parts:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            infixes[part] += 1
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            # Now rank all candidates (prefix, infix, suffix), deduplicate, and limit to most common
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            cats = []
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                for k, v in prefixes.items():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                cats.append( (v, f"{k}_*", "prefix", k) )
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    for k, v in infixes.items():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    cats.append( (v, f"*{k}*", "infix", k) )
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        for k, v in suffixes.items():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        cats.append( (v, f"*_{k}", "suffix", k) )
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        # Sort by count, limit
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        cats.sort(reverse=True)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        # Deduplicate on display key
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        seen = set()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        out = []
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            for count, label, match_type, raw in cats:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            if label in seen: continue
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            seen.add(label)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            out.append( (label, match_type, raw, count) )
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                if len(out) >= CATEGORY_LIMIT:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            break
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            self.categories = out
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                def build_category_menu(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                self.category_menu.delete(0, tk.END)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if not self.categories:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.category_menu.add_command(label="No categories found", state="disabled")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    for label, match_type, raw, count in self.categories:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    pretty = f"{label} ({count})"
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.category_menu.add_command(label=pretty,command=lambda m=match_type, r=raw, c=label: self.category_batch_edit(m, r, c))
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.category_menu.add_separator()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.category_menu.add_command(label="Refresh Categories", command=self.scan_categories)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        def get_items_by_category(self, match_type, raw):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        """Return list of (trader_key, idx, item) tuples for matching items."""
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        matches = []
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        traders = self.data.get("economy-override", {}).get("traders", {})
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            for trader_key, items in traders.items():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                for idx, item in enumerate(items):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                code = item.get("tradeable-code", "")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                if (
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                (match_type == "prefix" and code.startswith(raw + "_"))
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                or (match_type == "suffix" and code.endswith("_" + raw))
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                or (match_type == "infix" and raw in code)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                matches.append( (trader_key, idx, item) )
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            return matches
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                def category_batch_edit(self, match_type, raw, label):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                matches = self.get_items_by_category(match_type, raw)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                count = len(matches)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if count == 0:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    messagebox.showinfo("No Matches", f"No items found for category: {label}")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                # Modal
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                win = tk.Toplevel(self.root)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                win.title(f"Batch Edit: {label}")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                win.geometry("420x250")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                tk.Label(win, text=f"{count} items found for category: {label}", font=("Arial", 12, "bold")).pack(pady=10)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                # can-be-purchased batch
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                purchase_var = tk.StringVar(value="nochange")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                fame_var = tk.StringVar(value="")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                fr = tk.Frame(win)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                fr.pack(pady=5)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                tk.Label(fr, text="Set can-be-purchased:").grid(row=0, column=0, sticky="e")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ttk.Combobox(fr, textvariable=purchase_var, width=8, values=["nochange", "true", "false", "default"]).grid(row=0, column=1)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                tk.Label(fr, text="Set famepoints:").grid(row=1, column=0, sticky="e")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                tk.Entry(fr, textvariable=fame_var, width=10).grid(row=1, column=1)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                # Preview option
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    def preview_items():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    items = [item['tradeable-code'] for _, _, item in matches]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    preview_win = tk.Toplevel(win)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    preview_win.title("Preview Matching Items")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    txt = tk.Text(preview_win, wrap="none", height=20, width=50)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    txt.pack(fill="both", expand=True)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    txt.insert("end", "\n".join(items))
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    txt.configure(state="disabled")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    yscroll = ttk.Scrollbar(preview_win, orient="vertical", command=txt.yview)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    yscroll.pack(side="right", fill="y")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    txt.configure(yscrollcommand=yscroll.set)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    tk.Button(win, text="Preview Items", command=preview_items).pack(pady=3)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        def do_apply():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        purchase = purchase_var.get()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        fame = fame_var.get()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            if purchase not in ("nochange", "true", "false", "default"):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            messagebox.showerror("Invalid", "Select a valid can-be-purchased value.")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            if fame and not (fame.lstrip("-").isdigit()):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            messagebox.showerror("Invalid", "Famepoints must be blank or an integer.")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            if not messagebox.askyesno("Confirm", f"Apply these changes to {count} items in {label}?"):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        changed = 0
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            for trader_key, idx, item in matches:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                if purchase != "nochange":
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                item['can-be-purchased'] = purchase
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                changed += 1
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if fame:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    item['required-famepoints'] = fame
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    changed += 1
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.populate_tree()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    win.destroy()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    messagebox.showinfo("Done", f"Updated {count} items for category {label}.")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    tk.Button(win, text="Apply", command=do_apply).pack(pady=10)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    tk.Button(win, text="Cancel", command=win.destroy).pack(pady=1)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        def populate_tree(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.tree.delete(*self.tree.get_children())
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        traders = self.data.get("economy-override", {}).get("traders", {})
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            for trader_key, items in traders.items():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            outpost = trader_key.split("_")[0]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            trader = trader_key
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            outpost_id = f"{outpost}"
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                if not self.tree.exists(outpost_id):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                self.tree.insert("", "end", iid=outpost_id, text=outpost)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                trader_id = f"{trader}"
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                self.tree.insert(outpost_id, "end", iid=trader_id, text=trader)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    for idx, item in enumerate(items):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    code = item.get("tradeable-code", "Unnamed")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        if self.category_filter:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            if self.category_filter == 'Weapon_':
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                if not (code.startswith('Weapon_') and "Weapon_parts" not in code):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            continue
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                elif self.category_filter not in code:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            continue
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                if self.exclude_crafted.get() and ("Crafted" in code or code.endswith("_Crafted")):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            continue
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                if self.exclude_improvised.get() and "Improvised" in code:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            continue
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            item_id = f"{trader}_{idx}"
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            self.tree.insert(trader_id, "end", iid=item_id, text=code)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                def on_tree_select(self, event):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                selected = self.tree.selection()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if not selected:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                item_id = selected[0]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                parts = item_id.split("_")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                # Only proceed if the last part is actually an integer
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if not parts[-1].isdigit():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                return  # Skip outpost/trader nodes!
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                trader_key = "_".join(parts[:-1])
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                idx = int(parts[-1])
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                item = self.data["economy-override"]["traders"].get(trader_key, [])[idx]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    for key in self.fields:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.fields[key].delete(0, tk.END)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.fields[key].insert(0, item.get(key, ""))
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    # Update JSON preview
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.preview_text.configure(state="normal")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.preview_text.delete(1.0, tk.END)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.preview_text.insert(tk.END, json.dumps(item, indent=4))
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    self.preview_text.configure(state="disabled")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        def update_item(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        selected = self.tree.selection()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            if not selected:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        item_id = selected[0]
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        parts = item_id.split("_")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        trader_key = "_".join(parts[:-1])
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        idx = int(parts[-1])
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            for key in self.fields:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            self.data["economy-override"]["traders"][trader_key][idx][key] = self.fields[key].get()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            messagebox.showinfo("Success", "Item updated.")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            self.on_tree_select(None)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                def save_json(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if not self.data:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                save_path = filedialog.asksaveasfilename(defaultextension=".json", filetypes=[("JSON files", "*.json")])
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if not save_path:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    with open(save_path, "w") as file:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    json.dump(self.data, file, indent=4)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    messagebox.showinfo("Saved", f"File saved to {save_path}")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        def set_category_filter(self, category):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.category_filter = category
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        self.populate_tree()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            def global_price_edit(self):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                if not self.data:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            win = tk.Toplevel(self.root)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            win.title("Global % Price Editor")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            tk.Label(win, text="Field to Edit:").grid(row=0, column=0, sticky="e")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            field_var = tk.StringVar(value="base-purchase-price")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            ttk.Combobox(win, textvariable=field_var, values=["base-purchase-price", "base-sell-price"]).grid(row=0, column=1)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            tk.Label(win, text="Percentage Change (-99 to 100):").grid(row=1, column=0, sticky="e")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            percent_entry = tk.Entry(win)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            percent_entry.grid(row=1, column=1)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                def apply():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    try:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    percent = int(percent_entry.get())
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        if not (-99 <= percent <= 100):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    raise ValueError
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        except ValueError:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        messagebox.showerror("Invalid", "Enter a number from -99 to 100.")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    field = field_var.get()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    traders = self.data.get("economy-override", {}).get("traders", {})
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        for trader_items in traders.values():
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            for item in trader_items:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                try:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                val = self.parse_price_value(item.get(field, "-1"))
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if val in [-1, None]:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                continue
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                new_val = val + (val * percent / 100)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                item[field] = str(int(round(new_val)))
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    except Exception:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                continue
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                self.populate_tree()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                messagebox.showinfo("Done", f"{field} updated globally by {percent}%")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                win.destroy()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                tk.Button(win, text="Apply", command=apply).grid(row=2, column=0, columnspan=2, pady=10)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    if __name__ == "__main__":
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    root = tk.Tk()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    app = EconomyChooserApp(root)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    root.mainloop()

                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        def load_json_from_path(self, filepath):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            if not os.path.exists(filepath):
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            messagebox.showerror("Error", f"File not found: {filepath}")
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        return
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            with open(filepath, "r") as file:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                try:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                self.data = json.load(file)
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                self.current_file = filepath
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                self.flat_style = self.detect_flat_style()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                self.populate_tree()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                self.scan_categories()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                self.build_category_menu()
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    except json.JSONDecodeError:
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    messagebox.showerror("Error", "Invalid JSON file.")
