import os
import time
import json
import random
import threading
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, simpledialog
from collections import Counter, defaultdict
from PIL import Image, ImageTk
import subprocess

CATEGORY_LIMIT = 40


class EconomyChooserApp:

    def set_category_mode(self, mode):
        self.category_mode.set(mode)
        messagebox.showinfo("Category Mode", f"Set category refresh mode to: {mode}")

    def refresh_categories_by_mode(self):
        mode = self.category_mode.get()
        if mode == "Manual":
            return
        if mode == "Always":
            self.build_smart_categories()
            self.build_category_menu()
        elif mode == "F.I.S.H.":
            self.build_smart_categories(use_fish_logic=True)
            self.build_category_menu()

    def import_category_file(self):
        path = filedialog.askopenfilename(filetypes=[("JSON Files", "*.json")])
        if not path:
            return
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if isinstance(data, list):
                self.categories = sorted(set(data))
                messagebox.showinfo("Success", f"Loaded {len(self.categories)} categories from file.")
                self.build_category_menu()
            else:
                raise ValueError("Invalid format. Expected list of categories.")
        except Exception as e:
            messagebox.showerror("Error", f"Could not import category file: {e}")

    def export_category_file(self):
        if not self.categories:
            messagebox.showinfo("No Categories", "There are no categories to export.")
            return
        path = filedialog.asksaveasfilename(defaultextension=".json",
                                           filetypes=[("JSON Files", "*.json")],
                                           initialfile="my_categories.json")
        if not path:
            return
        try:
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(sorted(set(self.categories)), f, indent=4)
            messagebox.showinfo("Exported", f"Saved {len(self.categories)} categories.")
        except Exception as e:
            messagebox.showerror("Error", f"Export failed: {e}")

    def build_smart_categories(self):
        '''AI-powered smart category builder (F.I.S.H.)'''
        try:
            keywords = {
                'Fish': lambda code: code.startswith('Fish_') and not any(x in code for x in ['Fishing', 'fishing']),
                'Weapon': lambda code: code.startswith('Weapon_') and 'Weapon_parts' not in code,
                'Crafted': lambda code: code.startswith('Crafted'),
                'Improvised': lambda code: 'Improvised' in code,
                'Ammo': lambda code: code.endswith('_AP_CR') or 'Cal_' in code,
                'Police': lambda code: any(x in code for x in ['Police', 'MP5', 'M9']),
                'Military': lambda code: any(x in code for x in ['AK', 'M16', 'Grenade', 'Military']),
            }
            category_hits = {k: [] for k in keywords}
            all_codes = set()
            for trader in self.data.values():
                for items in trader.values():
                    for item in items:
                        if isinstance(item, dict):
                            code = item.get('tradeable-code', '')
                            if not code or code in all_codes:
                                continue
                            all_codes.add(code)
                            for label, rule in keywords.items():
                                if rule(code):
                                    category_hits[label].append(code)
            self.categories = sorted(category_hits.keys())
            self.smart_category_map = category_hits
        except Exception as e:
            safe_error(f"[build_smart_categories] Failed: {e}")

        '''Rebuilds self.categories from loaded JSON'''
        try:
            category_set = set()
            for trader_data in self.data.values():
                for item_list in trader_data.values():
                    for item in item_list:
                        if isinstance(item, dict):
                            code = item.get("tradeable-code", "")
                            if code:
                                category_set.add(code.split("_")[0])
            self.categories = sorted(category_set)
        except Exception as e:
            safe_error(f"[scan_categories] Failed: {e}")
    def __init__(self, root):
        self.root = root
        self.root.title("Xconomy Chooser v1.71k")
        self.root.geometry("1500x780")
        self.data = {}
        self.current_file = None
        self.category_filter = None
        self.exclude_crafted = tk.BooleanVar(value=False)
        self.exclude_improvised = tk.BooleanVar(value=False)
        self.dark_mode = False
        self.category_mode = tk.StringVar(value="Always")
        self.categories = []
        self.category_menu_entries = []
        self.flat_style = False
        self.config_data = {}
        category_menu.add_separator()
        category_menu.add_command(label="Import Category File", command=self.import_category_file)
        category_menu.add_command(label="Export Category File", command=self.export_category_file)
        self.fields = {}
        self.status_var = tk.StringVar(value="Ready")
        self.setup_widgets()
     #  self.load_config()
        self.build_smart_categories()
        self.build_category_menu()
        monitor_file_changes(self, self.current_file)

    def setup_widgets(self):
        style = ttk.Style(self.root)
        self.set_theme(style)

        # Menu bar
        menubar = tk.Menu(self.root)
        settings_menu = tk.Menu(menubar, tearoff=0)
        settings_menu.add_command(label="Rebuild Categories: Manual", command=lambda: self.set_category_mode("Manual"))
        settings_menu.add_command(label="Rebuild Categories: Always", command=lambda: self.set_category_mode("Always"))
        settings_menu.add_command(label="Rebuild Categories: F.I.S.H.", command=lambda: self.set_category_mode("F.I.S.H."))
        menubar.add_cascade(label="Settings", menu=settings_menu)
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="Load JSON", command=self.load_json)
        file_menu.add_command(label="Save JSON", command=self.save_json)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        menubar.add_cascade(label="File", menu=file_menu)

        view_menu = tk.Menu(menubar, tearoff=0)
        view_menu.add_checkbutton(label="Exclude Crafted", variable=self.exclude_crafted, command=self.populate_tree)
        view_menu.add_checkbutton(label="Exclude Improvised", variable=self.exclude_improvised, command=self.populate_tree)
        view_menu.add_separator()
        view_menu.add_command(label="Toggle Dark Mode", command=self.toggle_dark_mode)
        menubar.add_cascade(label="View", menu=view_menu)

        tools_menu = self.build_tools_menu()
        menubar.add_cascade(label="Tools", menu=tools_menu)

        self.category_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Categories", menu=self.category_menu)

        help_menu = tk.Menu(menubar, tearoff=0)
        help_menu.add_command(label="About", command=self.show_about)
        menubar.add_cascade(label="Help", menu=help_menu)

        self.root.config(menu=menubar)

        # Layout frames
        search_frame = tk.Frame(self.root)
        search_frame.pack(fill="x", padx=10, pady=5)
        tk.Label(search_frame, text="Search Code:").pack(side="left")
        search_entry = tk.Entry(search_frame, width=40)
        search_entry.pack(side="left", padx=5)
        result_var = tk.StringVar()
        result_label = tk.Label(search_frame, textvariable=result_var)
        result_label.pack(side="left")

        def perform_search():
            target = search_entry.get().strip().lower()
            if not target:
                result_var.set("Enter search term.")
                return
            matches = []
            for trader, items in self.data.get("economy-override", {}).get("traders", {}).items():
                for item in items:
                    code = item.get("tradeable-code", "")
                    if target in code.lower():
                        matches.append(code)
            result_var.set(f"Matches: {len(matches)}")
            if matches:
                messagebox.showinfo("First Match", f"First match: {matches[0]}")
            else:
                messagebox.showwarning("No Match", "No matching items found.")

        tk.Button(search_frame, text="Go", command=perform_search).pack(side="left", padx=5)

        # Treeview (left)
        self.left_frame = tk.Frame(self.root)
        self.left_frame.pack(side="left", fill="y")
        self.tree = ttk.Treeview(self.left_frame, height=34)
        self.tree.heading("#0", text="Outpost > Trader > Item")
        self.tree.pack(fill="y", expand=True)
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)

        # JSON Preview (center)
        self.center_frame = tk.Frame(self.root, padx=5)
        self.center_frame.pack(side="left", fill="both", expand=True)
        self.preview_label = tk.Label(self.center_frame, text="Selected Item JSON", anchor="w")
        self.preview_label.pack(anchor="nw")
        self.preview_text = tk.Text(self.center_frame, wrap="none", height=40, width=70)
        self.preview_text.pack(fill="both", expand=True)
        self.preview_text.configure(state="disabled", font=("Courier", 10), background="#2e2e2e", foreground="white")
        yscroll = ttk.Scrollbar(self.center_frame, orient="vertical", command=self.preview_text.yview)
        yscroll.pack(side="right", fill="y")
        self.preview_text.configure(yscrollcommand=yscroll.set)

        # Item Field Editor (right)
        self.detail_frame = tk.Frame(self.root, padx=10, pady=10)
        self.detail_frame.pack(side="right", fill="y")
        labels = ["tradeable-code", "base-purchase-price", "base-sell-price", "required-famepoints", "can-be-purchased"]
        for i, label in enumerate(labels):
            tk.Label(self.detail_frame, text=label).grid(row=i, column=0, sticky="w")
            entry = tk.Entry(self.detail_frame, width=30)
            entry.grid(row=i, column=1)
            self.fields[label] = entry
        tk.Button(self.detail_frame, text="Update Item", command=self.update_item).grid(row=6, column=0, columnspan=2, pady=10)

    def build_category_menu(self):
        self.category_menu.delete(0, "end")
        if not self.categories:
            self.category_menu.add_command(label="No categories found", state="disabled")
            return
        for label, match_type, raw, count in self.categories:
            pretty = f"{label} ({count})"
            self.category_menu.add_command(
                label=pretty,
                command=lambda m=match_type, r=raw, c=label: self.category_batch_edit(m, r, c)
            )
        self.category_menu.add_separator()
        self.category_menu.add_command(label="Refresh Categories", command=self.scan_categories)

    def set_theme(self, style):
        if self.dark_mode:
            self.root.configure(bg="#1e1e1e")
            style.theme_use("clam")
            style.configure("Treeview", background="#2e2e2e", foreground="white", fieldbackground="#2e2e2e")
            style.map("Treeview", background=[("selected", "#444")])
        else:
            self.root.configure(bg="SystemButtonFace")
            style.theme_use("default")



    def load_json(self):
        filepath = filedialog.askopenfilename(filetypes=[("JSON files", "*.json")])
        if not filepath:
            return
        try:
            with open(filepath, "r", encoding="utf-8") as file:
                self.data = json.load(file)
            self.current_file = filepath
            self.populate_tree()
            self.refresh_categories_by_mode()
            self.build_smart_categories()
            self.build_category_menu()
            messagebox.showinfo("Loaded", f"Loaded {os.path.basename(filepath)}")
        except json.JSONDecodeError:
            messagebox.showerror("Error", "Invalid JSON file.")

    def save_json(self):
        if not self.data:
            return
        save_path = filedialog.asksaveasfilename(defaultextension=".json", filetypes=[("JSON files", "*.json")])
        if not save_path:
            return
        with open(save_path, "w", encoding="utf-8") as file:
            json.dump(self.data, file, indent=2)
        messagebox.showinfo("Saved", f"File saved to {save_path}")

    def populate_tree(self):
        self.tree.delete(*self.tree.get_children())
        traders = self.data.get("economy-override", {}).get("traders", {})
        for trader_key, items in traders.items():
            outpost = trader_key.split("_")[0]
            trader = trader_key
            if not self.tree.exists(outpost):
                self.tree.insert("", "end", iid=outpost, text=outpost)
            self.tree.insert(outpost, "end", iid=trader, text=trader)
            for idx, item in enumerate(items):
                code = item.get("tradeable-code", f"{trader}_{idx}")
                if self.exclude_crafted.get() and ("Crafted" in code or code.endswith("_Crafted")):
                    continue
                if self.exclude_improvised.get() and "Improvised" in code:
                    continue
                item_id = f"{trader}_{idx}"
                self.tree.insert(trader, "end", iid=item_id, text=code)

    def on_tree_select(self, event):
        selected = self.tree.selection()
        if not selected:
            return
        item_id = selected[0]
        parts = item_id.split("_")
        if not parts[-1].isdigit():
            return
        trader_key = "_".join(parts[:-1])
        idx = int(parts[-1])
        item = self.data.get("economy-override", {}).get("traders", {}).get(trader_key, [])[idx]

        for key in self.fields:
            self.fields[key].delete(0, tk.END)
            self.fields[key].insert(0, item.get(key, ""))

        self.preview_text.configure(state="normal")
        self.preview_text.delete("1.0", tk.END)
        self.preview_text.insert(tk.END, json.dumps(item, indent=4))
        self.preview_text.configure(state="disabled")

    def update_item(self):
        selected = self.tree.selection()
        if not selected:
            return
        item_id = selected[0]
        parts = item_id.split("_")
        if not parts[-1].isdigit():
            return
        trader_key = "_".join(parts[:-1])
        idx = int(parts[-1])
        for key in self.fields:
            value = self.fields[key].get()
            self.data["economy-override"]["traders"][trader_key][idx][key] = value
        messagebox.showinfo("Updated", "Item updated.")
        self.on_tree_select(None)


    def get_items_by_category(self, match_type, raw):
        matches = []
        traders = self.data.get("economy-override", {}).get("traders", {})
        for trader_key, items in traders.items():
            for idx, item in enumerate(items):
                code = item.get("tradeable-code", "")
                if (
                    (match_type == "prefix" and code.startswith(raw + "_")) or
                    (match_type == "suffix" and code.endswith("_" + raw)) or
                    (match_type == "infix" and raw in code)
                ):
                    matches.append((trader_key, idx, item))
        return matches

    def category_batch_edit(self, match_type, raw, label):
        matches = self.get_items_by_category(match_type, raw)
        count = len(matches)
        if count == 0:
            messagebox.showinfo("No Matches", f"No items found for category: {label}")
            return

        win = tk.Toplevel(self.root)
        win.title(f"Batch Edit: {label}")
        win.geometry("400x240")

        tk.Label(win, text=f"{count} items matched in: {label}").pack(pady=5)

        fr = tk.Frame(win)
        fr.pack(pady=5)

        purchase_var = tk.StringVar(value="nochange")
        tk.Label(fr, text="Set can-be-purchased:").grid(row=0, column=0, sticky="e")
        ttk.Combobox(fr, textvariable=purchase_var, values=["nochange", "true", "false", "default"]).grid(row=0, column=1)

        fame_var = tk.StringVar(value="")
        tk.Label(fr, text="Set famepoints:").grid(row=1, column=0, sticky="e")
        tk.Entry(fr, textvariable=fame_var, width=10).grid(row=1, column=1)

        def apply():
            nonlocal matches
            purchase = purchase_var.get()
            fame = fame_var.get()

            if purchase not in ("nochange", "true", "false", "default"):
                messagebox.showerror("Invalid", "Choose valid can-be-purchased option.")
                return
            if fame and not fame.lstrip("-").isdigit():
                messagebox.showerror("Invalid", "Famepoints must be blank or an integer.")
                return
            if not messagebox.askyesno("Confirm", f"Apply to {count} items?"):
                return

            changed = 0
            for trader_key, idx, item in matches:
                if purchase != "nochange":
                    item["can-be-purchased"] = purchase
                    changed += 1
                if fame:
                    item["required-famepoints"] = fame
                    changed += 1

            self.populate_tree()
            win.destroy()
            messagebox.showinfo("Updated", f"{changed} changes applied to category: {label}")

        tk.Button(win, text="Apply", command=apply).pack(pady=10)
        tk.Button(win, text="Cancel", command=win.destroy).pack()

    def set_category_filter(self, category):
        self.category_filter = category
        self.populate_tree()


    def launch_cli_window(self):
        if self.current_file:
            args = ["python", "economychooser1_52a_hybridmode.py", self.current_file]
        else:
            args = ["python", "economychooser1_52a_hybridmode.py"]
        subprocess.Popen(args, creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)

    def open_config_editor(self):
        win = tk.Toplevel(self.root)
        win.title("App Configuration")
        win.geometry("300x200")

        dev_var = tk.BooleanVar(value=self.config_data.get("dev_mode", False))
        night_var = tk.BooleanVar(value=self.config_data.get("night_mode", False))

        tk.Checkbutton(win, text="Developer Mode", variable=dev_var).pack(anchor="w", padx=10, pady=10)
        tk.Checkbutton(win, text="Night Mode (WIP)", variable=night_var).pack(anchor="w", padx=10)

        def save_and_close():
            self.config_data["dev_mode"] = dev_var.get()
            self.config_data["night_mode"] = night_var.get()
            try:
                with open("config.json", "w", encoding="utf-8") as f:
                    json.dump(self.config_data, f, indent=2)
                messagebox.showinfo("Saved", "Configuration updated.")
                win.destroy()
            except Exception as e:
                messagebox.showerror("Error", str(e))

        tk.Button(win, text="Save", command=save_and_close).pack(pady=10)

    def toggle_dark_mode(self):
        self.dark_mode = not self.dark_mode
        self.set_theme(ttk.Style(self.root))

    def lock_editor(self):
        class PreviewLockScreen(tk.Toplevel):
            def __init__(self, master):
                super().__init__(master)
                self.title("Editor Locked")
                self.geometry("500x300")
                self.attributes('-topmost', True)
                self.grab_set()
                self.resizable(False, False)
                self.protocol("WM_DELETE_WINDOW", lambda: None)
                self.bg_frame = tk.Frame(self)
                self.bg_frame.pack(fill="both", expand=True)
                self.bg_label = tk.Label(self.bg_frame)
                self.bg_label.place(x=0, y=0, relwidth=1, relheight=1)
                self.set_background_media()
                self.build_overlay()

            def set_background_media(self):
                try:
                    source = self.master.get_lockscreen_image_source()
                except:
                    source = "__DEFAULT__"
                if source == "__DEFAULT__":
                    candidates = [f for f in os.listdir('.') if f.lower().startswith("lock_image") and f.lower().endswith(('.png', '.jpg', '.gif'))]
                    if candidates:
                        source = random.choice(candidates)
                    else:
                        return
                if source.lower().endswith(".gif"):
                    self.play_gif(source)
                else:
                    try:
                        img = Image.open(source)
                        img = img.resize((500, 300), Image.Resampling.LANCZOS)
                        self.bg_img = ImageTk.PhotoImage(img)
                        self.bg_label.config(image=self.bg_img)
                    except:
                        pass

            def play_gif(self, path):
                def loop():
                    try:
                        img = Image.open(path)
                        frames = []
                        delays = []
                        try:
                            while True:
                                frame = img.copy()
                                frames.append(ImageTk.PhotoImage(frame.resize((500, 300), Image.Resampling.LANCZOS)))
                                delays.append(img.info.get("duration", 100))
                                img.seek(len(frames))
                        except EOFError:
                            pass
                        while self.winfo_exists():
                            for i, frame in enumerate(frames):
                                if not self.winfo_exists():
                                    return
                                self.bg_label.config(image=frame)
                                self.bg_label.image = frame
                                time.sleep(delays[i] / 1000.0)
                    except:
                        pass
                threading.Thread(target=loop, daemon=True).start()

            def build_overlay(self):
                font_opts = ("Segoe UI", 11)
                label = ttk.Label(self.bg_frame, text="Editor is locked. Click to unlock.", font=font_opts)
                label.place(relx=0.5, rely=0.4, anchor="center")
                self.bg_frame.bind("<Button-1>", lambda e: self.destroy())

        try:
            PreviewLockScreen(self.root)
        except Exception as e:
            messagebox.showerror("Lock Error", f"Could not lock editor: {e}")

    def get_lockscreen_image_source(self):
        candidates = [f for f in os.listdir('.') if f.lower().startswith("lock_image") and f.lower().endswith(('.png', '.jpg', '.gif'))]
        return candidates[0] if candidates else "__DEFAULT__"

    def show_about(self):
        msg = (
            "Xconomy Chooser\n"
            "Version: v1.71k\n"
            "Author: V1nceTD\n"
            "Powered by Python + Tkinter\n\n"
            "Custom economy editor for SCUM servers.\n"
            "Features: GUI + CLI hybrid, Smart Category Logic (F.I.S.H),\n"
            "Batch Edits, Presets, Night Mode, and more."
        )
        messagebox.showinfo("About Xconomy Chooser", msg)


    def save_item_as_preset(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showinfo("No Selection", "Select an item to save as a preset.")
            return
        item_id = selected[0]
        parts = item_id.split("_")
        if not parts[-1].isdigit():
            return
        trader_key = "_".join(parts[:-1])
        idx = int(parts[-1])
        item = self.data.get("economy-override", {}).get("traders", {}).get(trader_key, [])[idx]
        preset_name = simpledialog.askstring("Preset Name", "Enter name for this preset:")
        if not preset_name:
            return
        os.makedirs("presets", exist_ok=True)
        with open(f"presets/{preset_name}.json", "w", encoding="utf-8") as f:
            json.dump(item, f, indent=2)
        messagebox.showinfo("Saved", f"Preset saved: {preset_name}")

    def load_preset_into_item(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showinfo("No Selection", "Select an item to apply the preset to.")
            return
        preset_files = [f for f in os.listdir("presets") if f.endswith(".json")]
        if not preset_files:
            messagebox.showerror("No Presets", "No presets found.")
            return
        win = tk.Toplevel(self.root)
        win.title("Load Preset")
        win.geometry("300x200")

        preset_var = tk.StringVar(value=preset_files[0])
        ttk.Label(win, text="Choose preset:").pack(pady=5)
        dropdown = ttk.Combobox(win, values=preset_files, textvariable=preset_var)
        dropdown.pack()

        def apply():
            fname = preset_var.get()
            path = os.path.join("presets", fname)
            try:
                with open(path, "r", encoding="utf-8") as f:
                    preset = json.load(f)
            except Exception as e:
                messagebox.showerror("Load Failed", str(e))
                return

            item_id = selected[0]
            parts = item_id.split("_")
            if not parts[-1].isdigit():
                return
            trader_key = "_".join(parts[:-1])
            idx = int(parts[-1])
            self.data["economy-override"]["traders"][trader_key][idx] = preset
            self.populate_tree()
            win.destroy()
            messagebox.showinfo("Applied", f"Preset '{fname}' applied.")

        tk.Button(win, text="Apply Preset", command=apply).pack(pady=10)
        tk.Button(win, text="Cancel", command=win.destroy).pack()

    def build_tools_menu(self):
        tools_menu = tk.Menu(self.root, tearoff=0)
        tools_menu.add_command(label="Launch CLI Mode", command=self.launch_cli_window)
        tools_menu.add_command(label="Lock Editor", command=self.lock_editor)
        tools_menu.add_command(label="Save Item As Preset", command=self.save_item_as_preset)
        tools_menu.add_command(label="Load Preset To Item", command=self.load_preset_into_item)
        tools_menu.add_command(label="Settings", command=self.open_config_editor)
        return tools_menu


    def apply_spread_edit(self):
        win = tk.Toplevel(self.root)
        win.title("Spread Edit")
        win.geometry("400x300")

        tk.Label(win, text="Target Field:").pack()
        field_var = tk.StringVar()
        ttk.Entry(win, textvariable=field_var).pack(pady=5)

        tk.Label(win, text="Percentage Change (e.g. 10 or -15):").pack()
        percent_var = tk.StringVar()
        ttk.Entry(win, textvariable=percent_var).pack(pady=5)

        cat_only = tk.BooleanVar()
        tk.Checkbutton(win, text="Apply only to current category", variable=cat_only).pack(pady=5)

        def apply():
            field = field_var.get().strip()
            try:
                pct = float(percent_var.get())
            except ValueError:
                messagebox.showerror("Invalid", "Enter a valid number for percent change.")
                return

            if not field:
                messagebox.showerror("Missing", "Enter a target field.")
                return

            matches = []
            if cat_only.get() and self.category_filter:
                match_type, raw = self.category_filter[1], self.category_filter[2]
                matches = self.get_items_by_category(match_type, raw)
            else:
                for trader_key, items in self.data.get("economy-override", {}).get("traders", {}).items():
                    for idx, item in enumerate(items):
                        matches.append((trader_key, idx, item))

            changed = 0
            for trader_key, idx, item in matches:
                try:
                    val = float(item.get(field, 0))
                    new_val = round(val * (1 + pct / 100))
                    item[field] = str(int(new_val))
                    changed += 1
                except (ValueError, TypeError):
                    continue

            win.destroy()
            self.populate_tree()
            messagebox.showinfo("Spread Applied", f"{changed} items updated with {pct}% spread on '{field}'.")

        tk.Button(win, text="Apply Spread", command=apply).pack(pady=10)
        tk.Button(win, text="Cancel", command=win.destroy).pack()

    def build_tools_menu(self):
        tools_menu = tk.Menu(self.root, tearoff=0)
        tools_menu.add_command(label="Launch CLI Mode", command=self.launch_cli_window)
        tools_menu.add_command(label="Lock Editor", command=self.lock_editor)
        tools_menu.add_command(label="Save Item As Preset", command=self.save_item_as_preset)
        tools_menu.add_command(label="Load Preset To Item", command=self.load_preset_into_item)
        tools_menu.add_command(label="Apply Spread Edit", command=self.apply_spread_edit)
        tools_menu.add_command(label="Settings", command=self.open_config_editor)
        return tools_menu


    def convert_to_flat_format(self):
        traders = self.data.get("economy-override", {}).get("traders", {})
        new_traders = {}
        for outpost_key, subdict in traders.items():
            if isinstance(subdict, dict):  # nested style
                for trader_key, items in subdict.items():
                    new_key = f"{outpost_key}_{trader_key}"
                    new_traders[new_key] = items
            else:
                new_traders[outpost_key] = subdict
        self.data["economy-override"]["traders"] = new_traders
        self.populate_tree()
        messagebox.showinfo("Converted", "Trader structure converted to flat format.")

    def check_if_nested_format(self):
        traders = self.data.get("economy-override", {}).get("traders", {})
        sample = list(traders.values())[0] if traders else {}
        return isinstance(sample, dict)

    def clean_empty_items(self):
        count = 0
        traders = self.data.get("economy-override", {}).get("traders", {})
        for trader_key, items in list(traders.items()):
            filtered = [item for item in items if item]
            if not filtered:
                del traders[trader_key]
                count += 1
            else:
                traders[trader_key] = filtered
        self.populate_tree()
        messagebox.showinfo("Cleaned", f"Removed {count} empty traders or item lists.")

    def build_file_menu(self):
        file_menu = tk.Menu(self.root, tearoff=0)
        file_menu.add_command(label="Open JSON...", command=self.load_json)
        file_menu.add_command(label="Save JSON As...", command=self.save_json)
        file_menu.add_separator()
        file_menu.add_command(label="Convert to Flat Format", command=self.convert_to_flat_format)
        file_menu.add_command(label="Clean Empty Traders", command=self.clean_empty_items)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        return file_menu

def monitor_file_changes(app, filepath):
    if not filepath:
        return
    last_modified = os.path.getmtime(filepath)

    def check_reload():
        nonlocal last_modified
        try:
            current_modified = os.path.getmtime(filepath)
            if current_modified != last_modified:
                answer = messagebox.askyesno("Reload Detected",
                                             f"The file '{os.path.basename(filepath)}' was modified externally.\n\nReload from disk?")
                if answer:
                    app.load_json_from_path(filepath)
                    last_modified = current_modified
        except Exception:
            pass
        app.root.after(3000, check_reload)

    app.root.after(3000, check_reload)


if __name__ == "__main__":
    root = tk.Tk()
    app = EconomyChooserApp(root)
    root.mainloop()
