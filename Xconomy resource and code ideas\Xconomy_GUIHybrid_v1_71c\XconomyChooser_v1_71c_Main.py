
import subprocess

def launch_cli_window(self):
    if self.current_file:
        args = ["python", "economychooser1_52a_hybridmode.py", self.current_file]
    else:
        args = ["python", "economychooser1_52a_hybridmode.py"]
    subprocess.Popen(args, creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)




import os
import time
from tkinter import simpledialog

def monitor_file_changes(app, filepath):
    if not filepath:
        return
    last_modified = os.path.getmtime(filepath)

    def check_reload():
        nonlocal last_modified
        try:
            current_modified = os.path.getmtime(filepath)
            if current_modified != last_modified:
                answer = messagebox.askyesno("Reload Detected",
                    f"The file '{os.path.basename(filepath)}' was modified externally.\n\nReload from disk?")
                if answer:
                    app.load_json_from_path(filepath)
                    last_modified = current_modified
        except Exception:
            pass
        app.root.after(3000, check_reload)

    app.root.after(3000, check_reload)

# Patch into EconomyChooserApp.__init__ to auto-launch if current_file exists

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
from collections import Counter, defaultdict

CATEGORY_LIMIT = 40  # Show only the top N categories in the menu

class EconomyChooserApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Xconomy Chooser v1.05")
        self.root.geometry("1500x780")
        self.data = {}
        self.current_file = None
        self.category_filter = None
        self.exclude_crafted = tk.BooleanVar(value=False)
        self.exclude_improvised = tk.BooleanVar(value=False)
        self.dark_mode = False
        self.categories = []
        self.category_menu_entries = []
        self.flat_style = False  # for future
        self.setup_widgets()
        monitor_file_changes(self, self.current_file)

    
    def convert_to_flat_style(self):
        if not self.data or "economy-override" not in self.data:
            messagebox.showerror("Error", "No economy data loaded.")
            return
        try:
            nested = self.data["economy-override"]["traders"]
            flat = {}
            for key, items in nested.items():
                flat[key] = items
            self.data["economy-override"]["traders"] = flat
            self.flat_style = True
            self.populate_tree()
            messagebox.showinfo("Flat Style", "Converted to flat style format.")
        except Exception as e:
            messagebox.showerror("Conversion Error", str(e))



    def spread_edit(self):
        if not self.data:
            return
        win = tk.Toplevel(self.root)
        win.title("Spread Edit")
        win.geometry("500x300")

        tk.Label(win, text="Search tradeable-code:").pack()
        code_var = tk.StringVar()
        code_entry = tk.Entry(win, textvariable=code_var, width=40)
        code_entry.pack(pady=5)

        tk.Label(win, text="Field to Edit:").pack()
        field_var = tk.StringVar(value="base-purchase-price")
        ttk.Combobox(win, textvariable=field_var, values=[
            "base-purchase-price", "base-sell-price", "can-be-purchased"]).pack(pady=5)

        tk.Label(win, text="New Value:").pack()
        value_var = tk.StringVar()
        value_entry = tk.Entry(win, textvariable=value_var, width=30)
        value_entry.pack(pady=5)

        def apply_spread():
            code = code_var.get().strip()
            field = field_var.get()
            value = value_var.get().strip()
            if not code or not field or not value:
                messagebox.showerror("Input Error", "All fields must be filled.")
                return
            changed = 0
            for trader, items in self.data["economy-override"]["traders"].items():
                for item in items:
                    if item.get("tradeable-code", "").lower() == code.lower():
                        item[field] = value
                        changed += 1
            self.populate_tree()
            messagebox.showinfo("Spread Edit", f"{changed} items updated.")
            win.destroy()

        tk.Button(win, text="Apply Spread", command=apply_spread).pack(pady=10)


def setup_widgets(self):
        style = ttk.Style(self.root)
        self.set_theme(style)

        menubar = tk.Menu(self.root)
        file_menu = tk.Menu(menubar, tearoff=0)
        file_menu.add_command(label="Open JSON", command=self.load_json)
        file_menu.add_command(label="Save As...", command=self.save_json)
        file_menu.add_separator()
        file_menu.add_command(label="Convert to Flat Style", command=self.convert_to_flat, state="disabled")  # for future
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        menubar.add_cascade(label="File", menu=file_menu)

        edit_menu = tk.Menu(menubar, tearoff=0)
        edit_menu.add_command(label="Apply Global % Change", command=self.global_price_edit)
        menubar.add_cascade(label="Edit", menu=edit_menu)

        self.category_menu = tk.Menu(menubar, tearoff=0)
        self.category_menu.add_command(label="(Load a JSON file first)", state="disabled")
        menubar.add_cascade(label="Categories", menu=self.category_menu)

        view_menu = tk.Menu(menubar, tearoff=0)
        view_menu.add_checkbutton(label="Exclude Crafted", variable=self.exclude_crafted, command=self.populate_tree)
        view_menu.add_checkbutton(label="Exclude Improvised", variable=self.exclude_improvised, command=self.populate_tree)
        view_menu.add_separator()
        view_menu.add_command(label="Toggle Dark Mode", command=self.toggle_dark_mode)
        menubar.add_cascade(label="View", menu=view_menu)

        
        tools_menu = tk.Menu(menubar, tearoff=0)
        tools_menu.add_command(label="Launch CLI Mode", command=self.launch_cli_window)
        menubar.add_cascade(label="Tools", menu=tools_menu)

        self.root.config(menu=menubar)

        # LEFT: Treeview of traders
        self.left_frame = tk.Frame(self.root)
        self.left_frame.pack(side="left", fill="y")

        self.tree = ttk.Treeview(self.left_frame, height=34)
        self.tree.heading("#0", text="Outpost > Trader > Item")
        self.tree.pack(fill="y", expand=True)
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)

        # CENTER: JSON preview area
        self.center_frame = tk.Frame(self.root, padx=5)
        self.center_frame.pack(side="left", fill="both", expand=True)

        self.preview_label = tk.Label(self.center_frame, text="Selected Item JSON", anchor="w")
        self.preview_label.pack(anchor="nw")

        self.preview_text = tk.Text(self.center_frame, wrap="none", height=40, width=70)
        self.preview_text.pack(fill="both", expand=True)
        self.preview_text.configure(state="disabled", font=("Courier", 10), background="#2e2e2e", foreground="white")

        yscroll = ttk.Scrollbar(self.center_frame, orient="vertical", command=self.preview_text.yview)
        yscroll.pack(side="right", fill="y")
        self.preview_text.configure(yscrollcommand=yscroll.set)

        # RIGHT: Detail editor
        self.detail_frame = tk.Frame(self.root, padx=10, pady=10)
        self.detail_frame.pack(side="right", fill="y")

        self.fields = {}
        for i, label in enumerate(["tradeable-code", "base-purchase-price", "base-sell-price", "required-famepoints", "can-be-purchased"]):
            tk.Label(self.detail_frame, text=label).grid(row=i, column=0, sticky="w")
            entry = tk.Entry(self.detail_frame, width=30)
            entry.grid(row=i, column=1)
            self.fields[label] = entry

        tk.Button(self.detail_frame, text="Update Item", command=self.update_item).grid(row=6, column=0, columnspan=2, pady=10)

    def set_theme(self, style):
        if self.dark_mode:
            self.root.configure(bg="#1e1e1e")
            style.theme_use("clam")
            style.configure("Treeview", background="#2e2e2e", foreground="white", fieldbackground="#2e2e2e")
            style.map("Treeview", background=[("selected", "#444")])
        else:
            self.root.configure(bg="SystemButtonFace")
            style.theme_use("default")

    def toggle_dark_mode(self):
        self.dark_mode = not self.dark_mode
        self.set_theme(ttk.Style(self.root))

    def load_json(self):
        filepath = filedialog.askopenfilename(filetypes=[("JSON files", "*.json")])
        if not filepath:
            return
        with open(filepath, "r") as file:
            try:
                self.data = json.load(file)
                self.current_file = filepath
                self.flat_style = self.detect_flat_style()
                self.populate_tree()
                self.scan_categories()
                self.build_category_menu()
            except json.JSONDecodeError:
                messagebox.showerror("Error", "Invalid JSON file.")

    def detect_flat_style(self):
        # Detects if the loaded file is flat or nested.
        if "economy-override" in self.data and "traders" in self.data["economy-override"]:
            return False
        # Add better detection for future.
        return True

    def convert_to_flat(self):
        # Placeholder for future conversion to flat format.
        messagebox.showinfo("Coming Soon", "Flat style conversion will be available in a future update.")

    def scan_categories(self):
        """Scans all tradeable-codes and builds list of category candidates."""
        codes = []
        traders = self.data.get("economy-override", {}).get("traders", {})
        for items in traders.values():
            for item in items:
                code = item.get("tradeable-code", "")
                codes.append(code)
        prefixes, infixes, suffixes = Counter(), Counter(), Counter()
        for code in codes:
            parts = code.split("_")
            if len(parts) > 1:
                prefixes[parts[0]] += 1
                suffixes[parts[-1]] += 1
            for part in parts:
                infixes[part] += 1
        # Now rank all candidates (prefix, infix, suffix), deduplicate, and limit to most common
        cats = []
        for k, v in prefixes.items():
            cats.append( (v, f"{k}_*", "prefix", k) )
        for k, v in infixes.items():
            cats.append( (v, f"*{k}*", "infix", k) )
        for k, v in suffixes.items():
            cats.append( (v, f"*_{k}", "suffix", k) )
        # Sort by count, limit
        cats.sort(reverse=True)
        # Deduplicate on display key
        seen = set()
        out = []
        for count, label, match_type, raw in cats:
            if label in seen: continue
            seen.add(label)
            out.append( (label, match_type, raw, count) )
            if len(out) >= CATEGORY_LIMIT:
                break
        self.categories = out

    def build_category_menu(self):
        self.category_menu.delete(0, tk.END)
        if not self.categories:
            self.category_menu.add_command(label="No categories found", state="disabled")
            return
        for label, match_type, raw, count in self.categories:
            pretty = f"{label} ({count})"
            self.category_menu.add_command(
                label=pretty,
                command=lambda m=match_type, r=raw, c=label: self.category_batch_edit(m, r, c)
            )
        self.category_menu.add_separator()
        self.category_menu.add_command(label="Refresh Categories", command=self.scan_categories)

    def get_items_by_category(self, match_type, raw):
        """Return list of (trader_key, idx, item) tuples for matching items."""
        matches = []
        traders = self.data.get("economy-override", {}).get("traders", {})
        for trader_key, items in traders.items():
            for idx, item in enumerate(items):
                code = item.get("tradeable-code", "")
                if (
                    (match_type == "prefix" and code.startswith(raw + "_"))
                    or (match_type == "suffix" and code.endswith("_" + raw))
                    or (match_type == "infix" and raw in code)
                ):
                    matches.append( (trader_key, idx, item) )
        return matches

    def category_batch_edit(self, match_type, raw, label):
        matches = self.get_items_by_category(match_type, raw)
        count = len(matches)
        if count == 0:
            messagebox.showinfo("No Matches", f"No items found for category: {label}")
            return
        # Modal
        win = tk.Toplevel(self.root)
        win.title(f"Batch Edit: {label}")
        win.geometry("420x250")
        tk.Label(win, text=f"{count} items found for category: {label}", font=("Arial", 12, "bold")).pack(pady=10)

        # can-be-purchased batch
        purchase_var = tk.StringVar(value="nochange")
        fame_var = tk.StringVar(value="")

        fr = tk.Frame(win)
        fr.pack(pady=5)
        tk.Label(fr, text="Set can-be-purchased:").grid(row=0, column=0, sticky="e")
        ttk.Combobox(fr, textvariable=purchase_var, width=8, values=["nochange", "true", "false", "default"]).grid(row=0, column=1)

        tk.Label(fr, text="Set famepoints:").grid(row=1, column=0, sticky="e")
        tk.Entry(fr, textvariable=fame_var, width=10).grid(row=1, column=1)

        # Preview option
        def preview_items():
            items = [item['tradeable-code'] for _, _, item in matches]
            preview_win = tk.Toplevel(win)
            preview_win.title("Preview Matching Items")
            txt = tk.Text(preview_win, wrap="none", height=20, width=50)
            txt.pack(fill="both", expand=True)
            txt.insert("end", "\n".join(items))
            txt.configure(state="disabled")
            yscroll = ttk.Scrollbar(preview_win, orient="vertical", command=txt.yview)
            yscroll.pack(side="right", fill="y")
            txt.configure(yscrollcommand=yscroll.set)

        tk.Button(win, text="Preview Items", command=preview_items).pack(pady=3)

        def do_apply():
            purchase = purchase_var.get()
            fame = fame_var.get()
            if purchase not in ("nochange", "true", "false", "default"):
                messagebox.showerror("Invalid", "Select a valid can-be-purchased value.")
                return
            if fame and not (fame.lstrip("-").isdigit()):
                messagebox.showerror("Invalid", "Famepoints must be blank or an integer.")
                return
            if not messagebox.askyesno("Confirm", f"Apply these changes to {count} items in {label}?"):
                return
            changed = 0
            for trader_key, idx, item in matches:
                if purchase != "nochange":
                    item['can-be-purchased'] = purchase
                    changed += 1
                if fame:
                    item['required-famepoints'] = fame
                    changed += 1
            self.populate_tree()
            win.destroy()
            messagebox.showinfo("Done", f"Updated {count} items for category {label}.")

        tk.Button(win, text="Apply", command=do_apply).pack(pady=10)
        tk.Button(win, text="Cancel", command=win.destroy).pack(pady=1)

    def populate_tree(self):
        self.tree.delete(*self.tree.get_children())
        traders = self.data.get("economy-override", {}).get("traders", {})
        for trader_key, items in traders.items():
            outpost = trader_key.split("_")[0]
            trader = trader_key
            outpost_id = f"{outpost}"
            if not self.tree.exists(outpost_id):
                self.tree.insert("", "end", iid=outpost_id, text=outpost)
            trader_id = f"{trader}"
            self.tree.insert(outpost_id, "end", iid=trader_id, text=trader)
            for idx, item in enumerate(items):
                code = item.get("tradeable-code", "Unnamed")
                if self.category_filter:
                    if self.category_filter == 'Weapon_':
                        if not (code.startswith('Weapon_') and "Weapon_parts" not in code):
                            continue
                    elif self.category_filter not in code:
                        continue
                if self.exclude_crafted.get() and ("Crafted" in code or code.endswith("_Crafted")):
                    continue
                if self.exclude_improvised.get() and "Improvised" in code:
                    continue
                item_id = f"{trader}_{idx}"
                self.tree.insert(trader_id, "end", iid=item_id, text=code)

    def on_tree_select(self, event):
        selected = self.tree.selection()
        if not selected:
            return
        item_id = selected[0]
        parts = item_id.split("_")
        # Only proceed if the last part is actually an integer
        if not parts[-1].isdigit():
            return  # Skip outpost/trader nodes!
        trader_key = "_".join(parts[:-1])
        idx = int(parts[-1])
        item = self.data["economy-override"]["traders"].get(trader_key, [])[idx]
        for key in self.fields:
            self.fields[key].delete(0, tk.END)
            self.fields[key].insert(0, item.get(key, ""))
        # Update JSON preview
        self.preview_text.configure(state="normal")
        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(tk.END, json.dumps(item, indent=4))
        self.preview_text.configure(state="disabled")

    def update_item(self):
        selected = self.tree.selection()
        if not selected:
            return
        item_id = selected[0]
        parts = item_id.split("_")
        trader_key = "_".join(parts[:-1])
        idx = int(parts[-1])
        for key in self.fields:
            self.data["economy-override"]["traders"][trader_key][idx][key] = self.fields[key].get()
        messagebox.showinfo("Success", "Item updated.")
        self.on_tree_select(None)

    def save_json(self):
        if not self.data:
            return
        save_path = filedialog.asksaveasfilename(defaultextension=".json", filetypes=[("JSON files", "*.json")])
        if not save_path:
            return
        with open(save_path, "w") as file:
            json.dump(self.data, file, indent=4)
        messagebox.showinfo("Saved", f"File saved to {save_path}")

    def set_category_filter(self, category):
        self.category_filter = category
        self.populate_tree()

    def global_price_edit(self):
        if not self.data:
            return
        win = tk.Toplevel(self.root)
        win.title("Global % Price Editor")
        tk.Label(win, text="Field to Edit:").grid(row=0, column=0, sticky="e")
        field_var = tk.StringVar(value="base-purchase-price")
        ttk.Combobox(win, textvariable=field_var, values=["base-purchase-price", "base-sell-price"]).grid(row=0, column=1)
        tk.Label(win, text="Percentage Change (-99 to 100):").grid(row=1, column=0, sticky="e")
        percent_entry = tk.Entry(win)
        percent_entry.grid(row=1, column=1)

        def apply():
            try:
                percent = int(percent_entry.get())
                if not (-99 <= percent <= 100):
                    raise ValueError
            except ValueError:
                messagebox.showerror("Invalid", "Enter a number from -99 to 100.")
                return
            field = field_var.get()
            traders = self.data.get("economy-override", {}).get("traders", {})
            for trader_items in traders.values():
                for item in trader_items:
                    try:
                        val = float(item.get(field, "-1"))
                        if val in [-1, None]:
                            continue
                        new_val = val + (val * percent / 100)
                        item[field] = str(int(round(new_val)))
                    except Exception:
                        continue
            self.populate_tree()
            messagebox.showinfo("Done", f"{field} updated globally by {percent}%")
            win.destroy()

        tk.Button(win, text="Apply", command=apply).grid(row=2, column=0, columnspan=2, pady=10)

if __name__ == "__main__":
    root = tk.Tk()
    app = EconomyChooserApp(root)
    root.mainloop()




    def load_json_from_path(self, filepath):
        if not os.path.exists(filepath):
            messagebox.showerror("Error", f"File not found: {filepath}")
            return
        with open(filepath, "r") as file:
            try:
                self.data = json.load(file)
                self.current_file = filepath
                self.flat_style = self.detect_flat_style()
                self.populate_tree()
                self.scan_categories()
                self.build_category_menu()
            except json.JSONDecodeError:
                messagebox.showerror("Error", "Invalid JSON file.")
