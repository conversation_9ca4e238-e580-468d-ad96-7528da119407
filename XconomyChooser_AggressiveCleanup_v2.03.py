#!/usr/bin/env python3
"""
Aggressive Cleanup Script - Actually removes files from main directory
"""

import os
import shutil
from datetime import datetime

def aggressive_cleanup():
    """Remove archived files from main directory"""
    
    # Files that should remain in the current build
    keep_files = {
        # Core application files
        "scum_economy_gui_enhanced.py",
        "1_45c.py", 
        "enhanced_dialogs.py",
        "version_manager.py",
        "cleanup_and_archive.py",
        "aggressive_cleanup.py",
        
        # Configuration files
        "economyoverride.json",
        "custom_buckets.json", 
        "gui_layout_devbuild.json",
        "version_info.json",
        
        # Current documentation
        "README_CURRENT_VERSION.md",
        "README_GUI.md",
        "PROJECT_STATUS_SUMMARY.md",
        "XCONOMYCHOOSER_V2_01_RELEASE_NOTES.md",
        
        # Essential test files
        "cli_parity_check.py",
        "wiring_fix_verification.py", 
        "comprehensive_method_test.py",
        
        # Project files
        "AUGMENT XCONOMY.code-workspace"
    }
    
    # Directories to keep
    keep_dirs = {
        "backups",
        "releases",
        "logs", 
        "documentation",
        "__pycache__",
        "archived_files"
    }
    
    print("🧹 AGGRESSIVE CLEANUP - REMOVING ARCHIVED FILES")
    print("=" * 60)
    
    # Get all files in current directory
    all_files = [f for f in os.listdir(".") if os.path.isfile(f)]
    all_dirs = [d for d in os.listdir(".") if os.path.isdir(d)]
    
    files_to_remove = []
    dirs_to_remove = []
    
    # Identify files to remove
    for file in all_files:
        if file not in keep_files and not file.startswith('.'):
            files_to_remove.append(file)
    
    # Identify directories to remove  
    for directory in all_dirs:
        if directory not in keep_dirs and not directory.startswith('.'):
            dirs_to_remove.append(directory)
    
    print(f"📊 Analysis:")
    print(f"✅ Files to keep: {len(keep_files)}")
    print(f"❌ Files to remove: {len(files_to_remove)}")
    print(f"✅ Directories to keep: {len(keep_dirs)}")
    print(f"❌ Directories to remove: {len(dirs_to_remove)}")
    
    if not files_to_remove and not dirs_to_remove:
        print("\n✅ Directory is already clean!")
        return
    
    print(f"\n📋 FILES TO REMOVE:")
    for file in sorted(files_to_remove):
        print(f"  ❌ {file}")
    
    if dirs_to_remove:
        print(f"\n📂 DIRECTORIES TO REMOVE:")
        for directory in sorted(dirs_to_remove):
            print(f"  ❌ {directory}/")
    
    # Confirm removal
    response = input(f"\n⚠️  PERMANENTLY REMOVE {len(files_to_remove)} files and {len(dirs_to_remove)} directories? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ Cleanup cancelled")
        return
    
    # Remove files
    removed_files = 0
    for file in files_to_remove:
        try:
            os.remove(file)
            print(f"🗑️  Removed: {file}")
            removed_files += 1
        except Exception as e:
            print(f"❌ Failed to remove {file}: {e}")
    
    # Remove directories
    removed_dirs = 0
    for directory in dirs_to_remove:
        try:
            shutil.rmtree(directory)
            print(f"🗑️  Removed directory: {directory}/")
            removed_dirs += 1
        except Exception as e:
            print(f"❌ Failed to remove {directory}: {e}")
    
    print(f"\n✅ CLEANUP COMPLETE!")
    print(f"🗑️  Removed {removed_files} files")
    print(f"🗑️  Removed {removed_dirs} directories")
    print(f"✅ {len(keep_files)} essential files remain")
    
    # Show final directory structure
    print(f"\n📁 FINAL DIRECTORY STRUCTURE:")
    remaining_files = [f for f in os.listdir(".") if os.path.isfile(f)]
    remaining_dirs = [d for d in os.listdir(".") if os.path.isdir(d)]
    
    print(f"📄 Files ({len(remaining_files)}):")
    for file in sorted(remaining_files):
        if not file.startswith('.'):
            print(f"  ✅ {file}")
    
    print(f"\n📂 Directories ({len(remaining_dirs)}):")
    for directory in sorted(remaining_dirs):
        if not directory.startswith('.'):
            print(f"  ✅ {directory}/")

if __name__ == "__main__":
    aggressive_cleanup()
