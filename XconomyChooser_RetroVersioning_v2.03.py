#!/usr/bin/env python3
"""
Retroactive Versioning and Cleanup System
Fixes file naming, updates backups, and cleans workspace
"""

import os
import shutil
import json
import zipfile
from datetime import datetime

class RetroactiveVersionManager:
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Proper version mapping for files
        self.version_mapping = {
            # Current files -> Proper versioned names
            "scum_economy_gui_enhanced.py": "XconomyChooser_Enhanced_GUI_v2.03.py",
            "1_45c.py": "XconomyChooser_CLI_v1.45c.py",
            "enhanced_dialogs.py": "XconomyChooser_Dialogs_v2.03.py",
            "version_manager.py": "XconomyChooser_VersionManager_v2.03.py",
            "cleanup_and_archive.py": "XconomyChooser_Cleanup_v2.03.py",
            "aggressive_cleanup.py": "XconomyChooser_AggressiveCleanup_v2.03.py",
            "retroactive_versioning_cleanup.py": "XconomyChooser_RetroVersioning_v2.03.py"
        }
        
        # Historic version mapping for backups
        self.historic_versions = {
            "1_45c.py": "v1.45c",
            "XconomyChooser_v1_71k_01_Launchable.py": "v1.71k",
            "XconomyChooser_v1_72_r2_batchundo.py": "v1.72",
            "scum_economy_gui_enhanced.py": "v2.03"
        }
        
        # Files to keep (SCUM Economy Chooser related)
        self.keep_files = {
            # Core application files
            "scum_economy_gui_enhanced.py",
            "1_45c.py", 
            "enhanced_dialogs.py",
            "version_manager.py",
            "cleanup_and_archive.py",
            "aggressive_cleanup.py",
            "retroactive_versioning_cleanup.py",
            
            # Configuration files
            "economyoverride.json",
            "custom_buckets.json",
            "gui_layout_devbuild.json", 
            "version_info.json",
            
            # Documentation
            "README_CURRENT_VERSION.md",
            "README_GUI.md",
            "PROJECT_STATUS_SUMMARY.md",
            "XCONOMYCHOOSER_V2_01_RELEASE_NOTES.md",
            
            # Test files
            "cli_parity_check.py",
            "wiring_fix_verification.py",
            "comprehensive_method_test.py",
            
            # Project files
            "AUGMENT XCONOMY.code-workspace"
        }
        
        # Directories to keep
        self.keep_dirs = {
            "backups",
            "releases", 
            "logs",
            "documentation",
            "archived_files",
            "__pycache__"
        }

    def retroactive_version_files(self):
        """Apply proper versioning to current files"""
        print("🔄 APPLYING RETROACTIVE VERSIONING")
        print("=" * 50)
        
        versioned_files = {}
        
        for current_name, versioned_name in self.version_mapping.items():
            if os.path.exists(current_name):
                # Create versioned copy
                shutil.copy2(current_name, versioned_name)
                versioned_files[current_name] = versioned_name
                print(f"✅ {current_name} → {versioned_name}")
        
        return versioned_files

    def update_historic_backups(self):
        """Update historic backups with proper versioning"""
        print("\n🕰️ UPDATING HISTORIC BACKUPS")
        print("-" * 30)
        
        backup_dir = "backups"
        if not os.path.exists(backup_dir):
            print("❌ No backups directory found")
            return
        
        updated_backups = []
        
        # Update existing backups
        for backup_folder in os.listdir(backup_dir):
            backup_path = os.path.join(backup_dir, backup_folder)
            if os.path.isdir(backup_path) and backup_folder.startswith("XconomyChooser_"):
                
                # Update files in backup with proper versioning
                for file in os.listdir(backup_path):
                    file_path = os.path.join(backup_path, file)
                    if os.path.isfile(file_path) and file in self.version_mapping:
                        versioned_name = self.version_mapping[file]
                        versioned_path = os.path.join(backup_path, versioned_name)
                        
                        # Copy with versioned name
                        shutil.copy2(file_path, versioned_path)
                        print(f"  📁 {backup_folder}: {file} → {versioned_name}")
                
                updated_backups.append(backup_folder)
        
        return updated_backups

    def create_versioned_release(self):
        """Create properly versioned release package"""
        print("\n📦 CREATING VERSIONED RELEASE")
        print("-" * 30)
        
        release_name = f"XconomyChooser_Enhanced_v2.03_Properly_Versioned_{self.timestamp}"
        release_dir = os.path.join("releases", release_name)
        os.makedirs(release_dir, exist_ok=True)
        
        # Core application files with proper names
        core_dir = os.path.join(release_dir, "core")
        os.makedirs(core_dir, exist_ok=True)
        
        for current_name, versioned_name in self.version_mapping.items():
            if os.path.exists(current_name):
                shutil.copy2(current_name, os.path.join(core_dir, versioned_name))
        
        # Configuration files
        config_dir = os.path.join(release_dir, "config")
        os.makedirs(config_dir, exist_ok=True)
        
        config_files = ["economyoverride.json", "custom_buckets.json", "gui_layout_devbuild.json", "version_info.json"]
        for config_file in config_files:
            if os.path.exists(config_file):
                shutil.copy2(config_file, config_dir)
        
        # Documentation
        docs_dir = os.path.join(release_dir, "documentation")
        os.makedirs(docs_dir, exist_ok=True)
        
        doc_files = ["README_CURRENT_VERSION.md", "README_GUI.md", "PROJECT_STATUS_SUMMARY.md"]
        for doc_file in doc_files:
            if os.path.exists(doc_file):
                shutil.copy2(doc_file, docs_dir)
        
        # Create versioned README
        self.create_versioned_readme(release_dir)
        
        # Create ZIP
        zip_path = f"{release_dir}.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(release_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, release_dir)
                    zipf.write(file_path, arc_path)
        
        print(f"✅ Versioned release created: {zip_path}")
        return zip_path

    def create_versioned_readme(self, release_dir):
        """Create README with proper version information"""
        readme_content = f"""# SCUM Economy Chooser Enhanced GUI v2.03 - Properly Versioned

## 🎯 Properly Versioned Release

**Version:** 2.03  
**Release Date:** {datetime.now().strftime("%Y-%m-%d")}  
**Status:** Production Ready with Proper File Versioning  

## 📁 File Structure (Properly Versioned)

### Core Application Files:
- `XconomyChooser_Enhanced_GUI_v2.03.py` - Main enhanced GUI application
- `XconomyChooser_CLI_v1.45c.py` - Original CLI version (preserved)
- `XconomyChooser_Dialogs_v2.03.py` - Enhanced dialog components
- `XconomyChooser_VersionManager_v2.03.py` - Version management system
- `XconomyChooser_Cleanup_v2.03.py` - Cleanup and archiving tools

### Configuration Files:
- `economyoverride.json` - Sample SCUM economy data
- `custom_buckets.json` - Custom bucket configurations
- `gui_layout_devbuild.json` - GUI layout settings
- `version_info.json` - Version tracking information

### Documentation:
- `README_CURRENT_VERSION.md` - Current version guide
- `README_GUI.md` - GUI documentation
- `PROJECT_STATUS_SUMMARY.md` - Complete project status

## 🚀 Quick Start

### Run Enhanced GUI:
```bash
python XconomyChooser_Enhanced_GUI_v2.03.py
```

### Run Original CLI:
```bash
python XconomyChooser_CLI_v1.45c.py
```

## ✅ Features

### Complete CLI Parity:
- All CLI functions available in Normal mode
- Enhanced user interface with colored buttons
- Professional validation and safety features

### Enhanced GUI Features:
- 🎨 Colored icons and buttons throughout interface
- 🪣 Multiple custom bucket windows
- 📂 Essential categories with smart filtering
- 🔧 F.I.S.H. Logic categorization system
- 💾 Automatic backup and version management

### Professional Tools:
- 🔄 Version management with historic backups
- 📦 Release packaging with proper versioning
- 🧹 Cleanup and archiving systems
- 📊 Change tracking and undo/redo

## 🎯 Version History

- **v1.45c** - Original CLI foundation
- **v1.71k** - Early GUI development
- **v1.72** - Advanced features and batch operations
- **v2.00** - Enhanced GUI with F.I.S.H. Logic
- **v2.01** - CLI parity achievement
- **v2.02** - Wiring fixes and improvements
- **v2.03** - Colored interface and enhanced buckets

---
**SCUM Economy Chooser Enhanced GUI v2.03**  
*Properly versioned and production ready*
"""
        
        with open(os.path.join(release_dir, "README.md"), 'w', encoding='utf-8') as f:
            f.write(readme_content)

    def cleanup_workspace(self):
        """Clean up workspace and archive non-related files"""
        print("\n🧹 CLEANING UP WORKSPACE")
        print("-" * 30)
        
        # Get all files and directories
        all_files = [f for f in os.listdir(".") if os.path.isfile(f)]
        all_dirs = [d for d in os.listdir(".") if os.path.isdir(d)]
        
        # Files to archive
        archive_files = []
        for file in all_files:
            if file not in self.keep_files and not file.startswith('.') and not file.endswith('.zip'):
                archive_files.append(file)
        
        # Directories to archive
        archive_dirs = []
        for directory in all_dirs:
            if directory not in self.keep_dirs and not directory.startswith('.'):
                archive_dirs.append(directory)
        
        if not archive_files and not archive_dirs:
            print("✅ Workspace is already clean!")
            return
        
        # Create archive
        archive_name = f"Non_SCUM_Economy_Files_{self.timestamp}"
        archive_dir = os.path.join("archived_files", archive_name)
        os.makedirs(archive_dir, exist_ok=True)
        
        # Archive files
        for file in archive_files:
            shutil.copy2(file, archive_dir)
            print(f"📦 Archived: {file}")
        
        # Archive directories
        for directory in archive_dirs:
            dest_dir = os.path.join(archive_dir, directory)
            shutil.copytree(directory, dest_dir)
            print(f"📦 Archived directory: {directory}/")
        
        # Create ZIP of archived content
        zip_path = f"{archive_dir}.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(archive_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, archive_dir)
                    zipf.write(file_path, arc_path)
        
        # Remove archived files from workspace
        response = input(f"\n⚠️ Remove {len(archive_files)} files and {len(archive_dirs)} directories from workspace? (y/N): ").strip().lower()
        if response == 'y':
            for file in archive_files:
                try:
                    os.remove(file)
                    print(f"🗑️ Removed: {file}")
                except Exception as e:
                    print(f"❌ Failed to remove {file}: {e}")
            
            for directory in archive_dirs:
                try:
                    shutil.rmtree(directory)
                    print(f"🗑️ Removed directory: {directory}/")
                except Exception as e:
                    print(f"❌ Failed to remove {directory}: {e}")
        
        print(f"✅ Archive created: {zip_path}")
        return zip_path

    def run_complete_versioning_cleanup(self):
        """Run complete versioning and cleanup process"""
        print("🔧 SCUM ECONOMY CHOOSER - RETROACTIVE VERSIONING & CLEANUP")
        print("=" * 70)
        
        # Step 1: Apply retroactive versioning
        versioned_files = self.retroactive_version_files()
        
        # Step 2: Update historic backups
        updated_backups = self.update_historic_backups()
        
        # Step 3: Create properly versioned release
        versioned_release = self.create_versioned_release()
        
        # Step 4: Clean up workspace
        archive_zip = self.cleanup_workspace()
        
        # Summary
        print(f"\n🎉 RETROACTIVE VERSIONING & CLEANUP COMPLETE!")
        print(f"✅ Versioned {len(versioned_files)} current files")
        print(f"✅ Updated {len(updated_backups)} historic backups")
        print(f"✅ Created versioned release: {os.path.basename(versioned_release)}")
        if archive_zip:
            print(f"✅ Archived non-related files: {os.path.basename(archive_zip)}")
        
        print(f"\n📁 CLEAN WORKSPACE STRUCTURE:")
        remaining_files = [f for f in os.listdir(".") if os.path.isfile(f) and not f.startswith('.')]
        remaining_dirs = [d for d in os.listdir(".") if os.path.isdir(d) and not d.startswith('.')]
        
        print(f"📄 Files ({len(remaining_files)}):")
        for file in sorted(remaining_files):
            print(f"  ✅ {file}")
        
        print(f"\n📂 Directories ({len(remaining_dirs)}):")
        for directory in sorted(remaining_dirs):
            print(f"  ✅ {directory}/")
        
        print(f"\n🎯 SCUM Economy Chooser is now properly versioned and workspace is clean!")

def main():
    manager = RetroactiveVersionManager()
    manager.run_complete_versioning_cleanup()

if __name__ == "__main__":
    main()
