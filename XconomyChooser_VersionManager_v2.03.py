#!/usr/bin/env python3
"""
Version Management System for SCUM Economy Chooser Enhanced GUI
Handles version numbering, backups, and release packaging
"""

import os
import shutil
import json
import zipfile
from datetime import datetime
import subprocess

class VersionManager:
    def __init__(self):
        self.current_version = "2.01"  # XconomyChooser Enhanced GUI v2.01
        self.version_file = "version_info.json"
        self.backup_dir = "backups"
        self.releases_dir = "releases"
        self.docs_dir = "documentation"
        
        # Ensure directories exist
        for directory in [self.backup_dir, self.releases_dir, self.docs_dir]:
            os.makedirs(directory, exist_ok=True)
    
    def get_version_info(self):
        """Get current version information"""
        if os.path.exists(self.version_file):
            with open(self.version_file, 'r') as f:
                return json.load(f)
        else:
            return self.create_initial_version_info()
    
    def create_initial_version_info(self):
        """Create initial version information"""
        version_info = {
            "current_version": self.current_version,
            "build_number": 1,
            "release_date": datetime.now().strftime("%Y-%m-%d"),
            "version_history": [
                {
                    "version": "2.01",
                    "date": datetime.now().strftime("%Y-%m-%d"),
                    "description": "Enhanced GUI with CLI parity, F.I.S.H. Logic, and custom buckets",
                    "features": [
                        "Complete CLI function parity in Normal mode",
                        "F.I.S.H. Logic categorization system",
                        "Custom buckets with drag-and-drop",
                        "Advanced editing modes",
                        "Visual change tracking",
                        "Undo/Redo system",
                        "Database integration ready"
                    ],
                    "fixes": [
                        "Fixed JSON structure wiring issues",
                        "Corrected category search functionality",
                        "Improved item detection and filtering",
                        "Enhanced error handling and validation"
                    ]
                }
            ]
        }
        
        with open(self.version_file, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, indent=4, ensure_ascii=False)

        return version_info
    
    def increment_version(self, version_type="patch"):
        """Increment version number"""
        version_info = self.get_version_info()
        current = version_info["current_version"]
        
        # Parse current version (e.g., "2.01" -> [2, 1])
        parts = current.split(".")
        major = int(parts[0])
        minor = int(parts[1]) if len(parts) > 1 else 0
        
        if version_type == "major":
            major += 1
            minor = 0
        elif version_type == "minor":
            minor += 1
        else:  # patch
            minor += 1
        
        new_version = f"{major}.{minor:02d}"
        
        version_info["current_version"] = new_version
        version_info["build_number"] += 1
        version_info["release_date"] = datetime.now().strftime("%Y-%m-%d")
        
        with open(self.version_file, 'w', encoding='utf-8') as f:
            json.dump(version_info, f, indent=4, ensure_ascii=False)
        
        return new_version
    
    def create_backup(self, description="Manual backup"):
        """Create a backup of current state"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        version_info = self.get_version_info()
        current_version = version_info["current_version"]
        
        backup_name = f"XconomyChooser_v{current_version}_backup_{timestamp}"
        backup_path = os.path.join(self.backup_dir, backup_name)
        
        # Create backup directory
        os.makedirs(backup_path, exist_ok=True)
        
        # Files to backup
        files_to_backup = [
            "scum_economy_gui_enhanced.py",
            "1_45c.py",
            "enhanced_dialogs.py",
            "economyoverride.json",
            "custom_buckets.json",
            "gui_layout_devbuild.json",
            "version_info.json"
        ]
        
        # Copy files
        for file in files_to_backup:
            if os.path.exists(file):
                shutil.copy2(file, backup_path)
        
        # Copy documentation
        docs_to_backup = [
            "README_GUI.md",
            "XCONOMYCHOOSER_V2_01_RELEASE_NOTES.md",
            "ENHANCED_GUI_SUMMARY.md",
            "FINAL_WIRING_VERIFICATION.md"
        ]
        
        for doc in docs_to_backup:
            if os.path.exists(doc):
                shutil.copy2(doc, backup_path)
        
        # Create backup info file
        backup_info = {
            "version": current_version,
            "timestamp": timestamp,
            "description": description,
            "files_backed_up": files_to_backup + docs_to_backup
        }
        
        with open(os.path.join(backup_path, "backup_info.json"), 'w', encoding='utf-8') as f:
            json.dump(backup_info, f, indent=4, ensure_ascii=False)
        
        print(f"✅ Backup created: {backup_path}")
        return backup_path
    
    def create_release_package(self, version_type="patch"):
        """Create a complete release package"""
        # Increment version
        new_version = self.increment_version(version_type)
        
        # Create backup first
        backup_path = self.create_backup(f"Pre-release backup for v{new_version}")
        
        # Create release package
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        release_name = f"XconomyChooser_Enhanced_v{new_version}_{timestamp}"
        release_path = os.path.join(self.releases_dir, release_name)
        
        # Create release directory
        os.makedirs(release_path, exist_ok=True)
        
        # Core files
        core_files = {
            "scum_economy_gui_enhanced.py": "XconomyChooser_Enhanced_GUI.py",
            "1_45c.py": "XconomyChooser_CLI.py",
            "enhanced_dialogs.py": "enhanced_dialogs.py"
        }
        
        # Copy and rename core files
        for source, target in core_files.items():
            if os.path.exists(source):
                shutil.copy2(source, os.path.join(release_path, target))
        
        # Configuration files
        config_files = [
            "economyoverride.json",
            "custom_buckets.json",
            "gui_layout_devbuild.json"
        ]
        
        config_dir = os.path.join(release_path, "config")
        os.makedirs(config_dir, exist_ok=True)
        
        for config in config_files:
            if os.path.exists(config):
                shutil.copy2(config, config_dir)
        
        # Documentation
        docs = [
            "README_GUI.md",
            "XCONOMYCHOOSER_V2_01_RELEASE_NOTES.md",
            "ENHANCED_GUI_SUMMARY.md"
        ]
        
        docs_dir = os.path.join(release_path, "documentation")
        os.makedirs(docs_dir, exist_ok=True)
        
        for doc in docs:
            if os.path.exists(doc):
                shutil.copy2(doc, docs_dir)
        
        # Create release README
        self.create_release_readme(release_path, new_version)
        
        # Create ZIP package
        zip_path = f"{release_path}.zip"
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(release_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, release_path)
                    zipf.write(file_path, arc_path)
        
        print(f"✅ Release package created: {zip_path}")
        return zip_path, new_version
    
    def create_historic_backups(self):
        """Create historic backups based on version history and existing files"""
        print("🕰️ Creating Historic Backups...")

        # Check for historic versions in the "Xconomy resource and code ideas" directory
        historic_dir = "Xconomy resource and code ideas"
        if not os.path.exists(historic_dir):
            print("❌ Historic directory not found")
            return

        # Define historic versions we can reconstruct
        historic_versions = {
            "1.45c": {
                "files": ["1_45c.py"],
                "description": "Original CLI version - Foundation",
                "date": "2025-06-09"
            },
            "1.71k": {
                "files": ["XconomyChooser_v1_71k_01_Launchable.py"],
                "description": "Early GUI development phase",
                "date": "2025-06-09"
            },
            "1.72": {
                "files": ["XconomyChooser_v1_72_r2_batchundo.py"],
                "description": "Batch undo functionality added",
                "date": "2025-06-09"
            },
            "2.00": {
                "files": ["scum_economy_gui_enhanced.py"],
                "description": "Enhanced GUI with F.I.S.H. Logic (pre-wiring fixes)",
                "date": "2025-06-10"
            }
        }

        historic_backup_dir = os.path.join(self.backup_dir, "historic")
        os.makedirs(historic_backup_dir, exist_ok=True)

        created_backups = []

        for version, info in historic_versions.items():
            backup_name = f"Historic_v{version}_{info['date'].replace('-', '')}"
            backup_path = os.path.join(historic_backup_dir, backup_name)

            if os.path.exists(backup_path):
                print(f"⏭️ Historic backup v{version} already exists")
                continue

            os.makedirs(backup_path, exist_ok=True)
            files_found = []

            # Look for files in historic directory
            for filename in info['files']:
                # Check multiple possible locations
                possible_paths = [
                    filename,  # Current directory
                    os.path.join(historic_dir, filename),  # Historic directory
                    os.path.join(historic_dir, f"{filename}")  # Direct in historic
                ]

                for path in possible_paths:
                    if os.path.exists(path):
                        shutil.copy2(path, backup_path)
                        files_found.append(filename)
                        print(f"  📁 Found and backed up: {filename}")
                        break

            # Create historic backup info
            historic_info = {
                "version": version,
                "date": info['date'],
                "description": info['description'],
                "files_backed_up": files_found,
                "backup_type": "historic_reconstruction",
                "created_on": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            with open(os.path.join(backup_path, "historic_backup_info.json"), 'w', encoding='utf-8') as f:
                json.dump(historic_info, f, indent=4, ensure_ascii=False)

            if files_found:
                created_backups.append(f"v{version}")
                print(f"✅ Historic backup created: v{version}")
            else:
                # Remove empty directory
                os.rmdir(backup_path)
                print(f"❌ No files found for v{version}")

        if created_backups:
            print(f"\n🎉 Historic backups created for versions: {', '.join(created_backups)}")
        else:
            print("\n⚠️ No historic backups could be created")

        return created_backups

    def create_version_timeline_backup(self):
        """Create a comprehensive timeline backup of all versions"""
        timeline_dir = os.path.join(self.backup_dir, "timeline")
        os.makedirs(timeline_dir, exist_ok=True)

        # Create timeline backup
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        timeline_backup = os.path.join(timeline_dir, f"Version_Timeline_{timestamp}")
        os.makedirs(timeline_backup, exist_ok=True)

        # Copy all existing backups
        if os.path.exists(self.backup_dir):
            for item in os.listdir(self.backup_dir):
                if item != "timeline" and os.path.isdir(os.path.join(self.backup_dir, item)):
                    src = os.path.join(self.backup_dir, item)
                    dst = os.path.join(timeline_backup, item)
                    shutil.copytree(src, dst)

        # Create timeline documentation
        timeline_doc = self.create_timeline_documentation()
        with open(os.path.join(timeline_backup, "VERSION_TIMELINE.md"), 'w', encoding='utf-8') as f:
            f.write(timeline_doc)

        print(f"✅ Timeline backup created: {timeline_backup}")
        return timeline_backup

    def create_timeline_documentation(self):
        """Create comprehensive timeline documentation"""
        version_info = self.get_version_info()

        doc = f"""# SCUM Economy Chooser - Version Timeline

## 📊 Project Evolution Timeline

### 🎯 Current Status
- **Latest Version:** {version_info['current_version']}
- **Build Number:** {version_info['build_number']}
- **Last Updated:** {version_info['release_date']}

## 🕰️ Historic Version Timeline

### Phase 1: CLI Foundation (v1.45c)
- **Date:** June 9, 2025
- **Description:** Original CLI version with core functionality
- **Key Features:**
  - Command-line interface
  - Basic economy editing
  - Price adjustment capabilities
  - File validation and safety checks

### Phase 2: Early GUI Development (v1.71k)
- **Date:** June 9, 2025
- **Description:** First GUI implementation attempts
- **Key Features:**
  - Basic tkinter interface
  - GUI wrapper around CLI functions
  - Early user interface experiments

### Phase 3: Advanced Features (v1.72)
- **Date:** June 9, 2025
- **Description:** Enhanced functionality and batch operations
- **Key Features:**
  - Batch undo functionality
  - Improved error handling
  - Enhanced user experience

### Phase 4: Enhanced GUI Era (v2.00+)
- **Date:** June 10, 2025
- **Description:** Complete GUI rewrite with advanced features
- **Key Features:**
  - F.I.S.H. Logic system
  - Custom buckets functionality
  - Visual change tracking
  - Complete CLI parity

## 🔧 Major Milestones

### ✅ CLI Parity Achievement (v2.01)
- All CLI functions available in Normal mode
- Enhanced user interface with console-style buttons
- Perfect feature alignment between CLI and GUI

### ✅ Wiring Fixes (v2.02-2.03)
- Fixed JSON structure access issues
- Corrected category search functionality
- Implemented drag-and-drop for custom buckets
- Added comprehensive item list displays

### ✅ Version Management (v2.03)
- Automated backup system
- Release package generation
- Version history tracking
- Professional documentation

## 📁 File Evolution

### Core Files Timeline:
1. **1_45c.py** - Original CLI (preserved)
2. **XconomyChooser_v1_71k_*.py** - Early GUI attempts
3. **XconomyChooser_v1_72_*.py** - Advanced features
4. **scum_economy_gui_enhanced.py** - Current enhanced GUI

### Configuration Evolution:
1. **Basic JSON handling** - Simple file operations
2. **Enhanced validation** - CLI-compatible safety rules
3. **Custom buckets** - User-defined item grouping
4. **F.I.S.H. Logic** - Intelligent categorization

## 🎯 Future Roadmap

### Planned Enhancements:
- Database integration completion
- Heat map visualization
- Build mode with resizable frames
- Additional F.I.S.H. categories

---
**Generated:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
**Version Manager:** SCUM Economy Chooser Enhanced GUI
"""
        return doc

    def create_release_readme(self, release_path, version):
        """Create README for release package"""
        readme_content = f"""# SCUM Economy Chooser Enhanced GUI v{version}

## 🎮 What's New in Version {version}

### ✅ Complete CLI Parity
- All CLI functions available in Normal mode
- Enhanced user-friendly interface
- Professional tools in Advanced mode

### 🐟 F.I.S.H. Logic System
- Flexible Intelligence for Scenario Handling
- Smart item categorization
- Advanced filtering and analysis

### 🪣 Custom Buckets
- Create custom item groups
- Drag-and-drop functionality
- Save and manage bucket configurations

### 🔧 Enhanced Features
- Visual change tracking
- Undo/Redo system
- Database integration ready
- Improved error handling

## 📁 Package Contents

### Core Files
- `XconomyChooser_Enhanced_GUI.py` - Main enhanced GUI application
- `XconomyChooser_CLI.py` - Original CLI version (1_45c.py)
- `enhanced_dialogs.py` - Enhanced dialog components

### Configuration
- `config/economyoverride.json` - Sample economy file
- `config/custom_buckets.json` - Custom bucket configurations
- `config/gui_layout_devbuild.json` - GUI layout settings

### Documentation
- `documentation/` - Complete documentation and guides

## 🚀 Quick Start

1. **Run Enhanced GUI**: `python XconomyChooser_Enhanced_GUI.py`
2. **Run CLI Version**: `python XconomyChooser_CLI.py`
3. **Load Economy File**: Use File > Load to open your economyoverride.json
4. **Start Editing**: Choose tools from the left panel

## 🎯 Mode Selection

### Normal Mode (Recommended)
- All CLI functions available
- Essential categories
- Custom buckets
- User-friendly interface

### Advanced Mode (Expert Users)
- F.I.S.H. Logic tools
- Professional analytics
- Advanced bucket rules
- Full feature access

## 🛡️ Safety Features

- Automatic backups before changes
- CLI-compatible validation
- Undo/Redo system
- Change preview and tracking

## 📋 System Requirements

- Python 3.8+
- CustomTkinter library
- Windows/Linux/Mac compatible

## 🔧 Installation

```bash
pip install customtkinter
python XconomyChooser_Enhanced_GUI.py
```

## 📞 Support

For issues, feature requests, or questions, refer to the documentation
or check the project repository.

---
**SCUM Economy Chooser Enhanced GUI v{version}**  
*Complete CLI parity with enhanced user experience*
"""
        
        with open(os.path.join(release_path, "README.md"), 'w', encoding='utf-8') as f:
            f.write(readme_content)

def main():
    """Main version management interface"""
    vm = VersionManager()
    
    print("🔧 SCUM Economy Chooser - Version Manager")
    print("=" * 50)
    
    version_info = vm.get_version_info()
    print(f"📊 Current Version: {version_info['current_version']}")
    print(f"🔢 Build Number: {version_info['build_number']}")
    print(f"📅 Release Date: {version_info['release_date']}")
    
    print("\n🎯 Available Actions:")
    print("1. Create Backup")
    print("2. Create Release Package (Patch)")
    print("3. Create Release Package (Minor)")
    print("4. Create Release Package (Major)")
    print("5. View Version History")
    print("6. 🕰️ Create Historic Backups")
    print("7. 📚 Create Timeline Backup")
    print("8. Exit")
    
    choice = input("\n👉 Select action (1-8): ").strip()
    
    if choice == "1":
        description = input("📝 Backup description (optional): ").strip()
        if not description:
            description = "Manual backup"
        vm.create_backup(description)
    
    elif choice == "2":
        zip_path, version = vm.create_release_package("patch")
        print(f"🎉 Release v{version} created: {zip_path}")
    
    elif choice == "3":
        zip_path, version = vm.create_release_package("minor")
        print(f"🎉 Release v{version} created: {zip_path}")
    
    elif choice == "4":
        zip_path, version = vm.create_release_package("major")
        print(f"🎉 Release v{version} created: {zip_path}")
    
    elif choice == "5":
        print("\n📚 Version History:")
        for entry in version_info["version_history"]:
            print(f"\n🔖 Version {entry['version']} ({entry['date']})")
            print(f"   📝 {entry['description']}")
            if 'features' in entry:
                print("   ✨ Features:")
                for feature in entry['features']:
                    print(f"      • {feature}")
    
    elif choice == "6":
        created_backups = vm.create_historic_backups()
        if created_backups:
            print(f"🎉 Created historic backups for: {', '.join(created_backups)}")
        else:
            print("⚠️ No historic files found to backup")

    elif choice == "7":
        timeline_path = vm.create_version_timeline_backup()
        print(f"📚 Complete timeline backup created: {timeline_path}")

    elif choice == "8":
        print("👋 Goodbye!")

    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()
