import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import sys
import subprocess
import threading
import time
from datetime import datetime
import logging

# Import the original CLI functions
from importlib import import_module
import importlib.util

class SCUMEconomyGUI:
    def __init__(self):
        # Set appearance mode and color theme
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")

        # Create main window
        self.root = ctk.CTk()
        self.root.title("SCUM Economy Chooser - Advanced GUI")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)

        # Initialize variables
        self.current_data = None
        self.current_filename = None
        self.cli_module = None

        # Load CLI module
        self.load_cli_module()

        # Create GUI components
        self.create_main_interface()

    def load_cli_module(self):
        """Load the original CLI module for integration"""
        try:
            spec = importlib.util.spec_from_file_location("cli_module", "1_45c.py")
            self.cli_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(self.cli_module)
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load CLI module: {str(e)}")

    def create_main_interface(self):
        """Create the main GUI interface"""
        # Create main container
        self.main_container = ctk.CTkFrame(self.root)
        self.main_container.pack(fill="both", expand=True, padx=10, pady=10)

        # Create header
        self.create_header()

        # Create file management section
        self.create_file_section()

        # Create main menu section
        self.create_main_menu()

        # Create status bar
        self.create_status_bar()

    def create_header(self):
        """Create the application header"""
        header_frame = ctk.CTkFrame(self.main_container)
        header_frame.pack(fill="x", padx=5, pady=5)

        # Title
        title_label = ctk.CTkLabel(
            header_frame,
            text="SCUM Economy Chooser - Advanced GUI",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=10)

        # Version info
        version_label = ctk.CTkLabel(
            header_frame,
            text="Build v1.45b - Advanced GUI Edition",
            font=ctk.CTkFont(size=12)
        )
        version_label.pack()

        # CLI Access button
        cli_button = ctk.CTkButton(
            header_frame,
            text="Open CLI Version",
            command=self.open_cli_popup,
            width=150
        )
        cli_button.pack(pady=5)

    def create_file_section(self):
        """Create file management section"""
        file_frame = ctk.CTkFrame(self.main_container)
        file_frame.pack(fill="x", padx=5, pady=5)

        # File section title
        file_title = ctk.CTkLabel(
            file_frame,
            text="File Management",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        file_title.pack(pady=5)

        # File selection frame
        file_select_frame = ctk.CTkFrame(file_frame)
        file_select_frame.pack(fill="x", padx=10, pady=5)

        # Current file label
        self.current_file_label = ctk.CTkLabel(
            file_select_frame,
            text="No file loaded",
            font=ctk.CTkFont(size=12)
        )
        self.current_file_label.pack(side="left", padx=10)

        # File buttons
        button_frame = ctk.CTkFrame(file_select_frame)
        button_frame.pack(side="right", padx=10)

        load_button = ctk.CTkButton(
            button_frame,
            text="Load JSON File",
            command=self.load_json_file,
            width=120
        )
        load_button.pack(side="left", padx=5)

        reload_button = ctk.CTkButton(
            button_frame,
            text="Reload",
            command=self.reload_file,
            width=80
        )
        reload_button.pack(side="left", padx=5)

    def create_main_menu(self):
        """Create the main menu with buttons"""
        menu_frame = ctk.CTkFrame(self.main_container)
        menu_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Menu title
        menu_title = ctk.CTkLabel(
            menu_frame,
            text="Economy Editor Tools",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        menu_title.pack(pady=10)

        # Create button grid
        self.create_menu_buttons(menu_frame)

    def create_menu_buttons(self, parent):
        """Create the main menu buttons in a grid layout"""
        # Create scrollable frame for buttons
        button_container = ctk.CTkScrollableFrame(parent)
        button_container.pack(fill="both", expand=True, padx=10, pady=10)

        # Define menu options
        menu_options = [
            ("Global Price Changes", self.open_global_price_changes, "Adjust prices globally across all merchants"),
            ("Edit Economy Fields", self.open_economy_fields, "Modify economy configuration settings"),
            ("Edit Merchant Level", self.open_merchant_level, "Adjust individual merchant settings"),
            ("Edit Outpost Level", self.open_outpost_level, "Modify outpost-wide settings"),
            ("Fine Tune Items", self.open_fine_tune, "Detailed item-level editing"),
            ("Spread Edit Items", self.open_spread_edit, "Apply changes across multiple traders"),
            ("Purchase Settings", self.open_purchase_settings, "Manage item purchase availability"),
            ("Category Management", self.open_category_management, "Edit items by category"),
        ]

        # Create buttons in a 2-column grid
        for i, (text, command, description) in enumerate(menu_options):
            row = i // 2
            col = i % 2

            # Create button frame
            btn_frame = ctk.CTkFrame(button_container)
            btn_frame.grid(row=row, column=col, padx=10, pady=5, sticky="ew")

            # Configure grid weights
            button_container.grid_columnconfigure(col, weight=1)

            # Create button
            btn = ctk.CTkButton(
                btn_frame,
                text=text,
                command=command,
                height=40,
                font=ctk.CTkFont(size=14, weight="bold")
            )
            btn.pack(fill="x", padx=10, pady=5)

            # Create description label
            desc_label = ctk.CTkLabel(
                btn_frame,
                text=description,
                font=ctk.CTkFont(size=10),
                text_color="gray"
            )
            desc_label.pack(pady=(0, 5))

    def create_status_bar(self):
        """Create status bar at bottom"""
        status_frame = ctk.CTkFrame(self.main_container)
        status_frame.pack(fill="x", padx=5, pady=5)

        self.status_label = ctk.CTkLabel(
            status_frame,
            text="Ready - Load a JSON file to begin editing",
            font=ctk.CTkFont(size=10)
        )
        self.status_label.pack(side="left", padx=10, pady=5)

        # Save buttons
        save_frame = ctk.CTkFrame(status_frame)
        save_frame.pack(side="right", padx=10, pady=5)

        save_button = ctk.CTkButton(
            save_frame,
            text="Save & Exit",
            command=self.save_and_exit,
            width=100
        )
        save_button.pack(side="left", padx=5)

        exit_button = ctk.CTkButton(
            save_frame,
            text="Exit",
            command=self.exit_application,
            width=80
        )
        exit_button.pack(side="left", padx=5)

    # File Management Methods
    def load_json_file(self):
        """Load a JSON file using file dialog"""
        file_path = filedialog.askopenfilename(
            title="Select Economy Override JSON File",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r') as f:
                    self.current_data = json.load(f)
                self.current_filename = os.path.basename(file_path)
                self.current_file_label.configure(text=f"Loaded: {self.current_filename}")
                self.status_label.configure(text=f"Successfully loaded {self.current_filename}")
                self.enable_menu_buttons()
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load JSON file: {str(e)}")
                self.status_label.configure(text="Error loading file")

    def reload_file(self):
        """Reload the current file"""
        if self.current_filename:
            # Find the file in current directory
            if os.path.exists(self.current_filename):
                try:
                    with open(self.current_filename, 'r') as f:
                        self.current_data = json.load(f)
                    self.status_label.configure(text=f"Reloaded {self.current_filename}")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to reload file: {str(e)}")
            else:
                messagebox.showwarning("Warning", "Original file not found. Please load a new file.")
        else:
            messagebox.showinfo("Info", "No file to reload. Please load a file first.")

    def enable_menu_buttons(self):
        """Enable menu buttons after file is loaded"""
        # This would enable buttons that were disabled
        pass

    # CLI Integration Methods
    def open_cli_popup(self):
        """Open the CLI version in a popup window"""
        if not self.cli_module:
            messagebox.showerror("Error", "CLI module not loaded")
            return

        # Create popup window
        cli_window = ctk.CTkToplevel(self.root)
        cli_window.title("CLI Version - SCUM Economy Chooser")
        cli_window.geometry("800x600")
        cli_window.transient(self.root)
        cli_window.grab_set()

        # Create text widget for CLI output
        text_frame = ctk.CTkFrame(cli_window)
        text_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Add instructions
        instructions = ctk.CTkLabel(
            text_frame,
            text="CLI Version Integration\nNote: This will launch the original CLI in a separate terminal window.",
            font=ctk.CTkFont(size=12)
        )
        instructions.pack(pady=10)

        # Launch CLI button
        launch_button = ctk.CTkButton(
            text_frame,
            text="Launch CLI Terminal",
            command=self.launch_cli_terminal,
            height=40
        )
        launch_button.pack(pady=10)

        # Close button
        close_button = ctk.CTkButton(
            text_frame,
            text="Close",
            command=cli_window.destroy,
            width=100
        )
        close_button.pack(pady=10)

    def launch_cli_terminal(self):
        """Launch the CLI version in a separate terminal"""
        try:
            if os.name == 'nt':  # Windows
                subprocess.Popen(['cmd', '/c', 'start', 'cmd', '/k', 'python', '1_45c.py'])
            else:  # Unix/Linux/Mac
                subprocess.Popen(['gnome-terminal', '--', 'python3', '1_45c.py'])
            self.status_label.configure(text="CLI version launched in separate terminal")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch CLI: {str(e)}")

    # Menu Action Methods
    def open_global_price_changes(self):
        """Open global price changes dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        GlobalPriceDialog(self.root, self.current_data, self.update_status)

    def open_economy_fields(self):
        """Open economy fields editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        EconomyFieldsDialog(self.root, self.current_data, self.update_status)

    def open_merchant_level(self):
        """Open merchant level editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        MerchantLevelDialog(self.root, self.current_data, self.update_status)

    def open_outpost_level(self):
        """Open outpost level editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        OutpostLevelDialog(self.root, self.current_data, self.update_status)

    def open_fine_tune(self):
        """Open fine tune editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        FineTuneDialog(self.root, self.current_data, self.update_status)

    def open_spread_edit(self):
        """Open spread edit dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        SpreadEditDialog(self.root, self.current_data, self.update_status)

    def open_purchase_settings(self):
        """Open purchase settings dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        PurchaseSettingsDialog(self.root, self.current_data, self.update_status)

    def open_category_management(self):
        """Open category management dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        CategoryManagementDialog(self.root, self.current_data, self.update_status)

    def update_status(self, message):
        """Update status bar message"""
        self.status_label.configure(text=message)

    # Save and Exit Methods
    def save_and_exit(self):
        """Save the current data and exit"""
        if not self.current_data:
            messagebox.showwarning("Warning", "No data to save")
            return

        # Generate timestamped filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_filename = f"{timestamp}_economy_config.json"

        try:
            with open(save_filename, 'w') as f:
                json.dump(self.current_data, f, indent=2)

            messagebox.showinfo("Success", f"File saved as {save_filename}")
            self.root.quit()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save file: {str(e)}")

    def exit_application(self):
        """Exit the application"""
        if messagebox.askyesno("Confirm Exit", "Are you sure you want to exit without saving?"):
            self.root.quit()


# Dialog Classes
class GlobalPriceDialog:
    def __init__(self, parent, data, status_callback):
        self.data = data
        self.status_callback = status_callback

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("Global Price Changes")
        self.window.geometry("600x400")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the global price changes interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="Global Price Changes",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Description
        desc = ctk.CTkLabel(
            main_frame,
            text="Adjust prices globally across all merchants and outposts",
            font=ctk.CTkFont(size=12)
        )
        desc.pack(pady=5)

        # Price field selection
        field_frame = ctk.CTkFrame(main_frame)
        field_frame.pack(fill="x", padx=10, pady=10)

        field_label = ctk.CTkLabel(field_frame, text="Select Price Field:")
        field_label.pack(pady=5)

        self.price_field_var = ctk.StringVar(value="base-purchase-price")
        field_radio1 = ctk.CTkRadioButton(
            field_frame,
            text="Base Purchase Price",
            variable=self.price_field_var,
            value="base-purchase-price"
        )
        field_radio1.pack(pady=2)

        field_radio2 = ctk.CTkRadioButton(
            field_frame,
            text="Base Sell Price",
            variable=self.price_field_var,
            value="base-sell-price"
        )
        field_radio2.pack(pady=2)

        # Percentage input
        percent_frame = ctk.CTkFrame(main_frame)
        percent_frame.pack(fill="x", padx=10, pady=10)

        percent_label = ctk.CTkLabel(percent_frame, text="Percentage Change (-99 to +100):")
        percent_label.pack(pady=5)

        self.percent_entry = ctk.CTkEntry(
            percent_frame,
            placeholder_text="Enter percentage (e.g., -10, +25)"
        )
        self.percent_entry.pack(pady=5, padx=20, fill="x")

        # Preview section
        preview_frame = ctk.CTkFrame(main_frame)
        preview_frame.pack(fill="x", padx=10, pady=10)

        preview_label = ctk.CTkLabel(preview_frame, text="Preview Changes:")
        preview_label.pack(pady=5)

        self.preview_text = ctk.CTkTextbox(preview_frame, height=100)
        self.preview_text.pack(fill="x", padx=10, pady=5)

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        preview_btn = ctk.CTkButton(
            button_frame,
            text="Preview Changes",
            command=self.preview_changes
        )
        preview_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="Apply Changes",
            command=self.apply_changes
        )
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="Cancel",
            command=self.window.destroy
        )
        cancel_btn.pack(side="right", padx=5)

    def preview_changes(self):
        """Preview the changes without applying them"""
        try:
            percentage = float(self.percent_entry.get())
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return

            field = self.price_field_var.get()
            preview_text = f"Preview for {field} change of {percentage}%:\n\n"

            count = 0
            for trader_name, trader_items in self.data["economy-override"]["traders"].items():
                for item in trader_items:
                    if field in item and item[field] not in ["null", "-1"]:
                        current_price = float(item[field])
                        new_price = max(1, round(current_price * (1 + percentage / 100)))
                        preview_text += f"{trader_name}: {item['tradeable-code']} - {current_price} → {new_price}\n"
                        count += 1
                        if count >= 10:  # Limit preview to first 10 items
                            preview_text += f"... and {self.count_total_items(field) - 10} more items\n"
                            break
                if count >= 10:
                    break

            self.preview_text.delete("1.0", "end")
            self.preview_text.insert("1.0", preview_text)

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")

    def count_total_items(self, field):
        """Count total items that would be affected"""
        count = 0
        for trader_items in self.data["economy-override"]["traders"].values():
            for item in trader_items:
                if field in item and item[field] not in ["null", "-1"]:
                    count += 1
        return count

    def apply_changes(self):
        """Apply the global price changes"""
        try:
            percentage = float(self.percent_entry.get())
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return

            field = self.price_field_var.get()

            if messagebox.askyesno("Confirm", f"Apply {percentage}% change to {field} for all items?"):
                count = 0
                for trader_items in self.data["economy-override"]["traders"].values():
                    for item in trader_items:
                        if field in item and item[field] not in ["null", "-1"]:
                            current_price = float(item[field])
                            new_price = max(1, round(current_price * (1 + percentage / 100)))
                            item[field] = str(new_price)
                            count += 1

                self.status_callback(f"Applied {percentage}% change to {count} items")
                messagebox.showinfo("Success", f"Successfully updated {count} items")
                self.window.destroy()

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")


class EconomyFieldsDialog:
    def __init__(self, parent, data, status_callback):
        self.data = data
        self.status_callback = status_callback

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("Edit Economy Fields")
        self.window.geometry("700x500")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the economy fields interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="Economy Fields Editor",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Scrollable frame for fields
        scroll_frame = ctk.CTkScrollableFrame(main_frame)
        scroll_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Economy fields with default values
        self.economy_fields = {
            "economy-reset-time-hours": "-1.0",
            "prices-randomization-time-hours": "-1.0",
            "tradeable-rotation-time-ingame-hours-min": "48.0",
            "tradeable-rotation-time-ingame-hours-max": "96.0",
            "tradeable-rotation-time-of-day-min": "8.0",
            "tradeable-rotation-time-of-day-max": "16.0",
            "fully-restock-tradeable-hours": "2.0",
            "trader-funds-change-rate-per-hour-multiplier": "1.0",
            "prices-subject-to-player-count": "1",
            "gold-price-subject-to-global-multiplier": "1",
            "economy-logging": "1",
            "traders-unlimited-funds": "0",
            "traders-unlimited-stock": "0",
            "only-after-player-sale-tradeable-availability-enabled": "1",
            "tradeable-rotation-enabled": "1",
            "enable-fame-point-requirement": "1"
        }

        self.field_entries = {}

        # Create entry fields for each economy field
        for field_name, default_value in self.economy_fields.items():
            field_frame = ctk.CTkFrame(scroll_frame)
            field_frame.pack(fill="x", padx=5, pady=2)

            # Get current value from data or use default
            current_value = self.data.get("economy-override", {}).get(field_name, default_value)

            label = ctk.CTkLabel(
                field_frame,
                text=field_name,
                width=300,
                anchor="w"
            )
            label.pack(side="left", padx=5, pady=5)

            entry = ctk.CTkEntry(field_frame, width=100)
            entry.pack(side="right", padx=5, pady=5)
            entry.insert(0, str(current_value))

            self.field_entries[field_name] = entry

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        save_btn = ctk.CTkButton(
            button_frame,
            text="Save Changes",
            command=self.save_changes
        )
        save_btn.pack(side="left", padx=5)

        reset_btn = ctk.CTkButton(
            button_frame,
            text="Reset to Defaults",
            command=self.reset_to_defaults
        )
        reset_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="Cancel",
            command=self.window.destroy
        )
        cancel_btn.pack(side="right", padx=5)

    def save_changes(self):
        """Save the economy field changes"""
        try:
            # Ensure economy-override section exists
            if "economy-override" not in self.data:
                self.data["economy-override"] = {}

            changes_made = 0
            for field_name, entry in self.field_entries.items():
                new_value = entry.get().strip()
                if new_value:
                    self.data["economy-override"][field_name] = new_value
                    changes_made += 1

            self.status_callback(f"Updated {changes_made} economy fields")
            messagebox.showinfo("Success", f"Successfully updated {changes_made} fields")
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save changes: {str(e)}")

    def reset_to_defaults(self):
        """Reset all fields to default values"""
        if messagebox.askyesno("Confirm Reset", "Reset all fields to default values?"):
            for field_name, entry in self.field_entries.items():
                entry.delete(0, "end")
                entry.insert(0, self.economy_fields[field_name])


class MerchantLevelDialog:
    def __init__(self, parent, data, status_callback):
        self.data = data
        self.status_callback = status_callback

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("Edit Merchant Level")
        self.window.geometry("800x600")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the merchant level interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="Edit Specific Merchants",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Outpost selection
        outpost_frame = ctk.CTkFrame(main_frame)
        outpost_frame.pack(fill="x", padx=10, pady=10)

        outpost_label = ctk.CTkLabel(outpost_frame, text="Select Outpost:")
        outpost_label.pack(pady=5)

        self.outpost_var = ctk.StringVar(value="A_0")
        outpost_menu = ctk.CTkOptionMenu(
            outpost_frame,
            variable=self.outpost_var,
            values=["A_0", "B_4", "C_2", "Z_3"],
            command=self.update_merchant_list
        )
        outpost_menu.pack(pady=5)

        # Merchant selection
        merchant_frame = ctk.CTkFrame(main_frame)
        merchant_frame.pack(fill="both", expand=True, padx=10, pady=10)

        merchant_label = ctk.CTkLabel(merchant_frame, text="Select Merchant:")
        merchant_label.pack(pady=5)

        # Merchant listbox
        self.merchant_listbox = tk.Listbox(merchant_frame, height=10)
        self.merchant_listbox.pack(fill="both", expand=True, padx=10, pady=5)
        self.merchant_listbox.bind('<<ListboxSelect>>', self.on_merchant_select)

        # Price adjustment
        adjust_frame = ctk.CTkFrame(main_frame)
        adjust_frame.pack(fill="x", padx=10, pady=10)

        # Price field selection
        field_label = ctk.CTkLabel(adjust_frame, text="Price Field:")
        field_label.pack(pady=2)

        self.price_field_var = ctk.StringVar(value="base-purchase-price")
        field_radio1 = ctk.CTkRadioButton(
            adjust_frame,
            text="Base Purchase Price",
            variable=self.price_field_var,
            value="base-purchase-price"
        )
        field_radio1.pack(pady=1)

        field_radio2 = ctk.CTkRadioButton(
            adjust_frame,
            text="Base Sell Price",
            variable=self.price_field_var,
            value="base-sell-price"
        )
        field_radio2.pack(pady=1)

        # Percentage input
        percent_label = ctk.CTkLabel(adjust_frame, text="Percentage Change (-99 to +100):")
        percent_label.pack(pady=2)

        self.percent_entry = ctk.CTkEntry(
            adjust_frame,
            placeholder_text="Enter percentage"
        )
        self.percent_entry.pack(pady=2, padx=20, fill="x")

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="Apply to Selected Merchant",
            command=self.apply_to_merchant
        )
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="Close",
            command=self.window.destroy
        )
        cancel_btn.pack(side="right", padx=5)

        # Initialize merchant list
        self.update_merchant_list()

    def update_merchant_list(self, *args):
        """Update the merchant list based on selected outpost"""
        outpost = self.outpost_var.get()
        self.merchant_listbox.delete(0, tk.END)

        if "economy-override" in self.data and "traders" in self.data["economy-override"]:
            merchants = [
                trader for trader in self.data["economy-override"]["traders"].keys()
                if trader.startswith(outpost)
            ]
            for merchant in sorted(merchants):
                self.merchant_listbox.insert(tk.END, merchant)

    def on_merchant_select(self, event):
        """Handle merchant selection"""
        selection = self.merchant_listbox.curselection()
        if selection:
            merchant = self.merchant_listbox.get(selection[0])
            # Could show merchant details here

    def apply_to_merchant(self):
        """Apply percentage change to selected merchant"""
        selection = self.merchant_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a merchant")
            return

        try:
            percentage = float(self.percent_entry.get())
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return

            merchant = self.merchant_listbox.get(selection[0])
            field = self.price_field_var.get()

            if messagebox.askyesno("Confirm", f"Apply {percentage}% change to {field} for {merchant}?"):
                count = 0
                trader_items = self.data["economy-override"]["traders"][merchant]

                for item in trader_items:
                    if field in item and item[field] not in ["null", "-1"]:
                        current_price = float(item[field])
                        new_price = max(1, round(current_price * (1 + percentage / 100)))
                        item[field] = str(new_price)
                        count += 1

                self.status_callback(f"Updated {count} items for {merchant}")
                messagebox.showinfo("Success", f"Successfully updated {count} items for {merchant}")

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")
        except KeyError:
            messagebox.showerror("Error", "Merchant data not found")


class OutpostLevelDialog:
    def __init__(self, parent, data, status_callback):
        self.data = data
        self.status_callback = status_callback

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("Edit Outpost Level")
        self.window.geometry("600x400")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the outpost level interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="Edit Outpost Level Prices",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Outpost selection
        outpost_frame = ctk.CTkFrame(main_frame)
        outpost_frame.pack(fill="x", padx=10, pady=10)

        outpost_label = ctk.CTkLabel(outpost_frame, text="Select Outpost:")
        outpost_label.pack(pady=5)

        self.outpost_var = ctk.StringVar(value="A_0")
        outpost_menu = ctk.CTkOptionMenu(
            outpost_frame,
            variable=self.outpost_var,
            values=["A_0", "B_4", "C_2", "Z_3"]
        )
        outpost_menu.pack(pady=5)

        # Price field selection
        field_frame = ctk.CTkFrame(main_frame)
        field_frame.pack(fill="x", padx=10, pady=10)

        field_label = ctk.CTkLabel(field_frame, text="Select Price Field:")
        field_label.pack(pady=5)

        self.price_field_var = ctk.StringVar(value="base-purchase-price")
        field_radio1 = ctk.CTkRadioButton(
            field_frame,
            text="Base Purchase Price",
            variable=self.price_field_var,
            value="base-purchase-price"
        )
        field_radio1.pack(pady=2)

        field_radio2 = ctk.CTkRadioButton(
            field_frame,
            text="Base Sell Price",
            variable=self.price_field_var,
            value="base-sell-price"
        )
        field_radio2.pack(pady=2)

        # Percentage input
        percent_frame = ctk.CTkFrame(main_frame)
        percent_frame.pack(fill="x", padx=10, pady=10)

        percent_label = ctk.CTkLabel(percent_frame, text="Percentage Change (-99 to +100):")
        percent_label.pack(pady=5)

        self.percent_entry = ctk.CTkEntry(
            percent_frame,
            placeholder_text="Enter percentage"
        )
        self.percent_entry.pack(pady=5, padx=20, fill="x")

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="Apply to All Merchants in Outpost",
            command=self.apply_to_outpost
        )
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="Close",
            command=self.window.destroy
        )
        cancel_btn.pack(side="right", padx=5)

    def apply_to_outpost(self):
        """Apply percentage change to all merchants in selected outpost"""
        try:
            percentage = float(self.percent_entry.get())
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return

            outpost = self.outpost_var.get()
            field = self.price_field_var.get()

            if messagebox.askyesno("Confirm", f"Apply {percentage}% change to {field} for all merchants in {outpost}?"):
                count = 0
                merchant_count = 0

                for trader_name, trader_items in self.data["economy-override"]["traders"].items():
                    if trader_name.startswith(outpost):
                        merchant_count += 1
                        for item in trader_items:
                            if field in item and item[field] not in ["null", "-1"]:
                                current_price = float(item[field])
                                new_price = max(1, round(current_price * (1 + percentage / 100)))
                                item[field] = str(new_price)
                                count += 1

                self.status_callback(f"Updated {count} items across {merchant_count} merchants in {outpost}")
                messagebox.showinfo("Success", f"Successfully updated {count} items across {merchant_count} merchants")

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")


# Placeholder classes for remaining dialogs
class FineTuneDialog:
    def __init__(self, parent, data, status_callback):
        self.window = ctk.CTkToplevel(parent)
        self.window.title("Fine Tune Items")
        self.window.geometry("400x200")
        self.window.transient(parent)

        label = ctk.CTkLabel(self.window, text="Fine Tune Dialog - Coming Soon")
        label.pack(pady=50)

        close_btn = ctk.CTkButton(self.window, text="Close", command=self.window.destroy)
        close_btn.pack(pady=10)


class SpreadEditDialog:
    def __init__(self, parent, data, status_callback):
        self.window = ctk.CTkToplevel(parent)
        self.window.title("Spread Edit Items")
        self.window.geometry("400x200")
        self.window.transient(parent)

        label = ctk.CTkLabel(self.window, text="Spread Edit Dialog - Coming Soon")
        label.pack(pady=50)

        close_btn = ctk.CTkButton(self.window, text="Close", command=self.window.destroy)
        close_btn.pack(pady=10)


class PurchaseSettingsDialog:
    def __init__(self, parent, data, status_callback):
        self.window = ctk.CTkToplevel(parent)
        self.window.title("Purchase Settings")
        self.window.geometry("400x200")
        self.window.transient(parent)

        label = ctk.CTkLabel(self.window, text="Purchase Settings Dialog - Coming Soon")
        label.pack(pady=50)

        close_btn = ctk.CTkButton(self.window, text="Close", command=self.window.destroy)
        close_btn.pack(pady=10)


class CategoryManagementDialog:
    def __init__(self, parent, data, status_callback):
        self.window = ctk.CTkToplevel(parent)
        self.window.title("Category Management")
        self.window.geometry("400x200")
        self.window.transient(parent)

        label = ctk.CTkLabel(self.window, text="Category Management Dialog - Coming Soon")
        label.pack(pady=50)

        close_btn = ctk.CTkButton(self.window, text="Close", command=self.window.destroy)
        close_btn.pack(pady=10)


if __name__ == "__main__":
    app = SCUMEconomyGUI()
    app.root.mainloop()