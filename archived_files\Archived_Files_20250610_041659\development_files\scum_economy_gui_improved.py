import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import sys
import subprocess
import threading
import time
from datetime import datetime
import logging
import math

# Set appearance
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class SCUMEconomyGUI:
    def __init__(self):
        # Create main window
        self.root = ctk.CTk()
        self.root.title("SCUM Economy Chooser - Advanced GUI v1.45b")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # Initialize variables
        self.current_data = None
        self.current_filename = None
        self.current_file_path = None
        
        # Create GUI components
        self.create_main_interface()
        
        # Auto-scan for JSON files on startup
        self.auto_scan_json_files()
    
    def create_main_interface(self):
        """Create the main GUI interface"""
        # Create main container
        self.main_container = ctk.CTkFrame(self.root)
        self.main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Create header
        self.create_header()
        
        # Create file management section
        self.create_file_section()
        
        # Create main menu section
        self.create_main_menu()
        
        # Create status bar
        self.create_status_bar()
    
    def create_header(self):
        """Create the application header"""
        header_frame = ctk.CTkFrame(self.main_container)
        header_frame.pack(fill="x", padx=5, pady=5)
        
        # Title
        title_label = ctk.CTkLabel(
            header_frame,
            text="🎮 SCUM Economy Chooser - Advanced GUI",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=10)
        
        # Version info
        version_label = ctk.CTkLabel(
            header_frame,
            text="Build v1.45b - Advanced GUI Edition by V1nceTD",
            font=ctk.CTkFont(size=12)
        )
        version_label.pack()
        
        # Button frame
        button_frame = ctk.CTkFrame(header_frame)
        button_frame.pack(pady=10)
        
        # CLI Access button
        cli_button = ctk.CTkButton(
            button_frame,
            text="🖥️ Launch CLI Version",
            command=self.launch_cli_terminal,
            width=150,
            height=35
        )
        cli_button.pack(side="left", padx=5)
        
        # Help button
        help_button = ctk.CTkButton(
            button_frame,
            text="❓ Help",
            command=self.show_help,
            width=80,
            height=35
        )
        help_button.pack(side="left", padx=5)
    
    def create_file_section(self):
        """Create file management section"""
        file_frame = ctk.CTkFrame(self.main_container)
        file_frame.pack(fill="x", padx=5, pady=5)
        
        # File section title
        file_title = ctk.CTkLabel(
            file_frame,
            text="📁 File Management",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        file_title.pack(pady=5)
        
        # File selection frame
        file_select_frame = ctk.CTkFrame(file_frame)
        file_select_frame.pack(fill="x", padx=10, pady=5)
        
        # Current file label
        self.current_file_label = ctk.CTkLabel(
            file_select_frame,
            text="No file loaded",
            font=ctk.CTkFont(size=12),
            text_color="orange"
        )
        self.current_file_label.pack(side="left", padx=10)
        
        # File buttons
        button_frame = ctk.CTkFrame(file_select_frame)
        button_frame.pack(side="right", padx=10)
        
        load_button = ctk.CTkButton(
            button_frame,
            text="📂 Load JSON File",
            command=self.load_json_file,
            width=140
        )
        load_button.pack(side="left", padx=5)
        
        scan_button = ctk.CTkButton(
            button_frame,
            text="🔍 Scan Directory",
            command=self.scan_directory,
            width=120
        )
        scan_button.pack(side="left", padx=5)
        
        reload_button = ctk.CTkButton(
            button_frame,
            text="🔄 Reload",
            command=self.reload_file,
            width=80
        )
        reload_button.pack(side="left", padx=5)
    
    def create_main_menu(self):
        """Create the main menu with buttons"""
        menu_frame = ctk.CTkFrame(self.main_container)
        menu_frame.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Menu title
        menu_title = ctk.CTkLabel(
            menu_frame,
            text="⚙️ Economy Editor Tools",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        menu_title.pack(pady=10)
        
        # Create button grid
        self.create_menu_buttons(menu_frame)
    
    def create_menu_buttons(self, parent):
        """Create the main menu buttons in a grid layout"""
        # Create scrollable frame for buttons
        button_container = ctk.CTkScrollableFrame(parent)
        button_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Define menu options with emojis
        menu_options = [
            ("🌍 Global Price Changes", self.open_global_price_changes, "Adjust prices globally across all merchants"),
            ("⚙️ Edit Economy Fields", self.open_economy_fields, "Modify economy configuration settings"),
            ("👤 Edit Merchant Level", self.open_merchant_level, "Adjust individual merchant settings"),
            ("🏢 Edit Outpost Level", self.open_outpost_level, "Modify outpost-wide settings"),
            ("🔧 Fine Tune Items", self.open_fine_tune, "Detailed item-level editing"),
            ("📊 Spread Edit Items", self.open_spread_edit, "Apply changes across multiple traders"),
            ("🛒 Purchase Settings", self.open_purchase_settings, "Manage item purchase availability"),
            ("📂 Category Management", self.open_category_management, "Edit items by category"),
        ]
        
        # Create buttons in a 2-column grid
        for i, (text, command, description) in enumerate(menu_options):
            row = i // 2
            col = i % 2
            
            # Create button frame
            btn_frame = ctk.CTkFrame(button_container)
            btn_frame.grid(row=row, column=col, padx=10, pady=5, sticky="ew")
            
            # Configure grid weights
            button_container.grid_columnconfigure(col, weight=1)
            
            # Create button
            btn = ctk.CTkButton(
                btn_frame,
                text=text,
                command=command,
                height=50,
                font=ctk.CTkFont(size=14, weight="bold")
            )
            btn.pack(fill="x", padx=10, pady=5)
            
            # Create description label
            desc_label = ctk.CTkLabel(
                btn_frame,
                text=description,
                font=ctk.CTkFont(size=10),
                text_color="gray"
            )
            desc_label.pack(pady=(0, 5))
    
    def create_status_bar(self):
        """Create status bar at bottom"""
        status_frame = ctk.CTkFrame(self.main_container)
        status_frame.pack(fill="x", padx=5, pady=5)
        
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="Ready - Load a JSON file to begin editing",
            font=ctk.CTkFont(size=10)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        # Save buttons
        save_frame = ctk.CTkFrame(status_frame)
        save_frame.pack(side="right", padx=10, pady=5)
        
        save_button = ctk.CTkButton(
            save_frame,
            text="💾 Save & Exit",
            command=self.save_and_exit,
            width=120
        )
        save_button.pack(side="left", padx=5)
        
        exit_button = ctk.CTkButton(
            save_frame,
            text="❌ Exit",
            command=self.exit_application,
            width=80
        )
        exit_button.pack(side="left", padx=5)
    
    # File Management Methods
    def auto_scan_json_files(self):
        """Auto-scan for JSON files in current directory"""
        try:
            json_files = [f for f in os.listdir('.') if f.endswith('.json')]
            if json_files:
                self.status_label.configure(text=f"Found {len(json_files)} JSON file(s) in directory")
            else:
                self.status_label.configure(text="No JSON files found in current directory")
        except Exception as e:
            self.status_label.configure(text="Error scanning directory")
    
    def scan_directory(self):
        """Scan directory for JSON files and show selection dialog"""
        try:
            json_files = [f for f in os.listdir('.') if f.endswith('.json')]
            if not json_files:
                messagebox.showinfo("No Files", "No JSON files found in current directory")
                return
            
            # Create selection dialog
            self.show_file_selection_dialog(json_files)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to scan directory: {str(e)}")
    
    def show_file_selection_dialog(self, json_files):
        """Show dialog to select from available JSON files"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Select JSON File")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # Title
        title = ctk.CTkLabel(dialog, text="Select Economy Override JSON File", 
                           font=ctk.CTkFont(size=16, weight="bold"))
        title.pack(pady=10)
        
        # File list
        listbox_frame = ctk.CTkFrame(dialog)
        listbox_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        file_listbox = tk.Listbox(listbox_frame, height=15)
        file_listbox.pack(fill="both", expand=True, padx=10, pady=10)
        
        for file in json_files:
            file_listbox.insert(tk.END, file)
        
        # Buttons
        button_frame = ctk.CTkFrame(dialog)
        button_frame.pack(fill="x", padx=20, pady=10)
        
        def load_selected():
            selection = file_listbox.curselection()
            if selection:
                filename = json_files[selection[0]]
                self.load_specific_file(filename)
                dialog.destroy()
            else:
                messagebox.showwarning("No Selection", "Please select a file")
        
        load_btn = ctk.CTkButton(button_frame, text="Load Selected", command=load_selected)
        load_btn.pack(side="left", padx=5)
        
        cancel_btn = ctk.CTkButton(button_frame, text="Cancel", command=dialog.destroy)
        cancel_btn.pack(side="right", padx=5)
    
    def load_specific_file(self, filename):
        """Load a specific JSON file"""
        try:
            with open(filename, 'r') as f:
                self.current_data = json.load(f)
            self.current_filename = filename
            self.current_file_path = os.path.abspath(filename)
            self.current_file_label.configure(text=f"✅ Loaded: {filename}", text_color="green")
            self.status_label.configure(text=f"Successfully loaded {filename}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load {filename}: {str(e)}")
            self.status_label.configure(text="Error loading file")

    def load_json_file(self):
        """Load a JSON file using file dialog"""
        file_path = filedialog.askopenfilename(
            title="Select Economy Override JSON File",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r') as f:
                    self.current_data = json.load(f)
                self.current_filename = os.path.basename(file_path)
                self.current_file_path = file_path
                self.current_file_label.configure(text=f"✅ Loaded: {self.current_filename}", text_color="green")
                self.status_label.configure(text=f"Successfully loaded {self.current_filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load JSON file: {str(e)}")
                self.status_label.configure(text="Error loading file")

    def reload_file(self):
        """Reload the current file"""
        if self.current_file_path and os.path.exists(self.current_file_path):
            try:
                with open(self.current_file_path, 'r') as f:
                    self.current_data = json.load(f)
                self.status_label.configure(text=f"Reloaded {self.current_filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to reload file: {str(e)}")
        elif self.current_filename and os.path.exists(self.current_filename):
            try:
                with open(self.current_filename, 'r') as f:
                    self.current_data = json.load(f)
                self.status_label.configure(text=f"Reloaded {self.current_filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to reload file: {str(e)}")
        else:
            messagebox.showwarning("Warning", "No file to reload. Please load a file first.")

    # CLI Integration Methods
    def launch_cli_terminal(self):
        """Launch the CLI version in a separate terminal"""
        try:
            if os.name == 'nt':  # Windows
                subprocess.Popen(['cmd', '/c', 'start', 'cmd', '/k', 'python', '1_45c.py'])
            else:  # Unix/Linux/Mac
                subprocess.Popen(['gnome-terminal', '--', 'python3', '1_45c.py'])
            self.status_label.configure(text="CLI version launched in separate terminal")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch CLI: {str(e)}")

    def show_help(self):
        """Show help dialog"""
        help_window = ctk.CTkToplevel(self.root)
        help_window.title("Help - SCUM Economy Chooser")
        help_window.geometry("600x500")
        help_window.transient(self.root)

        # Help content
        help_text = """
SCUM Economy Chooser - Advanced GUI Help

GETTING STARTED:
1. Load a JSON file using 'Load JSON File' or 'Scan Directory'
2. Select the editing tool you need from the main menu
3. Make your changes and save when done

MAIN FEATURES:

🌍 Global Price Changes
- Adjust all merchant prices by percentage
- Choose between purchase and sell prices
- Preview changes before applying

⚙️ Edit Economy Fields
- Modify core economy settings
- Reset time, rotation settings, etc.

👤 Edit Merchant Level
- Target specific merchants
- Adjust individual trader prices

🏢 Edit Outpost Level
- Apply changes to entire outposts
- Bulk edit all merchants in an outpost

🔧 Fine Tune Items
- Item-level precision editing
- Search and modify specific items

📊 Spread Edit Items
- Apply changes across multiple traders
- Bulk operations

🛒 Purchase Settings
- Manage item availability
- Control what can be purchased

📂 Category Management
- Edit items by category
- Weapons, ammo, crafted items, etc.

TIPS:
- Always backup your files before editing
- Use the CLI version for advanced operations
- Preview changes when possible
- Check logs for detailed change history

For more help, consult the original CLI documentation.
        """

        text_widget = ctk.CTkTextbox(help_window)
        text_widget.pack(fill="both", expand=True, padx=20, pady=20)
        text_widget.insert("1.0", help_text)
        text_widget.configure(state="disabled")

        close_btn = ctk.CTkButton(help_window, text="Close", command=help_window.destroy)
        close_btn.pack(pady=10)

    # Menu Action Methods
    def open_global_price_changes(self):
        """Open global price changes dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        GlobalPriceDialog(self.root, self.current_data, self.update_status)

    def open_economy_fields(self):
        """Open economy fields editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        EconomyFieldsDialog(self.root, self.current_data, self.update_status)

    def open_merchant_level(self):
        """Open merchant level editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        MerchantLevelDialog(self.root, self.current_data, self.update_status)

    def open_outpost_level(self):
        """Open outpost level editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        OutpostLevelDialog(self.root, self.current_data, self.update_status)

    def open_fine_tune(self):
        """Open fine tune editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        FineTuneDialog(self.root, self.current_data, self.update_status)

    def open_spread_edit(self):
        """Open spread edit dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        SpreadEditDialog(self.root, self.current_data, self.update_status)

    def open_purchase_settings(self):
        """Open purchase settings dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        PurchaseSettingsDialog(self.root, self.current_data, self.update_status)

    def open_category_management(self):
        """Open category management dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        CategoryManagementDialog(self.root, self.current_data, self.update_status)

    def update_status(self, message):
        """Update status bar message"""
        self.status_label.configure(text=message)

    # Save and Exit Methods
    def save_and_exit(self):
        """Save the current data and exit"""
        if not self.current_data:
            messagebox.showwarning("Warning", "No data to save")
            return

        # Generate timestamped filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_filename = f"{timestamp}_economy_config.json"

        try:
            with open(save_filename, 'w') as f:
                json.dump(self.current_data, f, indent=2)

            messagebox.showinfo("Success", f"File saved as {save_filename}")
            self.root.quit()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save file: {str(e)}")

    def exit_application(self):
        """Exit the application"""
        if messagebox.askyesno("Confirm Exit", "Are you sure you want to exit without saving?"):
            self.root.quit()


# Dialog Classes
class GlobalPriceDialog:
    def __init__(self, parent, data, status_callback):
        self.data = data
        self.status_callback = status_callback

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("🌍 Global Price Changes")
        self.window.geometry("700x500")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the global price changes interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="🌍 Global Price Changes",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Description
        desc = ctk.CTkLabel(
            main_frame,
            text="Adjust prices globally across all merchants and outposts",
            font=ctk.CTkFont(size=12)
        )
        desc.pack(pady=5)

        # Price field selection
        field_frame = ctk.CTkFrame(main_frame)
        field_frame.pack(fill="x", padx=10, pady=10)

        field_label = ctk.CTkLabel(field_frame, text="Select Price Field:")
        field_label.pack(pady=5)

        self.price_field_var = ctk.StringVar(value="base-purchase-price")
        field_radio1 = ctk.CTkRadioButton(
            field_frame,
            text="💰 Base Purchase Price",
            variable=self.price_field_var,
            value="base-purchase-price"
        )
        field_radio1.pack(pady=2)

        field_radio2 = ctk.CTkRadioButton(
            field_frame,
            text="💵 Base Sell Price",
            variable=self.price_field_var,
            value="base-sell-price"
        )
        field_radio2.pack(pady=2)

        # Percentage input
        percent_frame = ctk.CTkFrame(main_frame)
        percent_frame.pack(fill="x", padx=10, pady=10)

        percent_label = ctk.CTkLabel(percent_frame, text="Percentage Change (-99 to +100):")
        percent_label.pack(pady=5)

        self.percent_entry = ctk.CTkEntry(
            percent_frame,
            placeholder_text="Enter percentage (e.g., -10, +25)"
        )
        self.percent_entry.pack(pady=5, padx=20, fill="x")

        # Preview section
        preview_frame = ctk.CTkFrame(main_frame)
        preview_frame.pack(fill="both", expand=True, padx=10, pady=10)

        preview_label = ctk.CTkLabel(preview_frame, text="📊 Preview Changes:")
        preview_label.pack(pady=5)

        self.preview_text = ctk.CTkTextbox(preview_frame, height=150)
        self.preview_text.pack(fill="both", expand=True, padx=10, pady=5)

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        preview_btn = ctk.CTkButton(
            button_frame,
            text="👁️ Preview Changes",
            command=self.preview_changes
        )
        preview_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="✅ Apply Changes",
            command=self.apply_changes
        )
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Cancel",
            command=self.window.destroy
        )
        cancel_btn.pack(side="right", padx=5)

    def preview_changes(self):
        """Preview the changes without applying them"""
        try:
            percentage_str = self.percent_entry.get().strip()
            if not percentage_str:
                messagebox.showwarning("Warning", "Please enter a percentage")
                return

            # Handle + prefix
            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]

            percentage = float(percentage_str)
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return

            field = self.price_field_var.get()
            preview_text = f"Preview for {field} change of {percentage:+.1f}%:\n\n"

            count = 0
            total_items = 0
            sample_changes = []

            # Count total and collect sample changes
            for trader_name, trader_items in self.data.get("economy-override", {}).get("traders", {}).items():
                for item in trader_items:
                    if field in item and item[field] not in ["null", "-1", ""]:
                        try:
                            current_price = float(item[field])
                            new_price = max(1, round(current_price * (1 + percentage / 100)))
                            total_items += 1

                            if count < 10:  # Collect first 10 for preview
                                sample_changes.append(f"{trader_name}: {item.get('tradeable-code', 'Unknown')} - {current_price} → {new_price}")
                                count += 1
                        except (ValueError, TypeError):
                            continue

            # Build preview text
            for change in sample_changes:
                preview_text += change + "\n"

            if total_items > 10:
                preview_text += f"\n... and {total_items - 10} more items\n"

            preview_text += f"\nTotal items to be changed: {total_items}"

            self.preview_text.delete("1.0", "end")
            self.preview_text.insert("1.0", preview_text)

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")

    def apply_changes(self):
        """Apply the global price changes"""
        try:
            percentage_str = self.percent_entry.get().strip()
            if not percentage_str:
                messagebox.showwarning("Warning", "Please enter a percentage")
                return

            # Handle + prefix
            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]

            percentage = float(percentage_str)
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return

            field = self.price_field_var.get()

            if messagebox.askyesno("Confirm", f"Apply {percentage:+.1f}% change to {field} for all items?"):
                count = 0
                for trader_items in self.data.get("economy-override", {}).get("traders", {}).values():
                    for item in trader_items:
                        if field in item and item[field] not in ["null", "-1", ""]:
                            try:
                                current_price = float(item[field])
                                new_price = max(1, round(current_price * (1 + percentage / 100)))
                                item[field] = str(new_price)
                                count += 1
                            except (ValueError, TypeError):
                                continue

                self.status_callback(f"Applied {percentage:+.1f}% change to {count} items")
                messagebox.showinfo("Success", f"Successfully updated {count} items")
                self.window.destroy()

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")


class EconomyFieldsDialog:
    def __init__(self, parent, data, status_callback):
        self.data = data
        self.status_callback = status_callback

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("⚙️ Edit Economy Fields")
        self.window.geometry("800x600")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the economy fields interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="⚙️ Economy Fields Editor",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Scrollable frame for fields
        scroll_frame = ctk.CTkScrollableFrame(main_frame)
        scroll_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Economy fields with default values
        self.economy_fields = {
            "economy-reset-time-hours": "-1.0",
            "prices-randomization-time-hours": "-1.0",
            "tradeable-rotation-time-ingame-hours-min": "48.0",
            "tradeable-rotation-time-ingame-hours-max": "96.0",
            "tradeable-rotation-time-of-day-min": "8.0",
            "tradeable-rotation-time-of-day-max": "16.0",
            "fully-restock-tradeable-hours": "2.0",
            "trader-funds-change-rate-per-hour-multiplier": "1.0",
            "prices-subject-to-player-count": "1",
            "gold-price-subject-to-global-multiplier": "1",
            "economy-logging": "1",
            "traders-unlimited-funds": "0",
            "traders-unlimited-stock": "0",
            "only-after-player-sale-tradeable-availability-enabled": "1",
            "tradeable-rotation-enabled": "1",
            "enable-fame-point-requirement": "1"
        }

        self.field_entries = {}

        # Create entry fields for each economy field
        for field_name, default_value in self.economy_fields.items():
            field_frame = ctk.CTkFrame(scroll_frame)
            field_frame.pack(fill="x", padx=5, pady=3)

            # Get current value from data or use default
            current_value = self.data.get("economy-override", {}).get(field_name, default_value)

            label = ctk.CTkLabel(
                field_frame,
                text=field_name.replace("-", " ").title(),
                width=400,
                anchor="w"
            )
            label.pack(side="left", padx=10, pady=5)

            entry = ctk.CTkEntry(field_frame, width=120)
            entry.pack(side="right", padx=10, pady=5)
            entry.insert(0, str(current_value))

            self.field_entries[field_name] = entry

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        save_btn = ctk.CTkButton(
            button_frame,
            text="💾 Save Changes",
            command=self.save_changes
        )
        save_btn.pack(side="left", padx=5)

        reset_btn = ctk.CTkButton(
            button_frame,
            text="🔄 Reset to Defaults",
            command=self.reset_to_defaults
        )
        reset_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Cancel",
            command=self.window.destroy
        )
        cancel_btn.pack(side="right", padx=5)

    def save_changes(self):
        """Save the economy field changes"""
        try:
            # Ensure economy-override section exists
            if "economy-override" not in self.data:
                self.data["economy-override"] = {}

            changes_made = 0
            for field_name, entry in self.field_entries.items():
                new_value = entry.get().strip()
                if new_value:
                    old_value = self.data["economy-override"].get(field_name, "")
                    if old_value != new_value:
                        self.data["economy-override"][field_name] = new_value
                        changes_made += 1

            self.status_callback(f"Updated {changes_made} economy fields")
            messagebox.showinfo("Success", f"Successfully updated {changes_made} fields")
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save changes: {str(e)}")

    def reset_to_defaults(self):
        """Reset all fields to default values"""
        if messagebox.askyesno("Confirm Reset", "Reset all fields to default values?"):
            for field_name, entry in self.field_entries.items():
                entry.delete(0, "end")
                entry.insert(0, self.economy_fields[field_name])


class MerchantLevelDialog:
    def __init__(self, parent, data, status_callback):
        self.data = data
        self.status_callback = status_callback

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("👤 Edit Merchant Level")
        self.window.geometry("900x700")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the merchant level interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="👤 Edit Specific Merchants",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Selection frame
        selection_frame = ctk.CTkFrame(main_frame)
        selection_frame.pack(fill="x", padx=10, pady=10)

        # Outpost selection
        outpost_frame = ctk.CTkFrame(selection_frame)
        outpost_frame.pack(side="left", fill="both", expand=True, padx=5)

        outpost_label = ctk.CTkLabel(outpost_frame, text="🏢 Select Outpost:")
        outpost_label.pack(pady=5)

        self.outpost_var = ctk.StringVar(value="A_0")
        outpost_menu = ctk.CTkOptionMenu(
            outpost_frame,
            variable=self.outpost_var,
            values=["A_0", "B_4", "C_2", "Z_3"],
            command=self.update_merchant_list
        )
        outpost_menu.pack(pady=5, padx=10, fill="x")

        # Price field selection
        field_frame = ctk.CTkFrame(selection_frame)
        field_frame.pack(side="right", fill="both", expand=True, padx=5)

        field_label = ctk.CTkLabel(field_frame, text="💰 Price Field:")
        field_label.pack(pady=5)

        self.price_field_var = ctk.StringVar(value="base-purchase-price")
        field_radio1 = ctk.CTkRadioButton(
            field_frame,
            text="Purchase Price",
            variable=self.price_field_var,
            value="base-purchase-price"
        )
        field_radio1.pack(pady=2)

        field_radio2 = ctk.CTkRadioButton(
            field_frame,
            text="Sell Price",
            variable=self.price_field_var,
            value="base-sell-price"
        )
        field_radio2.pack(pady=2)

        # Merchant selection
        merchant_frame = ctk.CTkFrame(main_frame)
        merchant_frame.pack(fill="both", expand=True, padx=10, pady=10)

        merchant_label = ctk.CTkLabel(merchant_frame, text="👤 Select Merchant:")
        merchant_label.pack(pady=5)

        # Create frame for listbox with scrollbar
        listbox_frame = ctk.CTkFrame(merchant_frame)
        listbox_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Merchant listbox with scrollbar
        self.merchant_listbox = tk.Listbox(listbox_frame, height=12)
        scrollbar = tk.Scrollbar(listbox_frame, orient="vertical", command=self.merchant_listbox.yview)
        self.merchant_listbox.configure(yscrollcommand=scrollbar.set)

        self.merchant_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        self.merchant_listbox.bind('<<ListboxSelect>>', self.on_merchant_select)

        # Adjustment controls
        adjust_frame = ctk.CTkFrame(main_frame)
        adjust_frame.pack(fill="x", padx=10, pady=10)

        # Percentage input
        percent_label = ctk.CTkLabel(adjust_frame, text="📊 Percentage Change (-99 to +100):")
        percent_label.pack(pady=5)

        self.percent_entry = ctk.CTkEntry(
            adjust_frame,
            placeholder_text="Enter percentage (e.g., -10, +25)"
        )
        self.percent_entry.pack(pady=5, padx=20, fill="x")

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="✅ Apply to Selected Merchant",
            command=self.apply_to_merchant
        )
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Close",
            command=self.window.destroy
        )
        cancel_btn.pack(side="right", padx=5)

        # Initialize merchant list
        self.update_merchant_list()

    def update_merchant_list(self, *args):
        """Update the merchant list based on selected outpost"""
        outpost = self.outpost_var.get()
        self.merchant_listbox.delete(0, tk.END)

        if "economy-override" in self.data and "traders" in self.data["economy-override"]:
            merchants = [
                trader for trader in self.data["economy-override"]["traders"].keys()
                if trader.startswith(outpost)
            ]
            for merchant in sorted(merchants):
                self.merchant_listbox.insert(tk.END, merchant)

    def on_merchant_select(self, event):
        """Handle merchant selection"""
        selection = self.merchant_listbox.curselection()
        if selection:
            merchant = self.merchant_listbox.get(selection[0])
            # Could show merchant details here

    def apply_to_merchant(self):
        """Apply percentage change to selected merchant"""
        selection = self.merchant_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a merchant")
            return

        try:
            percentage_str = self.percent_entry.get().strip()
            if not percentage_str:
                messagebox.showwarning("Warning", "Please enter a percentage")
                return

            # Handle + prefix
            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]

            percentage = float(percentage_str)
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return

            merchant = self.merchant_listbox.get(selection[0])
            field = self.price_field_var.get()

            if messagebox.askyesno("Confirm", f"Apply {percentage:+.1f}% change to {field} for {merchant}?"):
                count = 0
                trader_items = self.data["economy-override"]["traders"][merchant]

                for item in trader_items:
                    if field in item and item[field] not in ["null", "-1", ""]:
                        try:
                            current_price = float(item[field])
                            new_price = max(1, round(current_price * (1 + percentage / 100)))
                            item[field] = str(new_price)
                            count += 1
                        except (ValueError, TypeError):
                            continue

                self.status_callback(f"Updated {count} items for {merchant}")
                messagebox.showinfo("Success", f"Successfully updated {count} items for {merchant}")

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")
        except KeyError:
            messagebox.showerror("Error", "Merchant data not found")
