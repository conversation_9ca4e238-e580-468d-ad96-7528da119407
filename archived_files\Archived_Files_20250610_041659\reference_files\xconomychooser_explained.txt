**XconomyChooser - Technical Documentation**

---

## 1. Overview

**Purpose:**
XconomyChooser is a comprehensive GUI-based utility designed to manage, select, and upload economic configuration files (such as SCUM's `EconomyOverrides.json` or `ServerSettings.ini`) via SFTP to a remote game server. It helps streamline the process of modifying economic data dynamically and supports both manual and scheduled configuration switching.

**Target Users:**

* Game administrators managing SCUM private or public servers
* Technical modders experimenting with game economy tweaks
* Community tool developers needing a portable config deployer

**Core Use Cases:**

* Uploading a selected JSON/INI economy config to a SCUM server via secure SFTP
* Automating economy changes with scheduled uploads
* Selecting files from a pool at random for dynamic economy balancing

---

## 2. Technical Architecture

**Programming Language:** Python 3.11+

**Libraries and Dependencies:**

* `tkinter`: GUI framework
* `paramiko`: SFTP and SSH functionality
* `threading`, `time`, `os`, `json`, `datetime`, `random`: Python standard libraries
* `cryptography.fernet`: (Optional) for secure credential storage

**File Structure:**

* `main.py`: Entry point and GUI logic
* `sftp_handler.py`: Handles SFTP file transfers, authentication, and path normalization
* `scheduler.py`: Manages time-based task execution
* `config_manager.py`: Saves and loads persistent settings in JSON
* `log_writer.py`: Tiered logging (INFO, WARNING, ERROR, DEBUG)
* `encryption_util.py`: Optional credential encryption/decryption support

**Application Flow:**

1. GUI initializes and loads user preferences (last-used path, creds)
2. User selects a file or folder, sets schedule and SFTP details
3. Upload can be triggered manually or queued for future execution
4. Transfer status, logs, and errors are displayed in the GUI console

---

## 3. Core Features & Special Logic

### 🔍 File Selection Logic

* FileChooser validates extensions `.json` and `.ini`
* Path remembered across sessions if `SAVE_CONFIG` is enabled
* Nested subfolder detection (planned feature)

### 🎲 Random Selection

* When folder path is selected and `Random` is toggled:

  * All valid config files are loaded
  * A `random.choice()` is used during each scheduled upload
  * Optionally filters by prefix/suffix (e.g., `economy_*.json`)

### 🕒 Scheduler Engine

* Implemented using `threading.Timer`
* Supports interval (e.g., every X hours) and absolute (e.g., daily at HH\:MM)
* On each tick, invokes upload task with the chosen file
* Reschedules itself recursively for persistent jobs
* Thread-safe flags to cancel upcoming tasks if GUI closes

### 🔐 Credential Management

* GUI fields: Host, Port, Username, Password
* Password is obscured in UI; optional encryption via Fernet
* Saved credentials stored encrypted if `XCHOOSE_SECURE_CREDS=1`

### 🪵 Logging and Debugging

* Console displays real-time logs with timestamp and log level
* Log file optionally enabled with `XCHOOSE_LOG_FILE=path.log`
* Logging tiers:

  * `[INFO]`: Normal operations
  * `[WARNING]`: Recoverable issues
  * `[ERROR]`: Failures (e.g., connection refused)
  * `[DEBUG]`: Verbose tracing, including file paths, threading states
* Debug flag can be toggled via env: `XCHOOSE_DEBUG=1`

### 💥 Error Handling

* Upload errors are caught with specific messages (e.g., wrong path, 404)
* GUI continues to run on background SFTP failure
* Fails gracefully on missing dependencies with user popup

---

## 4. Configuration & Usage

### Installation

```bash
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt
python main.py
```

### GUI Options

* File/Folder path input (manual or via browse dialog)
* Host/IP, Port, Username, Password (obfuscated)
* Mode: Manual Upload | Timed Upload
* Randomize (on/off)
* Save Settings checkbox
* Output log console (scrollable)

### Environment Variables

| Variable               | Description                         |
| ---------------------- | ----------------------------------- |
| `XCHOOSE_SAVE_CONFIG`  | Remembers settings on exit          |
| `XCHOOSE_LOG_FILE`     | Path to write logs to file          |
| `XCHOOSE_DEBUG`        | Enables debug-level logging         |
| `XCHOOSE_SECURE_CREDS` | Activates encryption of credentials |

---

## 5. Deployment & Maintenance

### Supported OS

* Windows 10/11
* macOS Ventura+
* Ubuntu 22.04+

### Packaging

* Uses `pyinstaller --noconsole --onefile main.py` to generate a standalone executable
* Build script includes all assets and optional README

### Log Rotation (Optional Enhancement)

* Support for 10MB log rotation with backup count via `logging.handlers.RotatingFileHandler`

### Maintenance Tasks

* Keep Paramiko up to date to avoid CVEs
* Purge stored credentials on shared systems regularly
* Validate file schema for SCUM version compatibility

---

## 6. Known Issues & Roadmap

### Known Issues

* SFTP password field sometimes resets on schedule update
* GUI scaling is fixed; poor DPI scaling on 4K monitors
* Debug log too verbose in production mode without filtering

### Planned Features

* Drag-and-drop file support
* Scheduled profile switching with nested folder pool
* Native task tray minimization support
* Version checker that polls GitHub
* Better upload progress bar

---

## 7. Appendices

### Example Upload Config (JSON)

```json
{
  "mode": "scheduled",
  "time": "03:30",
  "target": "./configs/rotating/",
  "sftp": {
    "host": "************",
    "port": 22,
    "username": "admin",
    "password": "********"
  },
  "random": true
}
```

### Sample Output Logs

```
[INFO] Connected to ************ via SFTP
[DEBUG] Selected config: ./configs/summer_prices.json
[INFO] Upload successful to /SCUM/Server/Config/EconomyOverrides.json
[WARNING] Schedule missed due to system sleep
```

### Screenshot

*\[Insert annotated GUI screenshot here during packaging phase]*

**XconomyChooser - Technical Documentation**

---

## 1. Overview

**Purpose:**
XconomyChooser is a comprehensive GUI-based utility designed to manage, categorize, modify, and upload economic configuration files (such as SCUM's `EconomyOverrides.json` or `ServerSettings.ini`) via SFTP to a remote game server. It supports both manual and scheduled deployment modes with layered logic and robust undo/redo capability.

**Target Users:**

* Game administrators managing SCUM private or public servers
* Technical modders experimenting with game economy tweaks
* Community tool developers needing a portable config deployer

**Core Use Cases:**

* Uploading a selected JSON/INI economy config to a SCUM server via secure SFTP
* Automating economy changes with scheduled uploads
* Categorizing and mass-adjusting specific economy types (e.g., merchants, items)
* Reversing previous edits or applying batches in redo stacks

---

## 2. Categorization System (“Buckets”)

XconomyChooser allows items and config objects to be dropped into **logical buckets** for structured adjustments. These categories are used for visual filtering, grouped logic application, and intelligent scheduling.

### Categories Defined:

* **Items** – Weaponry, tools, consumables (base classes or individual)
* **Groups** – Spawn groups, location-based templates
* **Merchants** – Trader-specific economy entries (e.g., Trader\_B, Trader\_C)
* **Selected** – Manual picks from the GUI grid, tagged during edit sessions

Each bucket is a virtual layer users can fill by:

* Dragging objects from the full dataset
* Filtering via regex, tags, type
* Importing from saved selection sets (`*.xbucket` files)

### Use Cases:

* Drop all "Weapon\_" items into a bucket and apply +15% base price
* Target Trader\_A and apply -20% sell price to all tools
* Select a curated list and apply a specific multiplier profile (e.g., NightRaid.json)

### Application Flow:

```
[ITEMS SELECTED] → [BUCKET ASSIGNED] → [ADJUSTMENT PROFILE APPLIED] → [PREVIEW DELTA] → [EXECUTE or UNDO]
```

---

## 3. Undo / Redo System (History Stack)

XconomyChooser includes an extensive **undo/redo engine** designed to give users complete safety when mass-adjusting configuration values.

### 🔙 Undo Stack

* Every operation that mutates config state is logged as an atomic delta (patch)
* Undo stack is replayable and allows stepping back one or multiple layers
* Undo is available even after switching categories or folders

### 🔁 Redo Stack

* Redo applies forward from most recent undone action
* Redo stack is cleared if new actions are performed post-undo (branch safety)

### Supported Actions:

* Percentage adjustments (+/-)
* Profile applications
* Bucket assignments
* Manual overrides
* Scheduled save and load states

### Architecture:

```python
class ChangeHistory:
    def __init__(self):
        self.undo_stack = []
        self.redo_stack = []

    def record(self, patch):
        self.undo_stack.append(patch)
        self.redo_stack.clear()

    def undo(self):
        if self.undo_stack:
            patch = self.undo_stack.pop()
            self.redo_stack.append(patch)
            return patch.inverse()

    def redo(self):
        if self.redo_stack:
            patch = self.redo_stack.pop()
            self.undo_stack.append(patch)
            return patch
```

### Visual Feedback:

* Modified rows are highlighted
* A breadcrumb trail shows recent patch history
* Ctrl+Z / Ctrl+Y support via keyboard

### Edge Protection:

* Undo is disabled once upload has occurred (unless rollback file saved)
* User can export pre-edit state before mass changes

---

*The next sections continue with scheduler, credential handling, file validation, SFTP deployment, and logging as previously outlined.*

