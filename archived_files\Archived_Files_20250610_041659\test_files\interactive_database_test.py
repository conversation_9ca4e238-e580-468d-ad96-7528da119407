#!/usr/bin/env python3
"""
Interactive Database Test for SCUM Economy Chooser
Creates a real SQLite database with actual economy data for testing
"""

import sqlite3
import json
import os
from datetime import datetime
from typing import Dict, List, Any

class SCUMEconomyDatabase:
    """Production-ready database manager for SCUM Economy data"""
    
    def __init__(self, db_path: str = "scum_economy_test.db"):
        self.db_path = db_path
        self.connection = None
        self.setup_database()
    
    def setup_database(self):
        """Initialize database with proper schema"""
        self.connection = sqlite3.connect(self.db_path)
        self.connection.row_factory = sqlite3.Row
        self.create_schema()
        print(f"✅ Database created: {self.db_path}")
    
    def create_schema(self):
        """Create comprehensive database schema"""
        cursor = self.connection.cursor()
        
        # Main economy items table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS economy_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tradeable_code TEXT UNIQUE NOT NULL,
                base_purchase_price INTEGER NOT NULL CHECK(base_purchase_price >= 1),
                base_sell_price INTEGER NOT NULL CHECK(base_sell_price >= 1),
                max_purchase_price INTEGER NOT NULL CHECK(max_purchase_price >= 1),
                max_sell_price INTEGER NOT NULL CHECK(max_sell_price >= 1),
                min_purchase_price INTEGER NOT NULL CHECK(min_purchase_price >= 1),
                min_sell_price INTEGER NOT NULL CHECK(min_sell_price >= 1),
                category TEXT,
                subcategory TEXT,
                fish_category TEXT,  -- F.I.S.H. Logic category
                item_type TEXT,      -- weapon, consumable, equipment, etc.
                rarity TEXT,         -- common, rare, epic, legendary
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Change history for audit trail
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS change_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tradeable_code TEXT NOT NULL,
                field_name TEXT NOT NULL,
                old_value INTEGER,
                new_value INTEGER,
                change_type TEXT DEFAULT 'manual',
                description TEXT,
                user_id TEXT DEFAULT 'system',
                session_id TEXT,
                gui_version TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (tradeable_code) REFERENCES economy_items(tradeable_code)
            )
        ''')
        
        # Custom buckets for user-defined categories
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS custom_buckets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bucket_name TEXT UNIQUE NOT NULL,
                description TEXT,
                filters TEXT,  -- JSON string with filter rules
                color_scheme TEXT,
                user_id TEXT DEFAULT 'system',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # User sessions for multi-user support
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE NOT NULL,
                user_id TEXT,
                start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                end_time TIMESTAMP,
                changes_count INTEGER DEFAULT 0,
                app_version TEXT,
                gui_mode TEXT  -- enhanced, devbuild, cli
            )
        ''')
        
        # Create indexes for performance
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_tradeable_code ON economy_items(tradeable_code)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_category ON economy_items(category)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_fish_category ON economy_items(fish_category)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_change_history_code ON change_history(tradeable_code)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_change_history_time ON change_history(created_at)')
        
        self.connection.commit()
        print("✅ Database schema created with indexes")
    
    def load_from_json(self, json_file: str = "economyoverride.json"):
        """Load data from JSON file into database"""
        if not os.path.exists(json_file):
            print(f"❌ JSON file not found: {json_file}")
            return False
        
        with open(json_file, 'r') as f:
            data = json.load(f)

        cursor = self.connection.cursor()
        items_loaded = 0

        # Import F.I.S.H. categorization
        try:
            from scum_economy_gui_enhanced import FISHLogicEngine
            fish_engine = FISHLogicEngine()
            print("✅ F.I.S.H. Logic engine loaded")
        except:
            fish_engine = None
            print("⚠️ F.I.S.H. Logic engine not available")

        # Handle the actual SCUM economy file structure
        economy_override = data.get('economy-override', {})
        traders = economy_override.get('traders', {})

        # Extract items from all traders
        all_items = {}
        for trader_name, trader_items in traders.items():
            print(f"📦 Processing trader: {trader_name}")
            for item in trader_items:
                code = item.get('tradeable-code')
                if code and code not in all_items:
                    # Convert string prices to integers, handle null values
                    def safe_int(value, default=1):
                        try:
                            if value is None or value == 'null' or value == '':
                                return default
                            return max(1, int(value))  # SCUM minimum price is 1
                        except (ValueError, TypeError):
                            return default

                    base_purchase = safe_int(item.get('base-purchase-price'))
                    base_sell = safe_int(item.get('base-sell-price'))

                    all_items[code] = {
                        'base-purchase-price': base_purchase,
                        'base-sell-price': base_sell,
                        'max-purchase-price': base_purchase * 2,  # Estimate
                        'max-sell-price': base_sell * 2,  # Estimate
                        'min-purchase-price': max(1, base_purchase // 2),  # Estimate
                        'min-sell-price': max(1, base_sell // 2),  # Estimate
                        'trader': trader_name
                    }

        print(f"📊 Found {len(all_items)} unique items across all traders")

        for code, item_data in all_items.items():
            try:
                # Get F.I.S.H. categorization
                fish_category = None
                item_type = None
                if fish_engine:
                    fish_result = fish_engine.categorize_item(code)
                    fish_category = fish_result.get('category', 'Uncategorized')
                    
                    # Determine item type
                    if code.startswith('Weapon_'):
                        item_type = 'weapon'
                    elif code.startswith('Cal_'):
                        item_type = 'ammunition'
                    elif code.startswith('Fish_'):
                        item_type = 'consumable'
                    elif code.startswith('Crafted_'):
                        item_type = 'crafted'
                    elif 'Improvised' in code:
                        item_type = 'improvised'
                    else:
                        item_type = 'equipment'
                
                cursor.execute('''
                    INSERT OR REPLACE INTO economy_items 
                    (tradeable_code, base_purchase_price, base_sell_price, 
                     max_purchase_price, max_sell_price, min_purchase_price, 
                     min_sell_price, fish_category, item_type, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (
                    code,
                    item_data.get('base-purchase-price', 1),
                    item_data.get('base-sell-price', 1),
                    item_data.get('max-purchase-price', 1),
                    item_data.get('max-sell-price', 1),
                    item_data.get('min-purchase-price', 1),
                    item_data.get('min-sell-price', 1),
                    fish_category,
                    item_type
                ))
                items_loaded += 1
                
            except Exception as e:
                print(f"❌ Failed to load {code}: {e}")
        
        self.connection.commit()
        print(f"✅ Loaded {items_loaded} items from {json_file}")
        return True
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive database statistics"""
        cursor = self.connection.cursor()
        
        stats = {}
        
        # Total items
        cursor.execute('SELECT COUNT(*) as total FROM economy_items')
        stats['total_items'] = cursor.fetchone()['total']
        
        # Items by F.I.S.H. category
        cursor.execute('''
            SELECT fish_category, COUNT(*) as count 
            FROM economy_items 
            WHERE fish_category IS NOT NULL 
            GROUP BY fish_category 
            ORDER BY count DESC
        ''')
        stats['fish_categories'] = dict(cursor.fetchall())
        
        # Items by type
        cursor.execute('''
            SELECT item_type, COUNT(*) as count 
            FROM economy_items 
            WHERE item_type IS NOT NULL 
            GROUP BY item_type 
            ORDER BY count DESC
        ''')
        stats['item_types'] = dict(cursor.fetchall())
        
        # Price ranges
        cursor.execute('''
            SELECT
                MIN(base_purchase_price) as min_price,
                MAX(base_purchase_price) as max_price,
                AVG(base_purchase_price) as avg_price,
                COUNT(*) as total_items
            FROM economy_items
        ''')
        price_stats = cursor.fetchone()
        stats['price_range'] = {
            'min': price_stats['min_price'] or 0,
            'max': price_stats['max_price'] or 0,
            'average': round(price_stats['avg_price'] or 0, 2),
            'total': price_stats['total_items'] or 0
        }
        
        # Most expensive items
        cursor.execute('''
            SELECT tradeable_code, base_purchase_price, fish_category
            FROM economy_items 
            ORDER BY base_purchase_price DESC 
            LIMIT 10
        ''')
        stats['most_expensive'] = [dict(row) for row in cursor.fetchall()]
        
        # Change history count
        cursor.execute('SELECT COUNT(*) as total FROM change_history')
        stats['total_changes'] = cursor.fetchone()['total']
        
        return stats
    
    def search_items(self, search_term: str, category: str = None) -> List[Dict]:
        """Search items with optional category filter"""
        cursor = self.connection.cursor()
        
        query = '''
            SELECT tradeable_code, base_purchase_price, base_sell_price, 
                   fish_category, item_type
            FROM economy_items 
            WHERE tradeable_code LIKE ?
        '''
        params = [f'%{search_term}%']
        
        if category:
            query += ' AND fish_category = ?'
            params.append(category)
        
        query += ' ORDER BY tradeable_code LIMIT 20'
        
        cursor.execute(query, params)
        return [dict(row) for row in cursor.fetchall()]
    
    def log_test_changes(self):
        """Log some test changes for demonstration"""
        cursor = self.connection.cursor()
        
        test_changes = [
            ('Weapon_AK47', 'base-purchase-price', 1000, 1200, 'Price increase for balance'),
            ('2H_Baseball_Bat', 'base-purchase-price', 150, 175, 'Minor price adjustment'),
            ('Fish_Bass', 'base-sell-price', 15, 18, 'Fish market price increase'),
            ('Cal_9mm', 'base-purchase-price', 5, 6, 'Ammunition price adjustment'),
            ('Crafted_Backpack', 'base-purchase-price', 200, 220, 'Crafting cost increase')
        ]
        
        session_id = f"test_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        for code, field, old_val, new_val, desc in test_changes:
            cursor.execute('''
                INSERT INTO change_history 
                (tradeable_code, field_name, old_value, new_value, 
                 description, session_id, gui_version)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (code, field, old_val, new_val, desc, session_id, 'v2.01-test'))
        
        self.connection.commit()
        print(f"✅ Logged {len(test_changes)} test changes")
    
    def create_sample_buckets(self):
        """Create sample custom buckets"""
        cursor = self.connection.cursor()
        
        sample_buckets = [
            {
                'name': 'High-Value Weapons',
                'description': 'Weapons worth more than $500',
                'filters': {
                    'rules': [
                        {'type': 'starts_with', 'value': 'Weapon_'},
                        {'type': 'price_greater_than', 'field': 'base_purchase_price', 'value': 500}
                    ]
                }
            },
            {
                'name': 'Two-Handed Arsenal',
                'description': 'All two-handed weapons and tools',
                'filters': {
                    'rules': [
                        {'type': 'starts_with', 'value': '2H_'}
                    ]
                }
            },
            {
                'name': 'Survival Essentials',
                'description': 'Fish, crafted items, and improvised gear',
                'filters': {
                    'rules': [
                        {'type': 'starts_with_any', 'values': ['Fish_', 'Crafted_']},
                        {'type': 'contains', 'value': 'Improvised'}
                    ]
                }
            }
        ]
        
        for bucket in sample_buckets:
            cursor.execute('''
                INSERT OR REPLACE INTO custom_buckets 
                (bucket_name, description, filters)
                VALUES (?, ?, ?)
            ''', (bucket['name'], bucket['description'], json.dumps(bucket['filters'])))
        
        self.connection.commit()
        print(f"✅ Created {len(sample_buckets)} sample buckets")
    
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            print("✅ Database connection closed")

def run_interactive_tests():
    """Run comprehensive interactive database tests"""
    print("🗄️ INTERACTIVE DATABASE TESTING")
    print("=" * 50)
    
    # Initialize database
    db = SCUMEconomyDatabase()
    
    try:
        # Test 1: Load real economy data
        print("\n📥 Test 1: Loading real economy data...")
        success = db.load_from_json("economyoverride.json")
        
        if success:
            # Test 2: Get statistics
            print("\n📊 Test 2: Database statistics...")
            stats = db.get_statistics()
            
            print(f"📦 Total items: {stats['total_items']}")
            print(f"💰 Price range: ${stats['price_range']['min']} - ${stats['price_range']['max']}")
            print(f"📈 Average price: ${stats['price_range']['average']}")
            
            print("\n🐟 F.I.S.H. Categories:")
            for category, count in list(stats['fish_categories'].items())[:10]:
                print(f"   {category}: {count} items")
            
            print("\n🏷️ Item Types:")
            for item_type, count in stats['item_types'].items():
                print(f"   {item_type}: {count} items")
            
            print("\n💎 Most Expensive Items:")
            for item in stats['most_expensive'][:5]:
                print(f"   {item['tradeable_code']}: ${item['base_purchase_price']} ({item['fish_category']})")
            
            # Test 3: Search functionality
            print("\n🔍 Test 3: Search functionality...")
            weapon_results = db.search_items("AK", "Weapons_Firearms")
            print(f"🔫 Found {len(weapon_results)} AK weapons:")
            for item in weapon_results[:3]:
                print(f"   {item['tradeable_code']}: ${item['base_purchase_price']}")
            
            fish_results = db.search_items("Fish_")
            print(f"🐟 Found {len(fish_results)} fish items:")
            for item in fish_results[:3]:
                print(f"   {item['tradeable_code']}: ${item['base_purchase_price']}")
            
            # Test 4: Change logging
            print("\n📝 Test 4: Change logging...")
            db.log_test_changes()
            
            # Test 5: Custom buckets
            print("\n🪣 Test 5: Custom buckets...")
            db.create_sample_buckets()
            
            print("\n🎉 All database tests completed successfully!")
            print(f"\n📁 Database file created: {db.db_path}")
            print("💡 You can now open this database with:")
            print("   • DB Browser for SQLite")
            print("   • DBeaver")
            print("   • SQLiteStudio")
            print("   • VS Code SQLite extension")
            
        else:
            print("❌ Failed to load economy data")
    
    finally:
        db.close()

if __name__ == "__main__":
    run_interactive_tests()
