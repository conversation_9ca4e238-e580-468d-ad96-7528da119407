#!/usr/bin/env python3
"""
Test the 4-Tier Debug System for XconomyChooser v2.01
Demonstrates all debug levels and features
"""

import os
import sys
import time

# Add the current directory to path to import our debug system
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_debug_levels():
    """Test all debug levels"""
    print("🧪 Testing 4-Tier Debug System")
    print("=" * 50)
    
    # Test different debug levels
    levels = [
        (0, "No Debug"),
        (1, "ERROR only"),
        (2, "INFO + ERROR"),
        (3, "DEBUG + INFO + ERROR"),
        (4, "TRACE + DEBUG + INFO + ERROR")
    ]
    
    for level, description in levels:
        print(f"\n🔧 Testing Level {level}: {description}")
        print("-" * 30)
        
        # Set environment variable
        os.environ['XCHOOSE_DEBUG_LEVEL'] = str(level)
        
        # Import debug tracer (will reinitialize with new level)
        from scum_economy_gui_enhanced import DebugTracer
        tracer = DebugTracer()
        
        # Test all message types
        tracer.error("This is an ERROR message", Exception("Test exception"))
        tracer.info("This is an INFO message")
        tracer.debug("This is a DEBUG message")
        tracer.trace("This is a TRACE message")
        
        # Test function tracing
        tracer.trace_enter("test_function", args=["arg1", "arg2"], kwargs={"key": "value"})
        tracer.trace_var("test_variable", "test_value")
        tracer.trace_exit("test_function", result="success")
        
        # Test operation tracking
        tracer.operation_start("Test Operation", "Testing debug system")
        time.sleep(0.1)  # Simulate work
        tracer.operation_end("Test Operation", True, "Operation completed successfully")
        
        # Test performance timer
        with tracer.performance_timer("Performance Test"):
            time.sleep(0.05)  # Simulate work
        
        print()

def test_dev_mode():
    """Test dev mode activation"""
    print("\n🔧 Testing Dev Mode")
    print("=" * 30)
    
    # Test with dev mode enabled
    os.environ['XCHOOSE_DEV_MODE'] = '1'
    os.environ['XCHOOSE_DEBUG_LEVEL'] = '0'  # Should be overridden by dev mode
    
    from scum_economy_gui_enhanced import DebugTracer
    tracer = DebugTracer()
    
    print("Dev mode should automatically enable DEBUG level:")
    tracer.error("Error in dev mode")
    tracer.info("Info in dev mode")
    tracer.debug("Debug in dev mode (should be visible)")
    tracer.trace("Trace in dev mode (should not be visible unless level 4)")

def test_log_file():
    """Test log file creation"""
    print("\n📁 Testing Log File Creation")
    print("=" * 30)
    
    # Enable logging
    os.environ['XCHOOSE_DEBUG_LEVEL'] = '3'
    
    from scum_economy_gui_enhanced import DebugTracer
    tracer = DebugTracer()
    
    tracer.info("This message should be logged to file")
    tracer.debug("This debug message should also be logged")
    tracer.error("This error should definitely be logged")
    
    # Check if log file was created
    if os.path.exists('xconomy_debug.log'):
        print("✅ Log file created successfully!")
        with open('xconomy_debug.log', 'r') as f:
            content = f.read()
            if content:
                print("📄 Log file content preview:")
                lines = content.strip().split('\n')
                for line in lines[-5:]:  # Show last 5 lines
                    print(f"   {line}")
            else:
                print("⚠️ Log file is empty")
    else:
        print("❌ Log file was not created")

def test_real_world_scenario():
    """Test a real-world debugging scenario"""
    print("\n🌍 Testing Real-World Scenario")
    print("=" * 30)
    
    # Set debug level to TRACE for full visibility
    os.environ['XCHOOSE_DEBUG_LEVEL'] = '4'
    
    from scum_economy_gui_enhanced import DebugTracer
    tracer = DebugTracer()
    
    # Simulate loading a JSON file
    tracer.operation_start("JSON File Loading", "economy_data.json")
    
    tracer.trace_enter("load_json_file", args=["economy_data.json"])
    
    try:
        tracer.debug("Checking if file exists...")
        tracer.trace_var("file_path", "economy_data.json")
        
        # Simulate file operations
        tracer.debug("Opening file for reading...")
        tracer.trace("File opened successfully")
        
        tracer.debug("Parsing JSON content...")
        tracer.trace_var("json_size", "1.2MB")
        
        # Simulate processing
        with tracer.performance_timer("JSON Parsing"):
            time.sleep(0.1)
        
        tracer.info("JSON file loaded successfully")
        tracer.trace_var("items_loaded", 1547)
        
        tracer.trace_exit("load_json_file", result="success")
        tracer.operation_end("JSON File Loading", True, "1547 items loaded")
        
    except Exception as e:
        tracer.error("Failed to load JSON file", e)
        tracer.trace_exit("load_json_file", result="error")
        tracer.operation_end("JSON File Loading", False, str(e))

def cleanup():
    """Clean up test environment"""
    print("\n🧹 Cleaning up...")
    
    # Remove environment variables
    env_vars = ['XCHOOSE_DEBUG_LEVEL', 'XCHOOSE_DEV_MODE']
    for var in env_vars:
        if var in os.environ:
            del os.environ[var]
    
    print("✅ Environment cleaned up")

def main():
    """Run all debug system tests"""
    print("🚀 XconomyChooser v2.01 - 4-Tier Debug System Test")
    print("=" * 60)
    
    try:
        test_debug_levels()
        test_dev_mode()
        test_log_file()
        test_real_world_scenario()
        
        print("\n🎉 All debug system tests completed!")
        print("\n📋 Summary:")
        print("✅ 4-tier debug levels working")
        print("✅ Dev mode auto-activation working")
        print("✅ Log file creation working")
        print("✅ Performance timing working")
        print("✅ Function tracing working")
        print("✅ Operation tracking working")
        
        print("\n💡 Usage Instructions:")
        print("• Set XCHOOSE_DEBUG_LEVEL=1 for errors only")
        print("• Set XCHOOSE_DEBUG_LEVEL=2 for info + errors")
        print("• Set XCHOOSE_DEBUG_LEVEL=3 for debug + info + errors")
        print("• Set XCHOOSE_DEBUG_LEVEL=4 for full tracing")
        print("• Set XCHOOSE_DEV_MODE=1 to auto-enable debug level 3")
        print("• Check xconomy_debug.log for persistent logging")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    finally:
        cleanup()
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
