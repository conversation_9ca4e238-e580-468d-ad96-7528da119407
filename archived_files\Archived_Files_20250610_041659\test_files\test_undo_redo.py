#!/usr/bin/env python3
"""
Undo/Redo System Test Suite for XconomyChooser v2.01
Tests all undo/redo functionality including mass undo and intelligent undo
"""

import copy
import json
from datetime import datetime

# Import the UndoRedoManager from our main file
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestUndoRedoManager:
    """Test version of UndoRedoManager for testing"""
    def __init__(self, max_history=100):
        self.history = []
        self.current_index = -1
        self.max_history = max_history
    
    def save_state(self, data, description=""):
        """Save current state to history"""
        if self.current_index < len(self.history) - 1:
            self.history = self.history[:self.current_index + 1]
        
        state = {
            'data': copy.deepcopy(data),
            'description': description,
            'timestamp': datetime.now().strftime("%H:%M:%S")
        }
        self.history.append(state)
        
        if len(self.history) > self.max_history:
            self.history.pop(0)
        else:
            self.current_index += 1
    
    def can_undo(self):
        return self.current_index > 0 and len(self.history) > 1
    
    def can_redo(self):
        return self.current_index < len(self.history) - 1
    
    def undo(self):
        if self.can_undo():
            self.current_index -= 1
            return copy.deepcopy(self.history[self.current_index]['data'])
        return None
    
    def redo(self):
        if self.can_redo():
            self.current_index += 1
            return copy.deepcopy(self.history[self.current_index]['data'])
        return None
    
    def get_current_description(self):
        if 0 <= self.current_index < len(self.history):
            return self.history[self.current_index]['description']
        return ""
    
    def get_history_list(self):
        return [(i, entry['description'], entry['timestamp']) 
                for i, entry in enumerate(self.history)]
    
    def mass_undo(self, steps=5):
        if self.can_undo():
            target_index = max(0, self.current_index - steps)
            self.current_index = target_index
            return copy.deepcopy(self.history[self.current_index]['data'])
        return None
    
    def intelligent_undo(self):
        if not self.can_undo():
            return None

        current_state = self.history[self.current_index]
        description = current_state['description']

        # If current operation is a batch operation, find the start of the batch
        if any(keyword in description.lower() for keyword in ['batch', 'global', 'all', 'mass']):
            # Look backwards to find where batch operations started
            for i in range(self.current_index - 1, -1, -1):
                prev_description = self.history[i]['description']
                if not any(keyword in prev_description.lower() for keyword in ['batch', 'global', 'all', 'mass']):
                    # Found the state before batch operations started
                    self.current_index = i
                    return copy.deepcopy(self.history[self.current_index]['data'])

            # If all previous operations were batch operations, go to first non-batch or beginning
            if self.current_index > 0:
                self.current_index = 0
                return copy.deepcopy(self.history[self.current_index]['data'])

        # Default single undo for non-batch operations
        return self.undo()
    
    def undo_to_description(self, target_description):
        # Search backwards from current position
        for i in range(self.current_index - 1, -1, -1):
            if target_description.lower() in self.history[i]['description'].lower():
                self.current_index = i
                return copy.deepcopy(self.history[self.current_index]['data'])
        return None
    
    def get_undo_preview(self, steps=1):
        if not self.can_undo():
            return None
        
        target_index = max(0, self.current_index - steps)
        undone_operations = []
        
        for i in range(self.current_index, target_index, -1):
            if i < len(self.history):
                undone_operations.append(self.history[i]['description'])
        
        return {
            'target_description': self.history[target_index]['description'] if target_index < len(self.history) else "Initial state",
            'undone_operations': undone_operations,
            'steps': len(undone_operations)
        }

def test_basic_undo_redo():
    """Test basic undo/redo functionality"""
    print("🧪 Testing Basic Undo/Redo...")
    
    manager = TestUndoRedoManager()
    
    # Create test data
    data1 = {"items": {"weapon1": {"price": 100}}}
    data2 = {"items": {"weapon1": {"price": 110}}}
    data3 = {"items": {"weapon1": {"price": 120}}}
    
    # Save states
    manager.save_state(data1, "Initial state")
    manager.save_state(data2, "Increased price by 10%")
    manager.save_state(data3, "Increased price by 20%")
    
    # Test undo
    assert manager.can_undo() == True, "Should be able to undo"
    undone_data = manager.undo()
    assert undone_data["items"]["weapon1"]["price"] == 110, "Undo should return to previous state"
    
    # Test redo
    assert manager.can_redo() == True, "Should be able to redo"
    redone_data = manager.redo()
    assert redone_data["items"]["weapon1"]["price"] == 120, "Redo should return to next state"
    
    print("✅ Basic Undo/Redo: PASSED")

def test_mass_undo():
    """Test mass undo functionality"""
    print("🧪 Testing Mass Undo...")
    
    manager = TestUndoRedoManager()
    
    # Create multiple states
    for i in range(10):
        data = {"items": {"weapon1": {"price": 100 + i * 10}}}
        manager.save_state(data, f"Price change {i}")
    
    # Test mass undo
    original_index = manager.current_index
    mass_undone_data = manager.mass_undo(5)
    
    assert manager.current_index == original_index - 5, "Mass undo should move back 5 steps"
    assert mass_undone_data["items"]["weapon1"]["price"] == 140, "Mass undo should return correct state"
    
    print("✅ Mass Undo: PASSED")

def test_intelligent_undo():
    """Test intelligent undo functionality"""
    print("🧪 Testing Intelligent Undo...")

    manager = TestUndoRedoManager()

    # Create test scenario
    manager.save_state({"items": {"weapon1": {"price": 100}}}, "Initial state")
    manager.save_state({"items": {"weapon1": {"price": 105}}}, "Small change")
    manager.save_state({"items": {"weapon1": {"price": 200}}}, "Global price increase - batch operation")
    manager.save_state({"items": {"weapon1": {"price": 210}}}, "Global price increase - batch operation")
    manager.save_state({"items": {"weapon1": {"price": 220}}}, "Global price increase - batch operation")

    # Debug: Print history before intelligent undo
    print("   History before intelligent undo:")
    for i, (idx, desc, time) in enumerate(manager.get_history_list()):
        marker = " <-- CURRENT" if i == manager.current_index else ""
        print(f"     {i}: {desc}{marker}")

    # Test intelligent undo from a batch operation
    intelligent_undone_data = manager.intelligent_undo()

    # Debug: Print history after intelligent undo
    print("   History after intelligent undo:")
    for i, (idx, desc, time) in enumerate(manager.get_history_list()):
        marker = " <-- CURRENT" if i == manager.current_index else ""
        print(f"     {i}: {desc}{marker}")

    # Should skip back to before the batch operations
    expected_description = "Small change"
    actual_description = manager.get_current_description()

    print(f"   Expected: '{expected_description}', Got: '{actual_description}'")
    print(f"   Current index: {manager.current_index}, History length: {len(manager.history)}")

    assert actual_description == expected_description, f"Intelligent undo should skip batch operations. Expected '{expected_description}', got '{actual_description}'"
    assert intelligent_undone_data["items"]["weapon1"]["price"] == 105, "Should return to state before batch"

    print("✅ Intelligent Undo: PASSED")

def test_undo_to_description():
    """Test undo to specific description"""
    print("🧪 Testing Undo to Description...")
    
    manager = TestUndoRedoManager()
    
    # Create test states
    manager.save_state({"items": {"weapon1": {"price": 100}}}, "Initial state")
    manager.save_state({"items": {"weapon1": {"price": 110}}}, "Price increase")
    manager.save_state({"items": {"weapon1": {"price": 120}}}, "Another price increase")
    manager.save_state({"items": {"weapon1": {"price": 130}}}, "Final price increase")
    
    # Test undo to specific description
    target_data = manager.undo_to_description("Price increase")
    
    assert manager.get_current_description() == "Price increase", "Should undo to target description"
    assert target_data["items"]["weapon1"]["price"] == 110, "Should return correct data"
    
    print("✅ Undo to Description: PASSED")

def test_undo_preview():
    """Test undo preview functionality"""
    print("🧪 Testing Undo Preview...")
    
    manager = TestUndoRedoManager()
    
    # Create test states
    manager.save_state({"items": {"weapon1": {"price": 100}}}, "Initial state")
    manager.save_state({"items": {"weapon1": {"price": 110}}}, "Price increase 1")
    manager.save_state({"items": {"weapon1": {"price": 120}}}, "Price increase 2")
    manager.save_state({"items": {"weapon1": {"price": 130}}}, "Price increase 3")
    
    # Test preview
    preview = manager.get_undo_preview(2)
    
    assert preview is not None, "Preview should not be None"
    assert preview['steps'] == 2, "Preview should show 2 steps"
    assert "Price increase 1" in preview['target_description'], "Preview should show target description"
    
    print("✅ Undo Preview: PASSED")

def test_edge_cases():
    """Test edge cases and error conditions"""
    print("🧪 Testing Edge Cases...")
    
    manager = TestUndoRedoManager()
    
    # Test undo with no history
    assert manager.can_undo() == False, "Should not be able to undo with no history"
    assert manager.undo() is None, "Undo should return None with no history"
    
    # Test redo with no future states
    manager.save_state({"test": "data"}, "Test state")
    assert manager.can_redo() == False, "Should not be able to redo at end of history"
    assert manager.redo() is None, "Redo should return None at end of history"
    
    # Test mass undo beyond available history
    mass_result = manager.mass_undo(10)  # More than available
    assert manager.current_index == 0, "Mass undo should not go below 0"
    
    print("✅ Edge Cases: PASSED")

def test_performance():
    """Test performance with large history"""
    print("🧪 Testing Performance...")
    
    import time
    
    manager = TestUndoRedoManager(max_history=1000)
    
    # Create large history
    start_time = time.time()
    for i in range(500):
        data = {"items": {f"item_{i}": {"price": i * 10}}}
        manager.save_state(data, f"Change {i}")
    
    creation_time = time.time() - start_time
    
    # Test undo performance
    start_time = time.time()
    for _ in range(100):
        manager.undo()
    
    undo_time = time.time() - start_time
    
    print(f"   📊 Created 500 states in {creation_time:.3f}s")
    print(f"   📊 100 undos in {undo_time:.3f}s")
    
    assert creation_time < 1.0, "State creation should be fast"
    assert undo_time < 0.1, "Undo operations should be fast"
    
    print("✅ Performance: PASSED")

def run_all_tests():
    """Run all undo/redo tests"""
    print("🚀 XconomyChooser v2.01 - Undo/Redo Test Suite")
    print("=" * 60)
    
    tests = [
        test_basic_undo_redo,
        test_mass_undo,
        test_intelligent_undo,
        test_undo_to_description,
        test_undo_preview,
        test_edge_cases,
        test_performance
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__}: FAILED - {str(e)}")
            failed += 1
    
    print("=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! Undo/Redo system is working correctly.")
    else:
        print("⚠️ Some tests failed. Please review the implementation.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
