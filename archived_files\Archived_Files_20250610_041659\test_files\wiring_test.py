#!/usr/bin/env python3
"""
End-to-End Wiring Test for SCUM Economy GUI Enhanced
Tests all button connections and functionality
"""

import sys
import os
import json
import traceback

def test_imports():
    """Test all required imports"""
    print("🔍 Testing imports...")
    
    try:
        import customtkinter as ctk
        print("✅ CustomTkinter imported successfully")
    except ImportError as e:
        print(f"❌ CustomTkinter import failed: {e}")
        return False
    
    try:
        from scum_economy_gui_enhanced import SCUMEconomyG<PERSON>, UndoRedoManager, FISHLogicEngine
        print("✅ Main GUI classes imported successfully")
    except ImportError as e:
        print(f"❌ Main GUI import failed: {e}")
        return False
    
    try:
        from enhanced_dialogs import EnhancedGlobalPriceDialog
        print("✅ Enhanced dialogs imported successfully")
    except ImportError as e:
        print(f"⚠️ Enhanced dialogs import failed (fallback available): {e}")
    
    return True

def test_class_instantiation():
    """Test class instantiation"""
    print("\n🔍 Testing class instantiation...")
    
    try:
        from scum_economy_gui_enhanced import UndoRedoManager, FISHLogicEngine
        
        # Test UndoRedoManager
        undo_manager = UndoRedoManager()
        print("✅ UndoRedoManager instantiated successfully")
        
        # Test FISHLogicEngine
        fish_engine = FISHLogicEngine()
        print("✅ FISHLogicEngine instantiated successfully")
        
        return True
    except Exception as e:
        print(f"❌ Class instantiation failed: {e}")
        traceback.print_exc()
        return False

def test_fish_logic():
    """Test F.I.S.H. Logic functionality"""
    print("\n🔍 Testing F.I.S.H. Logic...")
    
    try:
        from scum_economy_gui_enhanced import FISHLogicEngine
        
        fish = FISHLogicEngine()
        
        # Test categorization
        test_items = [
            "Weapon_AK74",
            "Cal_556x45_AP_CR", 
            "Fish_Carp",
            "Crafted_Improvised_Backpack",
            "Police_Helmet",
            "Military_Vest"
        ]
        
        for item in test_items:
            result = fish.categorize_item(item)
            print(f"  📦 {item} → {result['category']} ({result['priority']})")
        
        print("✅ F.I.S.H. Logic categorization working")
        return True
        
    except Exception as e:
        print(f"❌ F.I.S.H. Logic test failed: {e}")
        traceback.print_exc()
        return False

def test_undo_redo():
    """Test undo/redo functionality"""
    print("\n🔍 Testing Undo/Redo system...")
    
    try:
        from scum_economy_gui_enhanced import UndoRedoManager
        
        manager = UndoRedoManager()
        
        # Test initial state
        assert not manager.can_undo(), "Should not be able to undo initially"
        assert not manager.can_redo(), "Should not be able to redo initially"
        
        # Save some states
        test_data1 = {"test": "data1"}
        test_data2 = {"test": "data2"}
        
        manager.save_state(test_data1, "First state")
        manager.save_state(test_data2, "Second state")
        
        assert manager.can_undo(), "Should be able to undo after saving states"
        assert not manager.can_redo(), "Should not be able to redo when at latest state"
        
        # Test undo
        undone_data = manager.undo()
        assert undone_data == test_data1, "Undo should return first state"
        assert manager.can_redo(), "Should be able to redo after undo"
        
        # Test redo
        redone_data = manager.redo()
        assert redone_data == test_data2, "Redo should return second state"
        
        print("✅ Undo/Redo system working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Undo/Redo test failed: {e}")
        traceback.print_exc()
        return False

def test_gui_methods():
    """Test GUI method existence"""
    print("\n🔍 Testing GUI method wiring...")
    
    try:
        from scum_economy_gui_enhanced import SCUMEconomyGUI
        
        # Check if all required methods exist
        required_methods = [
            'open_global_price_changes',
            'open_economy_fields', 
            'open_merchant_level',
            'open_outpost_level',
            'open_fine_tune',
            'open_spread_edit',
            'open_purchase_settings',
            'open_fish_analysis',
            'open_smart_categories',
            'open_scenario_rules',
            'undo_operation',
            'redo_operation',
            'show_history',
            'apply_all_changes',
            'clear_all_changes',
            'add_pending_change',
            'update_undo_redo_buttons',
            'save_state',
            'run_fish_analysis'
        ]
        
        # Create a dummy GUI instance (without actually showing it)
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # We can't easily test the full GUI without showing it, so just check method existence
        for method_name in required_methods:
            if hasattr(SCUMEconomyGUI, method_name):
                print(f"  ✅ {method_name}")
            else:
                print(f"  ❌ {method_name} - MISSING")
                return False
        
        root.destroy()
        print("✅ All GUI methods exist")
        return True
        
    except Exception as e:
        print(f"❌ GUI method test failed: {e}")
        traceback.print_exc()
        return False

def test_sample_data():
    """Test with sample economy data"""
    print("\n🔍 Testing with sample data...")
    
    try:
        # Create sample economy data
        sample_data = {
            "economy-override": {
                "traders": {
                    "A_0_Trader_01": [
                        {
                            "tradeable-code": "Weapon_AK74",
                            "base-purchase-price": "2500",
                            "base-sell-price": "1250",
                            "required-famepoints": "100",
                            "can-be-purchased": "true"
                        },
                        {
                            "tradeable-code": "Cal_556x45_AP_CR",
                            "base-purchase-price": "15",
                            "base-sell-price": "7",
                            "required-famepoints": "0",
                            "can-be-purchased": "true"
                        }
                    ]
                }
            }
        }
        
        # Test F.I.S.H. analysis with sample data
        from scum_economy_gui_enhanced import FISHLogicEngine
        fish = FISHLogicEngine()
        analysis = fish.analyze_economy(sample_data)
        
        print(f"  📊 Total items: {analysis['total_items']}")
        print(f"  📂 Categories: {list(analysis['categories'].keys())}")
        print(f"  🎯 Priorities: {dict(analysis['priority_distribution'])}")
        
        assert analysis['total_items'] > 0, "Should find items in sample data"
        assert len(analysis['categories']) > 0, "Should categorize items"
        
        print("✅ Sample data processing working")
        return True
        
    except Exception as e:
        print(f"❌ Sample data test failed: {e}")
        traceback.print_exc()
        return False

def test_file_operations():
    """Test file operation methods"""
    print("\n🔍 Testing file operations...")
    
    try:
        # Test if test_economy.json exists
        if os.path.exists("test_economy.json"):
            with open("test_economy.json", 'r') as f:
                data = json.load(f)
            print("✅ test_economy.json loaded successfully")
            
            # Test F.I.S.H. analysis on real file
            from scum_economy_gui_enhanced import FISHLogicEngine
            fish = FISHLogicEngine()
            analysis = fish.analyze_economy(data)
            print(f"  📊 Real file analysis: {analysis['total_items']} items, {len(analysis['categories'])} categories")
            
        else:
            print("⚠️ test_economy.json not found (optional)")
        
        return True
        
    except Exception as e:
        print(f"❌ File operations test failed: {e}")
        traceback.print_exc()
        return False

def run_all_tests():
    """Run all wiring tests"""
    print("🚀 Starting End-to-End Wiring Test\n")
    
    tests = [
        ("Imports", test_imports),
        ("Class Instantiation", test_class_instantiation),
        ("F.I.S.H. Logic", test_fish_logic),
        ("Undo/Redo System", test_undo_redo),
        ("GUI Methods", test_gui_methods),
        ("Sample Data", test_sample_data),
        ("File Operations", test_file_operations)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 Running {test_name} Test")
        print('='*50)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} Test PASSED")
            else:
                print(f"❌ {test_name} Test FAILED")
        except Exception as e:
            print(f"❌ {test_name} Test CRASHED: {e}")
            traceback.print_exc()
    
    print(f"\n{'='*50}")
    print(f"🏁 FINAL RESULTS")
    print('='*50)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED - WIRING IS COMPLETE!")
    else:
        print("⚠️ SOME TESTS FAILED - CHECK WIRING ISSUES")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
