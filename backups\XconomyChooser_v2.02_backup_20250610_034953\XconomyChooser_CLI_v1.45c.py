import json
import sys
import math
import msvcrt
import subprocess
import os
import time
import glob
import logging
import datetime
import colorama
from datetime import datetime
from colorama import Fore, Style, init
init(autoreset=True)

# Initialize colorama for colored text
colorama.init()

# Global variable to keep track of valid or invalid JSON files
invalid_json_files = []
valid_json_files = []

json_filename = None

edited_fields = {}

# Initialize the primary logging
def initialize_logging(base_filename):
    # Create a log directory if it doesn't exist
    log_dir = "logs"
    os.makedirs(log_dir, exist_ok=True)

    # Get the timestamp for the log filename
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Configure the global logger
    global global_log_filename  # Define global_log_filename as a global variable
    global_log_filename = os.path.join(log_dir, f"{base_filename}_{timestamp}.log")


    # Create the global log file if it doesn't exist
    if not os.path.exists(global_log_filename):
        with open(global_log_filename, "w") as global_log_file:
            pass

    logging.basicConfig(
        filename=global_log_filename,
        filemode="a",
        format="%(asctime)s - %(levelname)s - %(message)s",
        level=logging.DEBUG,
    )


# Function to log function calls and their parameters
def log_function_call(function_name, parameters):
    with open("function_calls_log.txt", "a") as log_file:
        log_file.write("***\n")
        log_file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')} -\n")
        log_file.write(f"Function: {function_name}\n")
        log_file.write(f"Parameters: {parameters}\n")
        log_file.write("***\n")



# Define the script version
SCRIPT_VERSION = "Build v1_45b"
SCRIPT_AUTHOR = "V1nceTD"
SCRIPT_DATE = "08/10/2023"

# ANSI escape code for coloured text
GREEN = "\033[92m"
WHITE = "\033[97m"
MAGENTA = "\033[35m"
YELLOW = "\033[93m"
ORANGE = "\033[93m"
CYAN = "\033[36m"
RED = "\033[91m"
YELLOW_BOLD = "\033[1;33m"
BLUE = "\033[34m"
GRAY = "\033[90m"
REVERSE = "\033[7m"
BLINK = "\033[5m"
RESET_COLOR = "\033[0m"


# Function to clear the console screen
def clear_screen():
    os.system("cls" if os.name == "nt" else "clear")


# Function to load JSON data from a file
def load_json(filename, log_filename):
    try:
        with open(filename, "r") as json_file:
            json_content = json_file.read()
            try:
                json_data = json.loads(json_content)
                return json_data
            except json.JSONDecodeError as e:
                # Invalid JSON format
                print(f"Error: Invalid JSON format in '{filename}' at line {e.lineno}, column {e.colno}")
                print(f"Details: {e.msg}")
                logging.error(f"Invalid JSON format in '{filename}' at line {e.lineno}, column {e.colno}")
                logging.error(f"Details: {e.msg}")

                # Log error details to a separate log file
                with open(log_filename, "a") as log_file:
                    log_file.write(f"Invalid JSON format in '{filename}' at line {e.lineno}, column {e.colno}\n")
                    log_file.write(f"Details: {e.msg}\n")
                    log_file.write(f"Bad Line: {json_content.splitlines()[e.lineno - 1]}\n")  # Include the bad line

                # Present log file to the user
                print("\n[Error] JSON file contains formatting errors. Check the log file for details.")
                print(f"Log file: {log_filename}\n")
                while True:
                    log_option = input(f"View log file? (Y/N): ").strip().lower()
                    if log_option == "y":
                        with open(log_filename, "r") as log_file:
                            log_contents = log_file.read()
                            print("\n[Log File Content]")
                            print(log_contents)
                            print("\n[End of Log File Content]")
                            log_file.close()
                        print("\n[Log file displayed]")
                        input("Press Enter to close the log file.")
                        break
                    elif log_option == "n":
                        break
                    else:
                        print(f"{RED}Invalid input.{WHITE} Enter 'Y' to view the log file or 'N' to continue.{RESET_COLOR}")
                return None
    except FileNotFoundError:
        print(f"'{filename}' not found. Exiting.")
        logging.error(f"File not found: '{filename}'")
        time.sleep(2)
        sys.exit(1)


# Function to update merchant base-purchase-price values based on a percentage
def update_bpp_values(obj, percentage_to_add, outpost=None):
    if isinstance(obj, list):
        for item in obj:
            update_bpp_values(item, percentage_to_add, outpost)
    elif isinstance(obj, dict):
        if (
            obj.get("tradeable-code")
            and obj.get("base-purchase-price")
            and obj["base-purchase-price"] not in ["-1", "null"]
        ):
            try:
                current_value = float(obj["base-purchase-price"])
                new_value = current_value * (1 + percentage_to_add / 100)
                # Round the new value to the nearest whole number
                new_value = round(new_value)
                obj["base-purchase-price"] = str(new_value)
                if outpost:
                    edit_msg = f"{outpost} - Batch % updated '{obj['tradeable-code']}' to {new_value}. Old Value: {current_value}, New Value: {new_value}"
                    logging.info(edit_msg)
            except ValueError:
                pass
        for key, value in obj.items():
            if isinstance(value, (dict, list)):
                update_bpp_values(value, percentage_to_add, outpost)

# Function to update merchant base-sell-price values based on a percentage
def update_bsp_values(trader_data, percentage_to_add, outpost=None):
    for item in trader_data:
        if (
            item.get("base-sell-price")
            and item["base-sell-price"] not in ["-1", "null"]
        ):
            try:
                current_value = float(item["base-sell-price"])
                new_value = current_value * (1 + percentage_to_add / 100)
                new_value = round(new_value)
                item["base-sell-price"] = str(new_value)
                if outpost:
                    edit_msg = f"{outpost} - Batch % updated '{item['tradeable-code']}' to {new_value}. Old Value: {current_value}, New Value: {new_value}"
                    logging.info(edit_msg)
            except ValueError:
                pass
        for key, value in item.items():
            if isinstance(value, (dict, list)):
                update_bsp_values(value, percentage_to_add, outpost)


# Function to Edit OutPost Traders Menu
def edit_outpost_traders(data):
    logging.info(f"Function 'edit_outpost_traders' called")
    
    while True:
        display_main_menu_header()
        print(f"\n{GREEN}Edit Specific Merchants by -+ percent{RESET_COLOR}")
        print(f"=" * 40)
        print(f"{WHITE}1. Adjust {YELLOW}'base-purchase-price'{RESET_COLOR} {WHITE}by % {GREEN}-+{WHITE} for a specific merchant{RESET_COLOR}")
        print(f"{WHITE}2. Adjust {YELLOW}'base-sell-price'{RESET_COLOR} {WHITE}by % {GREEN}-+{WHITE} for a specific merchant{RESET_COLOR}")
        choice = input("\nChoose an option: ").strip().lower()

        if choice == "1":
            edit_bpp_traders(data)
        elif choice == "2":
            edit_bsp_traders(data)
        elif choice == "3" or choice == "":
            break
        else:
            print("Invalid choice. Please select a valid option.")


# Function to edit base-purchase-price outpost traders
def edit_bpp_traders(data):
    logging.info(f"Function 'edit_bpp_traders' called")
    outpost_choices = {"1": "A_0", "2": "B_4", "3": "C_2", "4": "Z_3"}

    chosen_traders = []  # Define chosen_traders in the outer scope

    while True:
        outpost_index = 0
        while outpost_index < len(outpost_choices):
            outpost_key = str(outpost_index + 1)
            outpost_value = outpost_choices[outpost_key]

            while True:
                # Ask for the Outpost selection
                print(f"{GREEN}Select the Outpost ({outpost_value}):{RESET_COLOR}")
                print(
                    f"{GREEN}Progress: {outpost_key} / {len(outpost_choices)}{RESET_COLOR}"
                )
                for key, value in outpost_choices.items():
                    print(f"{WHITE}{key}. {value}{RESET_COLOR}")

                outpost_choice = (
                    input(
                        f"\n{WHITE}Select an outpost by entering the number or enter to skip:{RESET_COLOR} "
                    )
                    .strip()
                    .lower()
                )

                if outpost_choice in (""):
                    outpost_index = len(outpost_choices)
                    break

                if outpost_choice in outpost_choices:
                    outpost = outpost_choices[outpost_choice]

                    # Function to display available traders
                    def display_available_traders():
                        available_traders = [
                            key
                            for key in data["economy-override"]["traders"]
                            if key.startswith(outpost) and key not in chosen_traders
                        ]
                        if not available_traders:
                            print("No more available merchants for the chosen Outpost.")
                            return None
                        print(
                            f"\n{GREEN}Available merchants in the chosen Outpost:{RESET_COLOR}"
                        )
                        for i, trader in enumerate(available_traders):
                            print(f"{i + 1}. {trader}")
                        return available_traders

                    available_traders = display_available_traders()
                    if available_traders is None:
                        break

                    while True:
                        trader_choice = (
                            input(
                                "\nChoose a Merchant by entering the number or enter to go back: "
                            )
                            .strip()
                            .lower()
                        )

                        if trader_choice in ("s", ""):
                            # Move on to the next outpost
                            break

                        if trader_choice.isdigit():
                            index = int(trader_choice) - 1
                            if 0 <= index < len(available_traders):
                                chosen_trader = available_traders[index]

                                while True:
                                    percentage_to_add = input(
                                        f"\nEdit {GREEN}'{chosen_trader}'{RESET_COLOR} - Enter the percentage to be adjusted by -+ (-99 to 100, Enter to skip): "
                                    ).strip()
                                    if percentage_to_add in ("s", ""):
                                        break
                                    if percentage_to_add.isdigit() or (
                                        percentage_to_add.startswith("-")
                                        and percentage_to_add[1:].isdigit()
                                    ):
                                        percentage_to_add = float(percentage_to_add)
                                        if -99 <= percentage_to_add <= 100:
                                            update_bpp_values(
                                                data["economy-override"]["traders"][
                                                    chosen_trader
                                                ],
                                                percentage_to_add,
                                                outpost,
                                            )
                                            print(f"Adjustment of {GREEN}'{chosen_trader}'{RESET_COLOR} at Outpost {outpost} by {GREEN}{percentage_to_add}%{RESET_COLOR} completed.")
        
                                            chosen_traders.append(chosen_trader)
                                            available_traders = (
                                                display_available_traders()
                                            )
                                            if available_traders is None:
                                                break
                                            while True:
                                                another_edit = (
                                                    input(
                                                        f"Do you want to make another edit for {GREEN}{chosen_trader}{RESET_COLOR} at this Outpost? Yes or enter to go back.: "
                                                    )
                                                    .strip()
                                                    .lower()
                                                )
                                                if another_edit in ("yes", ""):
                                                    break
                                                else:
                                                    print(
                                                        f"{RED}Invalid input.{RESET_COLOR} Please enter {ORANGE}'Yes'{RESET_COLOR} or enter to go back{RESET_COLOR}."
                                                    )
                                            if another_edit == "":
                                                break
                                        else:
                                            print(
                                                f"{RED}Invalid input.{RESET_COLOR} Please enter a percentage between {RED}-99{RESET_COLOR} and {GREEN}100.{RESET_COLOR}"
                                            )
                                    else:
                                        print(
                                            f"{RED}Invalid input.{RESET_COLOR} Please enter a valid number."
                                        )

        # Automatically go back to the main menu when finished with this outpost
        if outpost_index == len(outpost_choices):
            break


# Function to edit base-sell-price outpost merchants
def edit_bsp_traders(data):
    logging.info(f"Function 'edit_bsp_traders' called")
    outpost_choices = {"1": "A_0", "2": "B_4", "3": "C_2", "4": "Z_3"}

    chosen_traders = []

    while True:
        outpost_index = 0
        while outpost_index < len(outpost_choices):
            outpost_key = str(outpost_index + 1)
            outpost_value = outpost_choices[outpost_key]

            while True:
                print(f"{GREEN}Select the Outpost ({outpost_value}):{RESET_COLOR}")
                print(
                    f"{GREEN}Progress: {outpost_key} / {len(outpost_choices)}{RESET_COLOR}"
                )
                for key, value in outpost_choices.items():
                    print(f"{WHITE}{key}. {value}{RESET_COLOR}")

                outpost_choice = (
                    input(
                        f"\n{WHITE}Select an outpost by entering the number or 'S' to skip:{RESET_COLOR} "
                    )
                    .strip()
                    .lower()
                )

                if outpost_choice in ("s", ""):
                    outpost_index = len(outpost_choices)
                    break

                if outpost_choice in outpost_choices:
                    outpost = outpost_choices[outpost_choice]

                    # Function to display available merchants
                    def display_available_traders():
                        available_traders = [
                            key
                            for key in data["economy-override"]["traders"]
                            if key.startswith(outpost) and key not in chosen_traders
                        ]
                        if not available_traders:
                            print("No more available merchants for the chosen Outpost.")
                            return None
                        print(
                            f"\n{GREEN}Available merchants in the chosen Outpost:{RESET_COLOR}"
                        )
                        for i, trader in enumerate(available_traders):
                            print(f"{i + 1}. {trader}")
                        return available_traders

                    available_traders = display_available_traders()
                    if available_traders is None:
                        break

                    while True:
                        trader_choice = (
                            input(
                                "\nChoose a Merchant by entering the number or 'S' to skip and save: "
                            )
                            .strip()
                            .lower()
                        )

                        if trader_choice in ("s", ""):
                            # Move on to the next outpost
                            break

                        if trader_choice.isdigit():
                            index = int(trader_choice) - 1
                            if 0 <= index < len(available_traders):
                                chosen_trader = available_traders[index]

                                while True:
                                    percentage_to_add = input(
                                        f"\nEdit {GREEN}'{chosen_trader}'{RESET_COLOR} - Enter the percentage to be adjusted by -+ (-99 to 100, Enter to skip): "
                                    ).strip()
                                    if percentage_to_add in ("s", ""):
                                        # Move on to the next outpost
                                        break
                                    if percentage_to_add.isdigit() or (
                                        percentage_to_add.startswith("-")
                                        and percentage_to_add[1:].isdigit()
                                    ):
                                        percentage_to_add = float(percentage_to_add)
                                        if -99 <= percentage_to_add <= 100:
                                            # Update merchant base-sell-price values based on percentage
                                            update_bsp_values(
                                                data["economy-override"]["traders"][
                                                    chosen_trader
                                                ],
                                                percentage_to_add,
                                                outpost,
                                            )
                                            print(f"Adjustment of {GREEN}'{chosen_trader}'{RESET_COLOR} at Outpost {outpost} by {GREEN}{percentage_to_add}%{RESET_COLOR} completed.")
                                            chosen_traders.append(chosen_trader)
                                            available_traders = (
                                                display_available_traders()
                                            )
                                            if available_traders is None:
                                                break
                                            while True:
                                                another_edit = (
                                                    input(
                                                        f"Do you want to make another edit for {GREEN}{chosen_trader}{RESET_COLOR} at this Outpost? Y or enter to go back: "
                                                    )
                                                    .strip()
                                                    .lower()
                                                )
                                                if another_edit in ("y", ""):
                                                    break
                                                else:
                                                    print(
                                                        f"{RED}Invalid input.{RESET_COLOR} Please enter {ORANGE}'Y'{RESET_COLOR} or {GREEN}'N'{RESET_COLOR}."
                                                    )
                                            if another_edit == "":
                                                break  # Move on to the next outpost
                                        else:
                                            print(
                                                f"{RED}Invalid input.{RESET_COLOR} Please enter a percentage between {RED}-99{RESET_COLOR} and {GREEN}100.{RESET_COLOR}"
                                            )
                                    else:
                                        print(
                                            f"{RED}Invalid input.{RESET_COLOR} Please enter a valid number."
                                        )

        # Automatically go back to the main menu when finished with this outpost
        if outpost_index == len(outpost_choices):
            break

# Function to display the startup screen
def display_startup_screen():
    barbed_wire = """
        ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡  
   
    ---//---//---//---//---//---//---//---//---//---//---//---//---//---//---//---//---//---//---//---//---//---//---

     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡     ⚡
    """
    clear_screen()
    print(
        ORANGE
        + "                                                 SCUM Economy Chooser"
        + RESET_COLOR
    )
    print(YELLOW + barbed_wire + RESET_COLOR)
    # Display Author, Date, and Version in the bottom right corner
    print(
        f"\n{GRAY}Author: {SCRIPT_AUTHOR} {SCRIPT_DATE}{' ' * (38 - len(SCRIPT_AUTHOR) - len(SCRIPT_DATE) - len(SCRIPT_VERSION))}Version: {SCRIPT_VERSION}{RESET_COLOR}"
    )
    time.sleep(1)


# Function to display the main menu header
def display_main_menu_header():
    clear_screen()
    print("=" * 40)
    print(f"{GREEN}=== Welcome to SCUM Economy Chooser ==={RESET_COLOR}")
    print("=" * 40)
    print(f"{GRAY}Author: {SCRIPT_AUTHOR} {SCRIPT_DATE} {SCRIPT_VERSION}{RESET_COLOR}")
    print(
        f"{GRAY}Disclaimer: You are responsible for the data you upload to your server config.{RESET_COLOR}"
    )
    print("Files should be checked before applying to your server.")
if json_filename:
    print("\n" + " " * 37 + f"{YELLOW_BOLD}{json_filename}{RESET_COLOR}")
else:
    print("\n" + " " * 37 + f"{GRAY}No File Loaded{RESET_COLOR}")




# Function to display the main menu
def display_main_menu(data, edited_fields):
    readme_text = load_readme()
    while True:
        display_main_menu_header()
        print(f"\n{GREEN}Main Menu:{RESET_COLOR}")
        print("=" * 40)
        print(f"{WHITE}1. Global Price Changes")
        print(f"{WHITE}2. Edit Economy Fields")
        print(f"{WHITE}3. Edit Merchant level")
        print(f"{WHITE}4. Edit Outpost level")  
        print(f"{WHITE}5. Fine Tune Merchant Items")
        print(f"{WHITE}6. Spread Edit Item across traders")
        print(f"{WHITE}7. Spread can-be-purchased")
        print(f"{YELLOW}11. Categories - Exprimental")
        print(f"{WHITE}8. Save and Exit")
        print(f"{WHITE}9. Exit without Saving")
        print(f"{WHITE}10. Start Again{RESET_COLOR}")
        print(f"{WHITE}0. Display Read Me")
        choice = input("Choose an option: ").strip().lower()

        if choice == "1":
            global_price_changes(data)
        elif choice == "2":
            edit_economy_fields(data)
        elif choice == "3":
            edit_outpost_traders(data)
        elif choice == "4":
            edit_trader_prices(data) 
        elif choice == "5":
            fine_tune_menu(data)
        elif choice == "6":
            spread_edit(data)
        elif choice == "7":
            spread_edit_can_be_purchased(data)
        elif choice == "11":
            category_price_changes(data)
        elif choice == "8":
            save_and_exit(data)
        elif choice == '0':
            display_readme(readme_text)
        elif choice == "9":
            exit_without_saving(data)
        elif choice == "10":
            if start_again(data):
                break
        else:
            print("Invalid choice. Please enter a valid option number.")

def get_field_to_edit():
    choice = None
    while choice is None:
        display_main_menu_header()
        print(f"\n{GREEN}Choose the field to edit:{RESET_COLOR}")
        print("=" * 40)
        print(f"{WHITE}1. Edit {YELLOW}'base-purchase-price' {WHITE}by % {GREEN}-+{WHITE} for all merchants at selected Outpost{RESET_COLOR}")
        print(f"{WHITE}2. Edit {YELLOW}'base-sell-price' {WHITE}by % {GREEN}-+{WHITE} for all merchants at selected Outpost{RESET_COLOR}")
        user_input = input("Enter the number (or press Enter to go back): ").strip()

        if user_input == "1":
            choice = "base-purchase-price"
        elif user_input == "2":
            choice = "base-sell-price"
        elif user_input == "" or user_input.lower() == "s":
            return None
        else:
            print("Invalid field choice. Please enter '1', '2', or just press Enter to go back.")

    return choice

# Function to edit merchant prices at an outpost
def edit_trader_prices(data):
    logging.info(f"Function 'edit_trader_prices' called")
    outpost_choices = {"1": "A_0", "2": "B_4", "3": "C_2", "4": "Z_3"}
    field_to_edit = get_field_to_edit()

    if field_to_edit is None:
        return

    while True:
        print(f"{GREEN}Edit Merchant Prices at a chosen Outpost:{RESET_COLOR}")
        print("=" * 40)
        display_outpost_choices(outpost_choices)
        outpost_choice = input(f"{WHITE}Select an Outpost or press Enter to go back:{RESET_COLOR} ").strip()

        if outpost_choice == "":
            return
        elif outpost_choice in outpost_choices:
            outpost = outpost_choices[outpost_choice]
            percentage = get_percentage_change()

            if percentage is None:
                return

            if percentage > 0:
                percentage_sign = "+"
                percentage_color = GREEN
            elif percentage < 0:
                percentage_sign = "-"
                percentage_color = RED
            else:
                percentage_sign = ""
                percentage_color = RESET_COLOR  
            print(f"{CYAN}Percentage change:{RESET_COLOR} {percentage}%")

            apply_percentage_change(data, outpost, percentage, field_to_edit)
            print(
                f"{GREEN}All Merchants {RESET_COLOR}({percentage_color}{percentage_sign}{percentage}%{RESET_COLOR}{WHITE}) - {YELLOW}'{field_to_edit}'{RESET_COLOR} edited.{RESET_COLOR}"
            )


def apply_percentage_change(data, outpost, percentage, field_to_edit):
    traders = data["economy-override"]["traders"]

    # Determine the colour for displaying the percentage
    if percentage > 0:
        percentage_sign = "+"
        percentage_color = GREEN
    elif percentage < 0:
        percentage_sign = "-"
        percentage_color = RED
    else:
        percentage_sign = ""
        percentage_color = RESET_COLOR
    new_percentage = abs(percentage)

    for trader_key, trader_items in traders.items():
        if trader_key.startswith(outpost):
            for item in trader_items:
                if field_to_edit in item:
                    current_value = item[field_to_edit]
                    if current_value not in [None, "null", "-1"]:
                        try:
                            current_value = float(current_value)
                            new_value = current_value * (1 + percentage / 100)
                            new_value = round(new_value)  # Round to the nearest integer
                            item[field_to_edit] = str(new_value)
                            logging.info(f"Updated {field_to_edit} for '{item['tradeable-code']}' to {new_value}")
                        except ValueError:
                            print(f"Error: Unable to convert '{current_value}' to float.")
                    else:
                        print(f"Debug: {CYAN}'{item['tradeable-code']}'{WHITE} - Field {YELLOW}'{field_to_edit}' {WHITE}is already '-1', 'null', or None. Skipping.")

    # Moved the formatting code outside the trader_items loop
    print(
        f"{GREEN}All Merchants at {outpost} {RESET_COLOR}({percentage_color}{percentage_sign}{new_percentage}%{RESET_COLOR}{WHITE}) - {YELLOW}'{field_to_edit}'{RESET_COLOR} edited.{RESET_COLOR}"
    )

# Function to display outpost choicoes
def display_outpost_choices(outpost_choices):
    print(f"\n{GREEN}Select an Outpost:{RESET_COLOR}")
    for i, outpost in outpost_choices.items():
        print(f"{i}. {outpost}")

def get_percentage_change():
    while True:
        percentage_input = input("Enter the percentage change -99 to 100 or press Enter to go back: ").strip()

        if percentage_input == "":
            return None  # Return None to indicate going back
        if percentage_input.lstrip('-').isdigit():
            percentage = int(percentage_input)
            if -99 <= percentage <= 100:
                return percentage
            print(f"{RED}Invalid input.{WHITE} Please enter a percentage between -99 and 100.")
        else:
            print(f"{RED}Invalid input.{WHITE} Please enter a valid number or press Enter to go back.")

# Function to display the readme
def display_readme(readme_text):
    logging.info(f"Function 'display_readme' called")
    # Split the text into pages based on double newline
    pages = readme_text.split("\n\n")
    page_number = 0
    max_lines = 25  #fill the screen
    instructions = f"\n\n\nPress {WHITE}'Backspace'{RESET_COLOR} to go to the previous page.   Press {WHITE}'Enter'{RESET_COLOR} to go to the next page.   Press {WHITE}'Escape'{RESET_COLOR} to quit."

    while page_number < len(pages):
        os.system('cls' if os.name == 'nt' else 'clear')

        # Calculate the number of lines used by the text
        used_lines = len(pages[page_number].split('\n'))
        remaining_lines = max_lines - used_lines

        # Print the page
        print(pages[page_number])

        # Display instructions at the bottom of the screen
        print(instructions)

        # Get a keypress for Windows (ANSI keys)
        user_input = msvcrt.getch()

        # Convert ANSI keys to human-readable values
        user_input = user_input.decode('utf-8')

        if user_input == '\r' and page_number < len(pages) - 1:  
            page_number += 1
        elif user_input == '\x08' and page_number > 0:  
            page_number -= 1
        elif user_input == '\x1b':  
            break

# Call display_readme to load the README content
def load_readme():
    readme_text = f"""
====================================
  {GREEN}SCUM Economy Chooser - User Guide{RESET_COLOR}
====================================
Welcome to SCUM Economy Chooser application.
Version 1.x
The SCUM Economy Chooser is a lightweight powerful JSON editor, you are able to manipulate your {YELLOW}'economyoverride.json'{RESET_COLOR}
in multiple ways, globally, locally or in fine detail at item level.
{CYAN}'tradeable-code'{RESET_COLOR} is locked so it is not possible to ruin your items codes by accident.
All percentages ranges bottom out at -99 so it is not possible to wipe out your prices to 0. If you want to wipe a value for an item, 
this should be done within fine detail and saved across merchants.
values for {YELLOW}'base-purchase-price'{RESET_COLOR} and {YELLOW}'base-sell-price'{RESET_COLOR} already set as {GREEN}-1 (default){RESET_COLOR} or {RED}'null'{RESET_COLOR} or {CYAN}'none'{RESET_COLOR} will not 
be affected.


1. {GREEN}Global Price Changes:{RESET_COLOR}
------------------------
Global Price Changes
Allows you to adjust outpost values (prices) globally for all outposts/ merchants by specifying a percentage increase or decrease.
This function simplifies the process of managing values (prices) across your whole economy file.
First choose your type of edit, {YELLOW}'base-purchase-price'{RESET_COLOR} or {YELLOW}'base-sell-price'{RESET_COLOR}.
Choose within the range of {GREEN}-99{RESET_COLOR} to {GREEN}100%{RESET_COLOR} across all Outposts and traders.
Editing {YELLOW}'required-famepoints'{RESET_COLOR} option right now is for simply putting the whole file back to default values {GREEN}(-1){RESET_COLOR}.
Edit {YELLOW}'can-be-purchased'{RESET_COLOR} sets items to the chosen value. {CYAN}'true' default'{RESET_COLOR} or {CYAN}'false'{RESET_COLOR}.
-1. Globally edit {YELLOW}'base-purchase-price'{RESET_COLOR}
-2. Globally edit {YELLOW}'base-sell-price'{RESET_COLOR}
-3. Globally edit {YELLOW}'required-famepoints'{RESET_COLOR}
-4. Globally edit {YELLOW}'can-be-purchased'{RESET_COLOR}

2.{GREEN} Edit Economy Fields:{RESET_COLOR}
------------------------
Edit Economy Fields
You can modify the following fields:
    {Fore.YELLOW}- "economy-reset-time-hours"{Style.RESET_ALL}
    - "prices-randomization-time-hours"
    - "tradeable-rotation-time-ingame-hours-min"
    - "tradeable-rotation-time-ingame-hours-max"
    - "tradeable-rotation-time-of-day-min"
    - "tradeable-rotation-time-of-day-max"
    - "fully-restock-tradeable-hours"
    - "trader-funds-change-rate-per-hour-multiplier"
    - "prices-subject-to-player-count"
    - "gold-price-subject-to-global-multiplier"
    - "economy-logging"
    - "traders-unlimited-funds"
    - "traders-unlimited-stock"
    - "only-after-player-sale-tradeable-availability-enabled"
    - "tradeable-rotation-enabled"
    - "enable-fame-point-requirement"
You can use Up and Down arrows to navigate properties, Enter to edit, escape to return to the previous menu.
Follow the on-screen instructions to select the field you wish to modify and specify new values.
**** {BLINK}{YELLOW}Take care editing these values; incorrect values could have undesired effects{RESET_COLOR} ****

3.{GREEN}Edit Merchant level{RESET_COLOR}
------------------------
Edit Specific Merchants by -+ percent
First choose type of edit, {YELLOW}'base-purchse-price'{RESET_COLOR} or {YELLOW}'base-sell-price'{RESET_COLOR} then Outpost, then merchant to edit.
Adjust the value by a % within a range of {GREEN}-99{RESET_COLOR} to {GREEN}100%{RESET_COLOR}. 
This will apply to the select Merchant.
-1. Adjust {YELLOW}'base-purchase-price'{RESET_COLOR} by % -+ for a specific merchant
-2. Adjust {YELLOW}'base-sell-price'{RESET_COLOR} by % -+ for a specific merchant


4.{GREEN} Edit Outpost level:{RESET_COLOR}
------------------------
Edit Outpost Prices
Allows you to edit Outpost traders {YELLOW}'base-purchase-price'{RESET_COLOR} and/or {YELLOW}'base-sell-price'{RESET_COLOR} prices by 
specifying a percentage increase or decrease.
This function simplifies the process of managing values (prices) across a particular location.
First choose your type of edit.
Select an outpost from the list, then merchant to amend the values within a range of -99 to 100%.
-1. Edit {YELLOW}'base-purchase-price'{RESET_COLOR} for all merchants at selected Outpost
-2. Edit {YELLOW}'base-sell-price'{RESET_COLOR} for all merchants at selected Outpost

5.{GREEN} Fine Tune:{RESET_COLOR}
------------------------
Fine Tune
Provides granular control for editing item prices only. You can search for specific items by {CYAN}'tradeable-code'{RESET_COLOR} and the application will display a list of matching items.
Fine Tune allows you to adjust prices individually for each item, providing detailed customisation options.
Edit Merchant Directly allows you to search for an item; you will be provided with all the results for that item across the 4 outposts (Function to be reviewed),
 so be sure to choose the correct one from the correct trader.
You can use Up and Down arrows to navigate properties, Enter to edit, escape to return to the previous menu.
{GREEN}"A_0_Armory": [{RESET_COLOR}
 .
  {YELLOW}"tradeable-code": {CYAN}"Frag_Grenade"{RESET_COLOR},
  "base-purchase-price": "-1",
  "base-sell-price": "-1",
  "delta-price": "-1.0",
  "can-be-purchased": "default",
  "required-famepoints": "-1",
 ,
],
Follow the on-screen instructions to select the field you wish to modify and specify new values.
-1. Fine Tune {YELLOW}'base-purchase-price'{RESET_COLOR} Merchant Items
-2. Fine Tune {YELLOW}'base-sell-price'{RESET_COLOR} Merchant Items
-3. Edit Merchant {CYAN}Item Directly{RESET_COLOR}

5.{GREEN} Spread Edit:{RESET_COLOR}
--------------
Spread Edit
Streamlines the process of editing {YELLOW}'base-purchase-price'{RESET_COLOR} or {YELLOW}'base-sell-price'{RESET_COLOR} across multiple outposts. 
You can select a merchant from the available options, make your edit style choice, then search for items within that merchant. 
The chosen {CYAN}'tradeable-code'{RESET_COLOR} value can be updated across all outposts that stock it.
This is a convenient way to ensure uniform pricing across various locations.
Choose a Merchant or press Enter to go back:
{WHITE}1. Armory{RESET_COLOR}
{WHITE}2. BoatShop{RESET_COLOR}
{WHITE}3. Hospital{RESET_COLOR}
{WHITE}4. Mechanic{RESET_COLOR}
{WHITE}5. Saloon{RESET_COLOR}
{WHITE}6. Trader{RESET_COLOR}
{WHITE}7. Barber{RESET_COLOR}
   {WHITE} 1. Edit {YELLOW}'base-purchase-price'{RESET_COLOR}
   {WHITE} 2. Edit {YELLOW}'base-sell-price'{RESET_COLOR}

6.{GREEN} Spread can-be-purchased:{RESET_COLOR}
--------------
Spread Edit {YELLOW}'can-be-purchased'{RESET_COLOR}
Streamlines the process of editing {YELLOW}'can-be-purchased'{RESET_COLOR} field for items across multiple outposts.
The selected item's {YELLOW}'can-be-purchased'{RESET_COLOR} value can be updated to {CYAN}'true' 'default'{RESET_COLOR} or {CYAN}'false'{RESET_COLOR}, then saved to all merchants if chosen.
Choose a Merchant or press Enter to go back:
{WHITE}1. Armory{RESET_COLOR}
{WHITE}2. BoatShop{RESET_COLOR}
{WHITE}3. Hospital{RESET_COLOR}
{WHITE}4. Mechanic{RESET_COLOR}
{WHITE}5. Saloon{RESET_COLOR}
{WHITE}6. Trader{RESET_COLOR}
{WHITE}7. Barber{RESET_COLOR}

7.{GREEN} Save and Exit:{RESET_COLOR}
--------------
- Saves your file as a new {GREEN}date_time-stamped_economy_config.json'{RESET_COLOR}; the file will be saved to the location of the
SCUM Economy Chooser application.
8.{GREEN} Exit without Saving:{RESET_COLOR}
--------------
- You will be asked if you are sure twice.
9.{GREEN} Start Again:{RESET_COLOR}
--------------
 - If you Scummed yourself and need a break, hit 9, it unloads the JSON in memory and restarts the application. You have to type {GREEN}"yes"{RESET_COLOR}.

====================================
      {GREEN}Running the Application{RESET_COLOR}
====================================
To run the SCUM Economy Chooser application - Double click and go!
Check the extensive logs after edits to make sure the data is correct.
{RED}{BLINK}- Check your data before uploading to your host.{RESET_COLOR}
I repeat, check your data; there is no point having a fast edit system if you do not have confidence, 
a few minutes checking could solve hours of trying to work it out.
For additional help and support, bug reports, problems with maths, functions, or issues.
If you rre reporting an issue, please gather the log created & JSON for investigation.
Even suggestions! Contact V1nceTD.
If you would like to help power this project by adding coffee or energy drinks, here is my PayPal.
.
{CYAN}Paypal.me/V1nceTD
https://paypal.me/V1nceTD?country.x=GB&locale.x=en_GB{RESET_COLOR}
.
====================================
         Thank you for using
  SCUM Economy Chooser v1.43f_vw_a
             08.10.2023
====================================

{CYAN}Notes.{RESET_COLOR}
------
The percentage range for the Edit OutPost Traders function stops at -99 to avoid 0 values; the function rounds up the result so there are no decimal values.
Outposts will drop off the list as you make changes to them in Edit Outpost Merchants, to avoid multiple edits during the session.
Logs currently show all edits, prior to saving.
Functions called logged.
JSON parsing with error logs, separated to help the user find their JSON faults.
- Next version might handle faults and potentially fix them.
Review JSON tool will be attached to the main chooser for version 2.0
{YELLOW}"required-famepoints"{RESET_COLOR} can be edited in {WHITE}"Edit Merchant {CYAN}Item Directly{WHITE}"{RESET_COLOR} for items and applied across traders.
    """

    return readme_text

# Function to handle global price changes
def global_price_changes(data):
    logging.info(f"Function 'global_price_changes' called")
    while True:
        display_main_menu_header()

        print(f"\n{GREEN}Global Price Changes - {WHITE}Adjust prices by % -+ globally{RESET_COLOR}")
        print("=" * 40)
        print(f"{WHITE}1. Globally edit {YELLOW}'base-purchase-price'{RESET_COLOR}{WHITE} at all merchants.")
        print(f"{WHITE}2. Globally edit {YELLOW}'base-sell-price'{RESET_COLOR}{WHITE} at all merchants.")
        print(f"{WHITE}3. Globally edit {YELLOW}'required-famepoints'{RESET_COLOR}{WHITE} at all merchants.")
        print(f"{WHITE}4. Globally edit {YELLOW}'can-be-purchased'{RESET_COLOR}{WHITE} at all merchants.")
        choice = input("\nChoose an option or press Enter to return to the main menu: ").strip().lower()

        if choice == "1":
            global_edit_price(data, "base-purchase-price")
        elif choice == "2":
            global_edit_price(data, "base-sell-price")
        elif choice == "3":
            global_edit_required_famepoints(data)
        elif choice == "4":
            global_edit_can_be_purchased(data)
        elif choice == "":
            return
        else:
            print(f"{RED}Invalid choice.{RESET_COLOR} Please enter a valid option number.")


# Function to global edit
def global_edit_price(data, price_field):
    logging.info(f"Function 'global_price_changes' called")
    display_main_menu_header()
    current_datetime = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_message = f"{current_datetime} - Globally edited {price_field}"
    is_positive = None
    percentage_sign = ""

    print(f"\n{GREEN}Global Edit {YELLOW}'{price_field}'{RESET_COLOR}")
    print("=" * 40)
    new_percentage = input(
        f"{WHITE}Enter the percentage change (e.g., -99 to +100) for {YELLOW}'{price_field}'{WHITE} or press Enter to skip:{RESET_COLOR} "
    ).strip()

    if not new_percentage:
        print("No changes made.")
        input("Press Enter to continue...")
        return 

    # Check for a '+' sign
    if new_percentage.startswith('+'):
        new_percentage = new_percentage[1:]

    if new_percentage.startswith('-'):
        is_positive = False
        new_percentage = new_percentage[1:]
        percentage_sign = "-"
        percentage_color = RED
    else:
        is_positive = True
        percentage_sign = "+"
        percentage_color = GREEN  

    try:
        new_percentage = int(new_percentage)
    except ValueError:
        print(f"{RED}Invalid input. {WHITE}Please enter a valid percentage (e.g., -99 to 100).")
        input("Press Enter to continue...")
        return

    if new_percentage < -99 or new_percentage > 100:
        print(f"{RED}Invalid input. {WHITE}Please enter a valid percentage (e.g., -99 to 100).{RESET_COLOR}")
        input("Press Enter to continue...")
        return

    log_message += f" by {new_percentage}%"
    log_message_details = []
    for trader_name, trader_items in data["economy-override"]["traders"].items():
        for item in trader_items:
            if price_field in item:
                if item[price_field] not in ["null", "-1"]:
                    current_price = float(item[price_field])
                    if is_positive:
                        new_price = math.ceil(current_price + (current_price * new_percentage / 100))
                    else:
                        new_price = math.ceil(current_price - (current_price * new_percentage / 100))
                    new_price = max(new_price, 1)
                    item[price_field] = str(new_price)
                    log_message_details.append(
                        f"{current_datetime}Merchant: {trader_name}, Item: {item['tradeable-code']}, '{price_field}'  Old: {current_price}, New: {new_price} ({percentage_sign}{new_percentage}%)"
                    )

    # Add the log message details
    if log_message_details:
        log_message += " - Details:"
        for detail in log_message_details:
            log_message += f"\n  {detail}"

    logging.info(log_message)

    if is_positive:
        print(f"{CYAN}{percentage_sign}{new_percentage}% increase in {YELLOW}'{price_field}'{RESET_COLOR}")
    else:
        print(f"{RED}{percentage_sign}{new_percentage}% decrease in {YELLOW}'{price_field}'{RESET_COLOR}")
    print(
        f"{GREEN}All Merchants {RESET_COLOR}({percentage_color}{percentage_sign}{new_percentage}%{RESET_COLOR}{WHITE}) - {YELLOW}'{price_field}'{RESET_COLOR} edited.{RESET_COLOR}"
    )
    input("Press Enter to continue...")
    os.system('cls' if os.name == 'nt' else 'clear')


# Function to Global edit required-famepoints
def global_edit_required_famepoints(data):
    logging.info(f"Function 'global_edit_required_famepoints' called")
    display_main_menu_header()
    current_datetime = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_message = f"{current_datetime} - Globally edited 'required-famepoints'"

    print(f"\n{GREEN}Global Edit {YELLOW}'required-famepoints'{RESET_COLOR}")
    print("=" * 40)
    new_value = input(
        f"{WHITE}Enter the new value for {YELLOW}'required-famepoints'{WHITE} ({GREEN}-1{WHITE} to reset to default) or press Enter to skip:{RESET_COLOR} "
    ).strip()

    if new_value:
        if new_value == "-1":
            log_message += " to -1"
            for trader_name, trader_items in data["economy-override"]["traders"].items():
                for item in trader_items:
                    if "required-famepoints" in item:
                        item["required-famepoints"] = "-1"
            logging.info(log_message)
            print(f"{YELLOW}'required-famepoints'{RESET_COLOR} set to {GREEN}-1{RESET_COLOR} for all items")
        else:
            print(f"{RED}Invalid input.{RESET_COLOR} Please enter {GREEN}-1{RESET_COLOR} to reset {YELLOW}'required-famepoints'{RESET_COLOR}.")
    else:
        print(f"No changes made")
    input("Press Enter to continue.")

# Function to Global edit can-be-purchased
def global_edit_can_be_purchased(data):
    logging.info(f"Function 'global_edit_can_be_purchased' called")
    display_main_menu_header()
    current_datetime = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    log_message = f"Globally edited 'can-be-purchased'"

    print(f"\n{GREEN}Global Edit {YELLOW}'can-be-purchased'{RESET_COLOR}")
    print("=" * 40)
    new_value = input(
        f"{WHITE}Enter the new value for {YELLOW}'can-be-purchased'{RESET_COLOR} {CYAN}'true'{RESET_COLOR}, {CYAN}'false'{RESET_COLOR}, or {CYAN}'default'{RESET_COLOR} or press Enter to skip:{RESET_COLOR} "
    ).strip()

    if new_value:
        if new_value in {"true", "false", "default"}:
            log_message += f" to '{new_value}'"
            for trader_name, trader_items in data["economy-override"]["traders"].items():
                for item in trader_items:
                    if "can-be-purchased" in item:
                        item["can-be-purchased"] = new_value
            logging.info(log_message)
            print(f"{YELLOW}'can-be-purchased'{RESET_COLOR} set to {GREEN}'{new_value}'{RESET_COLOR} for all items.")
        else:
            print(f"{RED}Invalid input.{RESET_COLOR} Please enter {CYAN}'true'{RESET_COLOR}, {CYAN}'false'{RESET_COLOR}, or {CYAN}'default'{RESET_COLOR} for {YELLOW}'can-be-purchased'{RESET_COLOR}.")
    else:
        print("No changes made")
    input("Press Enter to continue.")

# Function to start the script over by unloading the current JSON data
def start_again(data):
    logging.info(f"Function 'start_again' called")
    while True:
        choice = input("Are you sure you want to start again? (yes not y): ").strip().lower()
        if choice == "yes":
            data = None
            logging.info("*" * 40)
            logging.info("JSON data unloaded.")
            print("JSON data unloaded.")
            logging.info("User is starting over.")

            time.sleep(1)
            main()
            return True

        elif choice == "":
            logging.info("User did NOT Start over.")
            print("Returning to the main menu.")
            return False
        else:
            print("Invalid choice. Are you Sure? 'yes'")

# Function to edit Economy Fields values
def edit_economy_fields(data):
    logging.info("Function 'edit_economy_fields' called.")
    economy_fields = {
        "economy-reset-time-hours": "-1.0",
        "prices-randomization-time-hours": "-1.0",
        "tradeable-rotation-time-ingame-hours-min": "48.0",
        "tradeable-rotation-time-ingame-hours-max": "96.0",
        "tradeable-rotation-time-of-day-min": "8.0",
        "tradeable-rotation-time-of-day-max": "16.0",
        "fully-restock-tradeable-hours": "2.0",
        "trader-funds-change-rate-per-hour-multiplier": "1.0",
        "prices-subject-to-player-count": "1",
        "gold-price-subject-to-global-multiplier": "1",
        "economy-logging": "1",
        "traders-unlimited-funds": "0",
        "traders-unlimited-stock": "0",
        "only-after-player-sale-tradeable-availability-enabled": "1",
        "tradeable-rotation-enabled": "1",
        "enable-fame-point-requirement": "1"
    }

    field_names = list(economy_fields.keys())
    current_field_index = 0

    while True:
        display_main_menu_header()
        print(f"\n{Fore.GREEN}Edit Economy Fields{Style.RESET_ALL}")
        print("=" * 40)
        print(f"{Fore.WHITE}Use Up and Down arrows to navigate properties, Enter to edit, {GREEN}Esc{RESET_COLOR} to accept changes to return to the previous menu:{Style.RESET_ALL}")

        for i, field_name in enumerate(field_names):
            edited_value = edited_fields.get(field_name)
            display_value = edited_value if edited_value is not None else economy_fields[field_name]
            display_name = f"{Fore.YELLOW if i == current_field_index else Fore.WHITE} > {field_name} = {display_value}{Style.RESET_ALL}"
            print(display_name)

        key = ord(msvcrt.getch())
        if key == 80:
            current_field_index = (current_field_index + 1) % len(field_names)
        elif key == 72: 
            current_field_index = (current_field_index - 1) % len(field_names)
        elif key == 13:
            field_name = field_names[current_field_index]
            old_value = economy_fields[field_name]
            new_value = input(f"\nEnter a new value for {YELLOW}'{field_name}'{RESET_COLOR} (Current Value: {GREEN}{old_value}{RESET_COLOR}): ")
            if new_value != "":
                economy_fields[field_name] = new_value
                edited_fields[field_name] = new_value
                logging.info(f"'{field_name}' updated from '{old_value}' to '{new_value}'")
        elif key == 27:
            break
            data["economy-override"][field_name] = new_value
        elif key == 27:
            break
    print("Editing completed.")
    time.sleep(0.2)
    display_main_menu(data, edited_fields)

# Function to handle the skip action
def handle_skip():
    input("Press Enter to continue...")
    return True

# Function for fine tune menu
def fine_tune_menu(data):
    while True:
        clear_screen()
        display_main_menu_header()
        print(f"\n{GREEN}Fine Tune Merchant Items by value{RESET_COLOR}")
        print("=" * 40)
        print(f"{WHITE}1. Fine Tune {YELLOW}'base-purchase-price'{RESET_COLOR}{WHITE} Merchant Item{RESET_COLOR}")
        print(f"{WHITE}2. Fine Tune {YELLOW}'base-sell-price'{RESET_COLOR}{WHITE} Merchant Item{RESET_COLOR}")
        print(f"{WHITE}3. Edit Merchant {CYAN}Item Directly{RESET_COLOR}")

        choice = input("Choose an option: ").strip().lower()
        if choice == "1":
            fine_tune_bpp_menu(data)
        elif choice == "2":
            fine_tune_bsp_menu(data)
        elif choice == "3":
            fine_tune_trader_items(data)  
        elif choice == "4" or choice == "":
            return 
        else:
            print("Invalid choice. Please select a valid option.")

# Function to fine-tune bpp merchant items
def fine_tune_bpp_menu(data):
    logging.info(f"Function 'fine_tune_bpp_menu' called")
    outposts = ["A_0", "B_4", "C_2", "Z_3"]
    while True:
        display_main_menu_header()
        print(f"\n{GREEN}Fine Tune base-purchase-price Merchant Items{RESET_COLOR}")
        print("=" * 40)
        print(f"{GREEN}Select an Outpost:{RESET_COLOR}")
        for i, outpost in enumerate(outposts, start=1):
            print(f"{WHITE}{i}. {outpost}")

        outpost_choice = (
            input(f"\nSelect an Outpost or press Enter to skip: {RESET_COLOR}").strip().lower()
        )

        if outpost_choice == "":
            break

        if outpost_choice.isdigit():
            outpost_index = int(outpost_choice) - 1
            if 0 <= outpost_index < len(outposts):
                chosen_outpost = outposts[outpost_index]
                available_traders = [
                    key
                    for key in data["economy-override"]["traders"]
                    if key.startswith(chosen_outpost)
                ]

                while True:
                    print(
                        f"\nAvailable Merchants in Outpost {GREEN}{chosen_outpost}:{RESET_COLOR}"
                    )
                    for i, trader in enumerate(available_traders, start=1):
                        print(f"{WHITE}{i}. {trader}{RESET_COLOR}")

                    trader_choice = (
                        input("Choose a Merchant or press Enter to skip: ")
                        .strip()
                        .lower()
                    )

                    if trader_choice == "":
                        break

                    if trader_choice.isdigit():
                        trader_index = int(trader_choice) - 1
                        if 0 <= trader_index < len(available_traders):
                            chosen_trader = available_traders[trader_index]
                            trader_data = data["economy-override"]["traders"][
                                chosen_trader
                            ]

                            while True:
                                print(f"\nFine Tune Merchant Items")
                                print(
                                    f"Selected Outpost: {GREEN}{chosen_outpost}{RESET_COLOR}"
                                )
                                print(
                                    f"Selected Merchant: {GREEN}{chosen_trader}{RESET_COLOR}"
                                )

                                item_choice = (
                                    input(f"\nEnter part of the item name or {CYAN}'tradeable-code'{RESET_COLOR} to select an item or press Enter to skip: ")
                                    .strip()
                                    .lower()
                                )
                                if item_choice == "":
                                    break

                                matching_items = []
                                matching_items = [
                                    item
                                    for item in trader_data
                                    if item_choice in item["tradeable-code"].lower()
                                ]

                                if matching_items:
                                    while True:
                                        print("\nMatching items:")
                                        current_values = {}

                                        for i, item in enumerate(matching_items, start=1):
                                            current_value = float(item["base-purchase-price"])
                                            current_values[item["tradeable-code"]] = current_value

                                            print(
                                                f"{i}. {CYAN}{item['tradeable-code']}{RESET_COLOR} - Current Value: {YELLOW}{current_value}{RESET_COLOR}"
                                            )

                                        item_selection = (
                                            input(f"\nChoose an item by entering the number, type part of the item name or {CYAN}'tradeable-code'{RESET_COLOR},Enter to skip: ")
                                            .strip()
                                            .lower()
                                        )

                                        if item_selection == "":
                                            break

                                        if item_selection.isdigit():
                                            item_index = int(item_selection) - 1
                                            if (
                                                0
                                                <= item_index
                                                < len(matching_items)
                                            ):
                                                chosen_item = matching_items[
                                                    item_index
                                                ]
                                                while True:
                                                    current_value = current_values.get(chosen_item["tradeable-code"], float(chosen_item["base-purchase-price"]))

                                                    new_value = input(
                                                        f"\nEnter the new value for {CYAN}'{chosen_item['tradeable-code']}'{RESET_COLOR}  Current Value: {YELLOW}{current_value}{RESET_COLOR} or press Enter to skip: "
                                                    ).strip()
                                                    if new_value == "":
                                                        break
                                                    if new_value.replace(
                                                        ".", ""
                                                    ).isdigit():
                                                        new_value = float(new_value)
                                                        chosen_item[
                                                            "base-purchase-price"
                                                        ] = str(new_value)
                                                        edit_msg = (
                                                            f"{chosen_outpost} - Fine Tune updated the value of '{chosen_item['tradeable-code']}' to {new_value}. Old Value: {current_value}, New Value: {new_value}"
                                                        )
                                                        logging.info(edit_msg)
                                                        print(
                                                            f"\nINFO - Fine tune updated the value of {CYAN}'{chosen_item['tradeable-code']}'{RESET_COLOR} to {GREEN}{new_value}{RESET_COLOR}. Old Value: {YELLOW}{current_value}{RESET_COLOR}"
                                                        )
                                                        break
                                                    else:
                                                        print(
                                                            f"{RED}Invalid input.{RESET_COLOR} Please enter a valid number."
                                                        )
                                            else:
                                                print(
                                                    "Invalid item number. Please enter a valid number."
                                                )
                                        else:
                                            matching_items = [
                                                item
                                                for item in matching_items
                                                if item_selection
                                                in item["tradeable-code"].lower()
                                            ]

                                            if matching_items:
                                                continue
                                            else:
                                                print(
                                                    "No items found that match the entered text."
                                                )
                                else:
                                    print(
                                        f"{RED}Invalid merchant number.{RESET_COLOR} Please enter a valid number."
                                    )
                        else:
                            print(f"{RED}Invalid input.{RESET_COLOR} Please enter a valid number.")
                    else:
                        print(f"{RED}Invalid outpost number.{RESET_COLOR} Please enter a valid number.")
            display_main_menu(data, edited_fields)

# Function to fine-tune bsp merchant items
def fine_tune_bsp_menu(data):
    logging.info(f"Function 'fine_tune_bsp_menu' called")
    outposts = ["A_0", "B_4", "C_2", "Z_3"]
    while True:
        display_main_menu_header()
        print(f"\n{GREEN}Fine Tune {YELLOW}'base-sell-price'{RESET_COLOR} Merchant Items")
        print("=" * 40)
        print(f"{GREEN}Select an Outpost:{RESET_COLOR}")
        for i, outpost in enumerate(outposts, start=1):
            print(f"{i}. {outpost}")

        outpost_choice = (
            input(f"\nSelect an Outpost or press Enter to skip: {RESET_COLOR}").strip().lower()
        )

        if outpost_choice == "":
            break 

        if outpost_choice.isdigit():
            outpost_index = int(outpost_choice) - 1
            if 0 <= outpost_index < len(outposts):
                chosen_outpost = outposts[outpost_index]
                available_traders = [
                    key
                    for key in data["economy-override"]["traders"]
                    if key.startswith(chosen_outpost)
                ]

                while True:
                    print(
                        f"\nAvailable Merchants in Outpost {GREEN}{chosen_outpost}:{RESET_COLOR}"
                    )
                    for i, trader in enumerate(available_traders, start=1):
                        print(f"{WHITE}{i}. {trader}{RESET_COLOR}")

                    trader_choice = (
                        input("Choose a Merchant or press Enter to skip: ")
                        .strip()
                        .lower()
                    )
                    if trader_choice == "":
                        break
                    if trader_choice.isdigit():
                        trader_index = int(trader_choice) - 1
                        if 0 <= trader_index < len(available_traders):
                            chosen_trader = available_traders[trader_index]
                            trader_data = data["economy-override"]["traders"][
                                chosen_trader
                            ]
                            while True:
                                print(f"\nFine Tune {YELLOW}'base-sell-price'{RESET_COLOR} Merchant Items")
                                print(
                                    f"Selected Outpost: {GREEN}{chosen_outpost}{RESET_COLOR}"
                                )
                                print(
                                    f"Selected Merchant {GREEN}{chosen_trader}{RESET_COLOR}"
                                )
                                item_choice = (
                                    input(f"\nEnter part of the item name or {CYAN}'tradeable-code'{RESET_COLOR} to select an item or press Enter to skip: "
                                    )
                                    .strip()
                                    .lower()
                                )
                                if item_choice == "":
                                    break
                                matching_items = []
                                matching_items = [
                                    item
                                    for item in trader_data
                                    if item_choice in item["tradeable-code"].lower()
                                ]
                                if matching_items:
                                    while True:
                                        print("\nMatching items:")
                                        current_values = {}

                                        for i, item in enumerate(matching_items, start=1):
                                            current_value = float(item["base-sell-price"])
                                            current_values[item["tradeable-code"]] = current_value

                                            print(
                                                f"{i}. {CYAN}{item['tradeable-code']}{RESET_COLOR} - Current Value: {YELLOW}{current_value}{RESET_COLOR}"
                                            )
                                        item_selection = (
                                            input(f"\nChoose an item by entering the number, type part of the item name or {CYAN}'tradeable-code'{RESET_COLOR},Enter to skip: "
                                            )
                                            .strip()
                                            .lower()
                                        )
                                        if item_selection == "":
                                            break
                                        if item_selection.isdigit():
                                            item_index = int(item_selection) - 1
                                            if (
                                                0
                                                <= item_index
                                                < len(matching_items)
                                            ):
                                                chosen_item = matching_items[
                                                    item_index
                                                ]
                                                while True:
                                                    current_value = current_values.get(chosen_item["tradeable-code"], float(chosen_item["base-sell-price"]))
                                                    new_value = input(
                                                        f"\nEnter the new value for {CYAN}'{chosen_item['tradeable-code']}'{RESET_COLOR} Current Value: {YELLOW}{current_value}{RESET_COLOR} or press Enter to skip: "
                                                    ).strip()
                                                    if new_value == "":
                                                        break
                                                    if new_value.replace(
                                                        ".", ""
                                                    ).isdigit():
                                                        new_value = float(new_value)
                                                        chosen_item[
                                                            "base-sell-price"
                                                        ] = str(new_value)
                                                        print(
                                                            f"\nINFO - Fine tune updated the value of {CYAN}'{chosen_item['tradeable-code']}'{RESET_COLOR} to {GREEN}{new_value}{RESET_COLOR}. Old Value: {YELLOW}{current_value}{RESET_COLOR}"
                                                        )
                                                        edit_msg = f"{chosen_outpost} - Fine Tune updated the value of '{chosen_item['tradeable-code']}' to {new_value}. Old Value: {current_value}, New Value: {new_value}"
                                                        logging.info(edit_msg)
                                                        break
                                                    else:
                                                        print(
                                                            f"{RED}Invalid input.{RESET_COLOR} Please enter a valid number."
                                                        )
                                            else:
                                                print(
                                                    "Invalid item number. Please enter a valid number."
                                                )
                                        else:
                                            matching_items = [
                                                item
                                                for item in matching_items
                                                if item_selection
                                                in item["tradeable-code"].lower()
                                            ]
                                            if matching_items:
                                                continue
                                            else:
                                                print(
                                                    "No items found that match the entered text."
                                                )
                                else:
                                    print(
                                        f"{RED}Invalid merchant number.{RESET_COLOR} Please enter a valid number."
                                    )
                        else:
                            print(f"{RED}Invalid input.{RESET_COLOR} Please enter a valid number.")
                    else:
                        print(f"{RED}Invalid outpost number.{RESET_COLOR} Please enter a valid number.")
            display_main_menu(data, edited_fields)

# Function to display search results
def display_search_results(data, trader_items, current_index):
    print("\nMatching merchant items:")
    for i, trader in enumerate(trader_items, start=1):
        current_value = float(trader["base-purchase-price"])
        print(f"{i}. {CYAN}{trader['tradeable-code']}{RESET_COLOR} - Current Value: {YELLOW}{current_value}{RESET_COLOR}")

# Define a function to refine the search
def refine_search():
    return input(f"Search by part of the {CYAN}'tradeable-code'{RESET_COLOR} or press Enter to return to the previous menu: ").strip()

# Function to fine-tune merchant items
def fine_tune_trader_items(data):
    logging.info(f"Function 'fine_tune_trader_items' called")
    while True:
        display_main_menu_header()
        print(f"\n{GREEN}Fine Tune Merchant Items{RESET_COLOR}")
        print("=" * 40)
        search_string = input(f"Search by part of the {CYAN}'tradeable-code'{RESET_COLOR} or press Enter to return to the previous menu: ").strip()
        if not search_string:
            break
        trader_items = []
        for outpost, traders in data["economy-override"]["traders"].items():
            for trader in traders:
                if search_string.lower() in trader["tradeable-code"].lower():
                    trader_items.append(trader)
        if not trader_items:
            print("No merchant items found for the specified 'tradeable-code'.")
            input("Press Enter to continue...")
            continue
        current_index = 0
        while True:
            display_search_results(data, trader_items, current_index)
            choice = input("Enter the number of the merchant item to edit or press Enter to return to the previous menu: ").strip()
            if not choice:
                return
            if choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(trader_items):
                    chosen_item = trader_items[index]
                    search_results = edit_single_trader_item(data, chosen_item, outpost)
                    apply_changes = input("Apply changes to all other traders stocking the same item? (y/n): ").strip().lower()
                    if apply_changes == "y":
                        apply_changes_to_other_traders(data, outpost, chosen_item)
                        break
                else:
                    print(f"{RED}Invalid item number.{RESET_COLOR} Please enter a valid number.")
            else:
                print(f"{RED}Invalid input.{RESET_COLOR} Please enter a valid number.")

#Function to Fine Tune Directly
def edit_single_trader_item(data, trader_item, outpost):
    logging.info(f"Function 'edit_single_trader_item' called")
    current_property_index = 0
    properties = list(trader_item.keys())
    trader_key = None
    for key in data["economy-override"]["traders"]:
        if trader_item in data["economy-override"]["traders"][key]:
            trader_key = key
            break

    while True:
        clear_screen()
        display_main_menu_header()
        print(f"\n{GREEN}Editing Merchant item for outpost '{trader_key}'{RESET_COLOR}")
        print("=" * 40)
        print(f"{Fore.WHITE}Use Up and Down arrows to navigate properties, Enter to edit, {GREEN}Esc{RESET_COLOR} to accept changes to return to the previous menu:{Style.RESET_ALL}")
        print(f'"{GREEN}{trader_key}{RESET_COLOR}": [')
        print(' {')
        for index, property_name in enumerate(properties):
            current_property = properties[current_property_index]
            current_value = trader_item[current_property]
            if property_name == current_property:
                print(f'  {Fore.YELLOW}"{property_name}": "{current_value}",{Style.RESET_ALL}')
            else:
                print(f'  "{property_name}": "{trader_item[property_name]}",')
        print(' },')
        print(f' {RESET_COLOR}')
        print('],')

        key = ord(msvcrt.getch())
        if key == 80:
            current_property_index = (current_property_index + 1) % len(properties)
        elif key == 72: 
            current_property_index = (current_property_index - 1) % len(properties)
        elif key == 13:
            current_property = properties[current_property_index]
            if current_property == "tradeable-code":
                print("Tradeable code cannot be edited.")
                input("Press Enter to continue...")
            else:
                new_value = input(f"Enter a new value for {YELLOW}'{current_property}'{RESET_COLOR} (Current Value: {YELLOW}{trader_item[current_property]}):{RESET_COLOR} ").strip()
                if current_property == "can-be-purchased":
                    if new_value.lower() not in ["default", "true", "false"]:
                        print(f"{RED}Invalid input.{RESET_COLOR} Please enter {YELLOW}'default'{RESET_COLOR}, {YELLOW}'true'{RESET_COLOR}, or {YELLOW}'false'{RESET_COLOR}.")
                        input("Press Enter to continue.")
                        continue
                elif current_property in ["base-purchase-price", "base-sell-price", "delta-price", "required-famepoints"]:
                    if not new_value.replace(".", "").isdigit() or float(new_value) < -1:
                        print(f"{RED}Invalid input.{RESET_COLOR} Please enter a valid number.")
                        input("Press Enter to continue.")
                        continue
                if new_value:
                    trader_item[current_property] = new_value
                    log_message = f"Edited '{current_property}' for merchant '{trader_item['tradeable-code']}' at outpost '{trader_key}'"
                    logging.info(log_message)
        elif key == 27:
            break
    return trader_item

def get_outpost_for_trader_item(data, trader_item):
    pass

# Function to apply changes to other traders stocking the same item
def apply_changes_to_other_traders(data, outpost, trader_item):
    logging.info(f"Function 'apply_changes_to_other_traders' called")
    tradeable_code = trader_item["tradeable-code"]
    for trader_outpost, trader_list in data["economy-override"]["traders"].items():
        for trader in trader_list:
            if trader["tradeable-code"] == tradeable_code and trader_outpost != outpost:
                for prop in trader_item:
                    if prop != "tradeable-code":
                        old_value = trader[prop]
                        new_value = trader_item[prop]
                        if old_value != new_value:
                            trader[prop] = new_value
                            log_message = f"Edited '{prop}' for merchant '{tradeable_code}' at outpost '{trader_outpost}'"
                            log_message += f" (Old Value: {old_value}, New Value: {new_value})"
                            logging.info(log_message)

# Function to edit the "can-be-purchased" field across all merchants
def spread_edit_can_be_purchased(data):
    logging.info("Function 'spread_edit_can_be_purchased' called.")
    outpost_list = ["A_0", "B_4", "C_2", "Z_3"]
    chosen_outpost = "A_0"
    while True:
        display_main_menu_header()
        print(f"\n{GREEN}Spread Edit item {YELLOW}'can-be-purchased'{RESET_COLOR} true/default/false")
        print("=" * 40)
        print(f"{WHITE}Choose a Merchant or press Enter to go back:")
        traders = [
            "Armory",
            "BoatShop",
            "Hospital",
            "Mechanic",
            "Saloon",
            "Trader",
            "Barber",
        ]
        for i, trader in enumerate(traders, start=1):
            print(f"{WHITE}{i}. {trader}{RESET_COLOR}")

        trader_selection = (input(f"\n{RESET_COLOR}Select a Merchant by entering the number or press Enter to go back:")
            .strip()
            .lower()
        )

        if trader_selection == "":
            break

        if trader_selection.isdigit():
            trader_index = int(trader_selection) - 1
            if 0 <= trader_index < len(traders):
                trader_name = traders[trader_index]
                trader_data = data["economy-override"]["traders"].get(
                    f"{chosen_outpost}_{trader_name}", []
                )

                while True:
                    display_main_menu_header()
                    print(
                        f"\n{GREEN}Spread Edit (can-be-purchased){WHITE} - Merchant: {GREEN}{trader_name}{RESET_COLOR}"
                    )
                    print("=" * 40)
                    print(f"{WHITE}Choose an item or press Enter to go back:{RESET_COLOR}")
                    search_string = (
                        input(f"Type part of the item name or {CYAN}'tradeable-code'{RESET_COLOR} or press Enter: ")
                        .strip()
                        .lower()
                    )
                    if search_string == "":
                        break
                    matching_items = []
                    for item_data in trader_data:
                        item_code = item_data.get("tradeable-code", "")
                        if search_string in item_code.lower() or (
                            item_data.get("tradeable-code")
                            and search_string
                            in item_data.get("tradeable-code", "").lower()
                        ):
                            matching_items.append(item_data)

                    if not matching_items:
                        print(
                            f"No items found that match the entered text."
                        )
                        continue

                    while True:
                        print("\nMatching items:")
                        for i, item_data in enumerate(
                            matching_items, start=1
                        ):
                            item_code = item_data.get("tradeable-code", "")
                            current_value = item_data.get("can-be-purchased", "")
                            print(
                                f"{i}. {CYAN}{item_code}{RESET_COLOR} - Current Value: {YELLOW}{current_value}{RESET_COLOR}"
                            )

                        item_selection = (
                            input(f"\nChoose an item by entering the number, type part of the item name or {CYAN}'tradeable-code'{RESET_COLOR}, or press Enter to skip: "
                            )
                            .strip()
                            .lower()
                        )

                        if item_selection == "":
                            break

                        if item_selection.isdigit():
                            item_index = int(item_selection) - 1
                            if 0 <= item_index < len(matching_items):
                                item_data = matching_items[item_index]
                                while True:
                                    new_value = input(
                                        f"\nEnter the new value for {CYAN}'{item_data['tradeable-code']}'{RESET_COLOR} Current Value: {YELLOW}{current_value}{RESET_COLOR} or press Enter to skip: "
                                    ).strip()
                                    if new_value == "":
                                        break
                                    elif new_value.lower() in ["true", "false", "default"]:
                                        item_code = item_data.get(
                                            "tradeable-code", ""
                                        )
                                        for outpost_code in outpost_list:
                                            for item in data["economy-override"][
                                                "traders"
                                            ].get(
                                                f"{outpost_code}_{trader_name}",
                                                [],
                                            ):
                                                if (
                                                    item.get(
                                                        "tradeable-code"
                                                    )
                                                    == item_code
                                                ):
                                                    item[
                                                        "can-be-purchased"
                                                    ] = new_value

                                        print(
                                            f"\nINFO - Spread Edit {YELLOW}'can-be-purchased'{RESET_COLOR} updated the value of {BLUE}'{item_data.get('tradeable-code', '')}'{RESET_COLOR} to {GREEN}{new_value}{RESET_COLOR} for {GREEN}all merchants.{RESET_COLOR}"
                                        )
                                        edit_msg = f"Spread Edit (can-be-purchased) updated the value of '{item_data.get('tradeable-code', '')}' to {new_value} for ALL merchants."
                                        logging.info(edit_msg)
                                        break
                                    else:
                                        print(
                                            f"{RED}Invalid input.{RESET_COLOR} Please enter {YELLOW}'true'{RESET_COLOR}, {YELLOW}'false'{RESET_COLOR}, or {YELLOW}'default'{RESET_COLOR}."
                                        )
                            else:
                                print(
                                    f"{RED}Invalid item number{RESET_COLOR}. Please enter a valid number."
                                )
                        else:
                            matching_items = [
                                item_data
                                for item_data in matching_items
                                if item_selection
                                in item_data.get(
                                    "tradeable-code", ""
                                ).lower()
                            ]
                            if not matching_items:
                                print(
                                    "No items found that match the entered text."
                                )
                                break
            else:
                print(f"{RED}Invalid merchant number. {WHITE} Please enter a valid number.")
        else:
            print(f"{RED}Invalid input. {WHITE} Please enter a valid number.")

# Function to save changes and exit
def save_and_exit(data):
    logging.info("Function 'save_and_exit' called.")
    save_filename = f"economyoverride_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(save_filename, "w") as json_file:
        json.dump(data, json_file, indent=4)
    print(f"\n\n\n\nChanges saved to {GREEN}'{save_filename}{RESET_COLOR}'")
    logging.info(f"Changes saved to '{save_filename}'")
    logging.info("Script ended.")
    print("Exiting...")
    time.sleep(1)
    log_file = None
    for handler in logging.root.handlers:
        if isinstance(handler, logging.FileHandler):
            log_file = handler.baseFilename

    if log_file:
        try:
            subprocess.Popen(["notepad.exe", log_file], shell=True)
        except Exception as e:
            print(f"Error opening log file: {str(e)}")
    else:
        print("No log file found in the logging configuration.")

    sys.exit(0)

# Function to exit without saving
def exit_without_saving(data):
    logging.info("Function 'exit_without_saving' called.")
    first_choice = input(f"{YELLOW}Are you sure you want to exit without saving changes? (y/n):{RESET_COLOR} ").strip().lower()
    if first_choice == 'y':
        second_choice = input(f"{CYAN}Are you really sure?{RESET_COLOR} This will discard all changes. (y/n): ").strip().lower()
        if second_choice == 'y':
            print("Exiting without saving...")
            logging.info("Script ended.")
            sys.exit(0)
            time.sleep(1)
        else:
            print("Returning to the main menu.")
    elif first_choice == 'n':
        print("Returning to the main menu.")
    else:
        print("Invalid choice. Returning to the main menu.")

# Function to open the log file
def open_logfile(log_filename):
    logging.info("Function 'open_logfile' called.")
    try:
        subprocess.Popen(["notepad.exe", log_filename], shell=True)
    except Exception as e:
        print(f"Error opening log file: {str(e)}")

# Main function
def main():
    display_startup_screen()
    initialize_logging("economyovveride")

    while True:
        json_files = glob.glob("*.json")

        if not json_files:
            print("No JSON files found.")
            time.sleep(2)
            sys.exit(1)

        if invalid_json_files:
            print("\nInvalid JSON files:")
            for i, filename in enumerate(invalid_json_files, start=1):
                print(f"{i}. {Fore.MAGENTA}{filename}{Style.RESET_ALL}")

        print(f"\n{Fore.GREEN}Load your economyoverride{Style.RESET_ALL}")
        print("=" * 40)
        print("JSON files found where the script is run:")
        for i, filename in enumerate(json_files, start=1):
            if filename in invalid_json_files:
                print(f"{i}. {Fore.MAGENTA}{filename}{Style.RESET_ALL}")
            else:
                print(f"{i}. {filename}")

        while True:
            file_choice = input(
                f"\nEnter the number of the Scum Economy file you want to edit or {GREEN}'q'{RESET_COLOR} to quit: "
            ).strip()

            if file_choice.lower() == 'q':
                sys.exit(0)
            elif file_choice.isdigit():
                file_index = int(file_choice) - 1
                if 0 <= file_index < len(json_files):
                    serverconfig_filename = json_files[file_index]

                    data = load_json(serverconfig_filename, "economyoverride.log")

                    if data is not None:

                        logging.info(f"Loaded JSON file: {serverconfig_filename}")

                        display_main_menu(data, edited_fields)
                    else:
                        invalid_json_files.append(serverconfig_filename)  
                        break  
                else:
                    print(f"{RED}Invalid file number.{WHITE} Please enter a valid number.")
            else:
                print(f"{RED}Invalid input. {WHITE} Please enter a valid number or 'q' to quit.")


# Function to Spread edit the price of an item across all merchants within a specific outpost
def spread_edit(data):
    logging.info("Function 'spread_edit' called.")
    outpost_list = ["A_0", "B_4", "C_2", "Z_3"]
    chosen_outpost = "A_0"

    while True:
        display_main_menu_header()
        print(f"\n{GREEN}Spread Edit{RESET_COLOR}")
        print("=" * 40)
        print(f"{WHITE}Choose a Merchant or press Enter to go back:")
        traders = [
            "Armory",
            "BoatShop",
            "Hospital",
            "Mechanic",
            "Saloon",
            "Trader",
            "Barber",
        ]
        for i, trader in enumerate(traders, start=1):
            print(f"{WHITE}{i}. {trader}{RESET_COLOR}")

        trader_selection = (
            input("\nSelect a Merchant by entering the number or press Enter to go back: ").strip().lower()
        )

        if trader_selection == "":
            break

        if trader_selection.isdigit():
            trader_index = int(trader_selection) - 1
            if 0 <= trader_index < len(traders):
                trader_name = traders[trader_index]
                trader_data = data["economy-override"]["traders"].get(f"{chosen_outpost}_{trader_name}", [])

                while True:
                    display_main_menu_header()
                    print(f"\n{GREEN}Spread Edit{RESET_COLOR} - Merchant: {GREEN}{trader_name}{RESET_COLOR}")
                    print("=" * 40)
                    print(f"{WHITE}Choose the value to edit or press Enter to go back:")
                    print(f"1. Edit {YELLOW}'base-purchase-price'{RESET_COLOR}")
                    print(f"2. Edit {YELLOW}'base-sell-price'{RESET_COLOR}")

                    price_selection = input("Enter the number to select a value or press Enter to skip: ").strip().lower()

                    if price_selection == "":
                        break

                    if price_selection == "1":
                        price_type = "base-purchase-price"
                        price_name = "base-purchase-price"
                    elif price_selection == "2":
                        price_type = "base-sell-price"
                        price_name = "base-sell-price"
                    else:
                        print("Invalid price selection. Please enter a valid number (1 or 2).")
                        continue

                    while True:
                        display_main_menu_header()
                        print(f"\n{GREEN}Spread Edit{RESET_COLOR} - Merchant: {GREEN}{trader_name}{RESET_COLOR} - Price: {YELLOW}{price_name}{RESET_COLOR}")
                        print("=" * 40)
                        print(f"{WHITE}Choose an item or press Enter to go back:")
                        search_string = input(f"Type part of the item name or {CYAN}'tradeable-code'{RESET_COLOR} or press Enter:").strip().lower()

                        if search_string == "":
                            break

                        matching_items = []
                        for item_data in trader_data:
                            item_code = item_data.get("tradeable-code", "")
                            if search_string in item_code.lower() or (item_data.get("tradeable-code") and search_string in item_data.get("tradeable-code", "").lower()):
                                matching_items.append(item_data)

                        if not matching_items:
                            print(f"No items found that match the entered text.")
                            continue

                        while True:
                            print("\nMatching items:")
                            current_values = {}
                            for i, item_data in enumerate(matching_items, start=1):
                                item_code = item_data.get("tradeable-code", "")
                                current_value = float(item_data.get(price_type, 0))
                                current_values[item_data["tradeable-code"]] = current_value

                                print(f"{i}. {CYAN}{item_code}{RESET_COLOR} - Current Value: {YELLOW}{current_value}{RESET_COLOR}")
                            
                            item_selection = input(f"\nEnter part of the item name or {CYAN}'tradeable-code'{RESET_COLOR} to select an item or press Enter to skip: ").strip().lower()
                            
                            if item_selection == "":
                                break
                            
                            if item_selection.isdigit():
                                item_index = int(item_selection) - 1
                                if 0 <= item_index < len(matching_items):
                                    chosen_item = matching_items[item_index]
                                    
                                    while True:
                                        current_value = current_values.get(chosen_item["tradeable-code"], float(chosen_item[price_type]))
                                        
                                        new_value = input(f"\nEnter the new value for {CYAN}'{chosen_item['tradeable-code']}'{RESET_COLOR} Current Value: {YELLOW}{current_value}{RESET_COLOR} or press Enter to skip: ").strip()
                                        
                                        if new_value == "":
                                            break
                                        
                                        if new_value.replace(".", "").isdigit():
                                            new_value = str(int(float(new_value)))  # Convert to int, then to string
                                            
                                            # Update price for all merchants in all outposts
                                            chosen_item[price_type] = new_value
                                            for outpost_code in outpost_list:
                                                for item in data["economy-override"]["traders"].get(f"{outpost_code}_{trader_name}", []):
                                                    if item.get("tradeable-code") == chosen_item["tradeable-code"]:
                                                        item[price_type] = new_value

                                            print(f"\nINFO - Spread Edit updated the value of {CYAN}'{chosen_item['tradeable-code']}'{RESET_COLOR} to {GREEN}{new_value}{RESET_COLOR} for {GREEN}all merchants.{RESET_COLOR}")
                                            edit_msg = f"Spread Edit updated the value of '{chosen_item['tradeable-code']}' to {new_value} for ALL merchants."
                                            logging.info(edit_msg)
                                            break
                                        else:
                                            print(f"{RED}Invalid input{RESET_COLOR}. Please enter a valid number.")
                                else:
                                    print(f"{RED}Invalid item number{RESET_COLOR}. Please enter a valid number.")
                            else:
                                matching_items = [item_data for item_data in matching_items if item_selection in item_data.get("tradeable-code", "").lower()]
                                
                                if not matching_items:
                                    print("No items found that match the entered text.")
                                    break
            else:
                print(f"{RED}Invalid merchant number{RESET_COLOR}. Please enter a valid number.")
        else:
            print(f"{RED}Invalid input{RESET_COLOR}. Please enter a valid number.")


def category_price_changes(data):
    logging.info(f"Function 'category_price_changes' called")
    category_map = {
        'A': 'Cal_',
        'B': 'Magazine',
        'C': 'Weapon_',
        'D': 'Crafted',
        'E': '_AP_CR'
    }
    
    while True:
        display_main_menu_header()

        print(f"\n{GREEN}Category-Based Price Changes{RESET_COLOR}")
        print("=" * 40)
        print(f"{WHITE}Select a Category:{RESET_COLOR}")
        print(f" A. {YELLOW}Cal_{RESET_COLOR}")
        print(f" B. {YELLOW}Magazine{RESET_COLOR}")
        print(f" C. {YELLOW}Weapon_ (excluding Weapon_parts){RESET_COLOR}")
        print(f" D. {YELLOW}Crafted{RESET_COLOR}")
        print(f" E. {YELLOW}_AP_CR{RESET_COLOR}")
        print(f" Q. {RED}Return to Main Menu{RESET_COLOR}")

        category_choice = input("\nChoose a category (A-E) or press 'Q' to quit: ").strip().upper()

        if category_choice == 'Q':
            break

        if category_choice not in category_map:
            print(f"{RED}Invalid choice. Please select a valid category.{RESET_COLOR}")
            continue

        # Get the search pattern for the selected category
        search_pattern = category_map[category_choice]

        # Filter items based on the selected category
        matching_items = []
        for trader_name, trader_items in data["economy-override"]["traders"].items():
            for item in trader_items:
                tradeable_code = item.get("tradeable-code", "")
                if category_choice == 'C' and "Weapon_parts" in tradeable_code:
                    continue  # Exclude Weapon_parts for category C
                if search_pattern in tradeable_code:
                    matching_items.append(item)

        if not matching_items:
            print(f"{RED}No items found for category {category_choice}.{RESET_COLOR}")
            continue

        # Once we have the matching items, show the edit menu for the category
        perform_price_changes_for_category(matching_items, category_choice, data)


def perform_price_changes_for_category(items, category_choice, data):
    logging.info(f"Function 'perform_price_changes_for_category' called for category {category_choice}")
    
    excluded_items = set()  # To track which items are excluded from changes
    page_size = 10  # Number of items to display per page
    total_items = len(items)
    current_page = 0

    while True:
        display_main_menu_header()

        # Paginate the items
        start_index = current_page * page_size
        end_index = min(start_index + page_size, total_items)
        paginated_items = items[start_index:end_index]

        category_name = {
            'A': 'Cal_',
            'B': 'Magazine',
            'C': 'Weapon_ (excluding Weapon_parts)',
            'D': 'Crafted',
            'E': '_AP_CR'
        }[category_choice]

        print(f"\n{GREEN}Price Changes for Category: {YELLOW}{category_name}{RESET_COLOR}")
        print("=" * 40)
        for i, item in enumerate(paginated_items, start=start_index + 1):
            excluded_text = f"{RED}(Excluded){RESET_COLOR}" if id(item) in excluded_items else ""
            print(f"{WHITE}{i}. {item['tradeable-code']} - Base Purchase Price: {item.get('base-purchase-price', 'N/A')} - Base Sell Price: {item.get('base-sell-price', 'N/A')} {excluded_text}{RESET_COLOR}")
        
        print(f"\nPage {current_page + 1}/{(total_items + page_size - 1) // page_size}")
        print(f" N. Next page")
        print(f" P. Previous page")
        print(f" E. Exclude/Include item from changes")
        print(f" F. Choose Field (Base Purchase Price or Base Sell Price)")
        print(f" A. Apply changes to all non-excluded items")
        print(f" Q. Return to Category Selection")

        choice = input("\nChoose an option: ").strip().lower()

        if choice == 'n' and end_index < total_items:
            current_page += 1
        elif choice == 'p' and current_page > 0:
            current_page -= 1
        elif choice == 'e':
            exclude_index = input(f"Enter the number of the item to exclude/include: ").strip()
            try:
                exclude_index = int(exclude_index) - 1
                if start_index <= exclude_index < end_index:
                    item_to_toggle = paginated_items[exclude_index - start_index]
                    item_id = id(item_to_toggle)
                    if item_id in excluded_items:
                        excluded_items.remove(item_id)
                        print(f"{GREEN}Item included for changes.{RESET_COLOR}")
                    else:
                        excluded_items.add(item_id)
                        print(f"{RED}Item excluded from changes.{RESET_COLOR}")
                else:
                    print(f"{RED}Invalid index.{RESET_COLOR}")
            except ValueError:
                print(f"{RED}Invalid input. Please enter a valid item number.{RESET_COLOR}")
        elif choice == 'f':
            field_to_edit = choose_field_for_edit()
        elif choice == 'a':
            if field_to_edit:
                apply_price_changes_to_all(items, excluded_items, data, field_to_edit, category_choice)
                break
            else:
                print(f"{RED}You need to choose a field to edit first.{RESET_COLOR}")
        elif choice == 'q':
            break
        else:
            print(f"{RED}Invalid choice.{RESET_COLOR} Please enter a valid option number.")


def choose_field_for_edit():
    logging.info(f"Function 'choose_field_for_edit' called")

    while True:
        print(f"\n{GREEN}Choose Field to Edit:{RESET_COLOR}")
        print("=" * 40)
        print(f"{WHITE}1. Base Purchase Price{RESET_COLOR}")
        print(f"{WHITE}2. Base Sell Price{RESET_COLOR}")
        print(f"{WHITE}Q. Return to Category Menu{RESET_COLOR}")

        choice = input("Enter your choice: ").strip().lower()

        if choice == '1':
            return 'base-purchase-price'
        elif choice == '2':
            return 'base-sell-price'
        elif choice == 'q':
            return None
        else:
            print(f"{RED}Invalid choice. Please select a valid field.{RESET_COLOR}")


def apply_price_changes_to_all(items, excluded_items, data, field, category_choice):
    logging.info(f"Function 'apply_price_changes_to_all' called for field {field}")

    print(f"\n{GREEN}Apply Price Changes to All Non-Excluded Items in Category {category_choice}{RESET_COLOR}")
    print("=" * 40)
    percentage_change = input(f"Enter the percentage change (e.g., -10 for -10%, +15 for +15%): ").strip()

    try:
        percentage_change = int(percentage_change)
    except ValueError:
        print(f"{RED}Invalid input. Please enter a valid percentage.{RESET_COLOR}")
        return

    for item in items:
        if id(item) not in excluded_items:
            current_price = int(float(item.get(field, 0)))
            new_price = current_price + (current_price * percentage_change // 100)
            item[field] = str(max(1, new_price))  # Ensure no decimal places and price doesn't drop below 1

            logging.info(f"Item {item['tradeable-code']} updated. Old {field}: {current_price}, New {field}: {new_price}")

    print(f"{GREEN}Prices updated for all non-excluded items in field {field}.{RESET_COLOR}")
    input("Press Enter to continue...")



if __name__ == "__main__":
    main()
