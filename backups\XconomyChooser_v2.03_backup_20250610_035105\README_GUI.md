# 🎮 SCUM Economy Chooser - Advanced GUI v1.45c

## Overview

This is the advanced GUI version of the SCUM Economy Chooser, built with CustomTkinter for a modern, dark-themed interface. It provides a visual way to edit your SCUM server's economy configuration while maintaining full compatibility with the original CLI version.

## Features

### 🌟 **Core Features**
- **Modern Dark Theme Interface** - Built with CustomTkinter
- **File Management** - Load, scan, and reload JSON files easily
- **Data Visualization** - Tree view, JSON preview, and statistics
- **CLI Integration** - Launch the original CLI version with one click
- **Real-time Preview** - See your data structure and changes instantly

### 🌍 **Global Operations**
- **Global Price Changes** - Adjust all prices by percentage with preview
- **Economy Fields Editor** - Modify core economy settings with descriptions
- **Category Management** - Smart category detection and editing

### 👤 **Merchant Operations**
- **Individual Merchant Editing** - Target specific traders
- **Outpost-wide Changes** - Bulk edit entire outposts
- **Fine-tuning Tools** - Item-level precision editing

### 🔬 **Advanced Tools**
- **Spread Editing** - Apply changes across multiple traders
- **Purchase Settings** - Manage item availability
- **Search Functionality** - Find specific items quickly

## Installation

### Prerequisites
- Python 3.7 or higher
- Required packages (install with pip):

```bash
pip install customtkinter
```

### Files Needed
- `scum_economy_gui_complete.py` - Main GUI application
- `1_45c.py` - Original CLI version (for CLI integration)
- Your `economyoverride.json` file

## Quick Start

1. **Launch the GUI:**
   ```bash
   python scum_economy_gui_complete.py
   ```

2. **Load your economy file:**
   - Click "📂 Load JSON" to browse for your file
   - Or click "🔍 Scan Dir" to see all JSON files in the current directory

3. **Explore your data:**
   - Use the "🌳 Data Tree" tab to browse your economy structure
   - Check the "📊 Statistics" tab for insights
   - View raw JSON in the "📋 JSON Preview" tab

4. **Make changes:**
   - Select a tool from the left panel
   - Follow the guided interface for each operation
   - Preview changes before applying

5. **Save your work:**
   - Click "💾 Save & Exit" to save with timestamp
   - Original files are never overwritten

## Interface Guide

### Main Window Layout

```
┌─────────────────────────────────────────────────────────┐
│                    🎮 SCUM Economy Chooser              │
│                   Advanced GUI v1.45c                   │
├─────────────────────────────────────────────────────────┤
│  📁 File Management                                     │
│  [Load JSON] [Scan Dir] [Reload]                       │
├─────────────────┬───────────────────────────────────────┤
│ ⚙️ Tools Panel  │  📊 Data Preview & Analysis          │
│                 │                                       │
│ 🌍 Global Ops   │  [📈 Overview] [🌳 Tree] [📋 JSON]   │
│ • Global Price  │                                       │
│ • Economy Fields│  Content area shows:                  │
│ • Categories    │  - Welcome info                       │
│                 │  - Data tree structure                │
│ 👤 Merchant Ops │  - JSON preview                       │
│ • Merchant Edit │  - Statistics                         │
│ • Outpost Edit  │                                       │
│ • Fine Tune     │                                       │
│                 │                                       │
│ 🔬 Advanced     │                                       │
│ • Spread Edit   │                                       │
│ • Purchase Set  │                                       │
└─────────────────┴───────────────────────────────────────┤
│ Status: Ready                    [💾 Save & Exit] [❌]  │
└─────────────────────────────────────────────────────────┘
```

### Tool Descriptions

#### 🌍 Global Operations

**💰 Global Price Changes**
- Adjust all merchant prices by percentage
- Choose between purchase and sell prices
- Preview changes before applying
- Range: -99% to +100%

**⚙️ Economy Fields**
- Modify core economy settings
- Includes descriptions for each field
- Reset to defaults option
- Covers: timers, multipliers, flags

**📂 Category Management**
- Smart category detection from item codes
- Shows item counts per category
- Batch operations (coming soon)

#### 👤 Merchant Operations

**👤 Edit Merchant Level** (Coming Soon)
- Target specific merchants
- Individual trader adjustments
- Item-by-item editing

**🏢 Edit Outpost Level** (Coming Soon)
- Outpost-wide operations
- Bulk merchant editing
- Regional price adjustments

**🔧 Fine Tune Items** (Coming Soon)
- Item-level precision
- Search and modify
- Field-by-field editing

#### 🔬 Advanced Tools

**📊 Spread Edit Items** (Coming Soon)
- Cross-trader operations
- Bulk synchronization
- Filter-based editing

**🛒 Purchase Settings** (Coming Soon)
- Availability management
- Fame point requirements
- Purchase flags

## Data Analysis Features

### 📈 Overview Tab
- Welcome information
- Quick start guide
- Feature overview

### 🌳 Data Tree Tab
- Hierarchical view: Outpost → Trader → Items
- Search functionality
- Click items to view details
- Performance optimized (shows first 50 items per trader)

### 📋 JSON Preview Tab
- Raw JSON data view
- Selected item details
- Structure inspection
- Formatted display

### 📊 Statistics Tab
- Total traders and items
- Outpost breakdown
- Top item categories
- Price distribution analysis

## Safety Features

- **No File Overwriting** - Original files are never modified
- **Timestamped Saves** - New files get unique timestamps
- **Preview Before Apply** - See changes before committing
- **Data Validation** - Input validation and error handling
- **Backup Recommendations** - Always backup before editing

## CLI Integration

The GUI includes seamless integration with the original CLI version:

- Click the "🖥️ CLI" button to launch the command-line version
- Both versions work with the same file format
- Use CLI for complex operations not yet in GUI
- Switch between interfaces as needed

## Troubleshooting

### Common Issues

**"No module named 'customtkinter'"**
```bash
pip install customtkinter
```

**"Failed to load JSON file"**
- Check file format and syntax
- Ensure it's a valid SCUM economy file
- Try the test_economy.json file included

**"No JSON files found"**
- Ensure you're in the correct directory
- Check file extensions (.json)
- Use "Load JSON" to browse manually

### Performance Tips

- Large economy files may take time to load
- Tree view shows limited items for performance
- Use search to find specific items quickly
- Close unused dialog windows

## Development Status

### ✅ Completed Features
- Modern GUI framework
- File management system
- Data visualization
- Global price changes
- Economy fields editor
- Category detection
- CLI integration
- Statistics analysis

### 🚧 In Development
- Individual merchant editing
- Outpost-level operations
- Fine-tuning tools
- Spread editing
- Purchase settings
- Advanced category operations

### 🔮 Planned Features
- Undo/Redo functionality
- Batch operations
- Import/Export presets
- Advanced filtering
- Data comparison tools

## Support

For issues, questions, or feature requests:
1. Check this README first
2. Try the original CLI version for comparison
3. Ensure you have the latest version
4. Report bugs with specific error messages

## Credits

- **Original CLI Version**: V1nceTD
- **GUI Enhancement**: Augment Agent
- **Framework**: CustomTkinter
- **Game**: SCUM by Gamepires

---

**⚠️ Important**: Always backup your economy files before making changes. This tool modifies game configuration files that affect your server's economy.
