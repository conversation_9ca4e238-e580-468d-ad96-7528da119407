{"fish": {"name": "🐟 Fish Items", "description": "All fish items for consumption", "filters": [{"type": "starts_with", "value": "Fish_"}, {"type": "not_contains", "value": "fishing"}, {"type": "not_contains", "value": "rod"}]}, "weapons": {"name": "🔫 Weapons", "description": "All combat weapons", "filters": [{"type": "starts_with", "value": "Weapon_"}, {"type": "not_contains", "value": "parts"}, {"type": "not_contains", "value": "attachment"}]}, "food": {"name": "🥫 Food & Canned", "description": "Food items and canned goods", "filters": [{"type": "contains_any", "value": ["food", "can_", "meat"]}, {"type": "not_starts_with", "value": "Weapon_"}]}, "medical": {"name": "💊 Medical Items", "description": "Medical supplies and healing items", "filters": [{"type": "contains_any", "value": ["bandage", "pill", "syringe", "medical"]}]}, "tools": {"name": "🔧 Tools & Equipment", "description": "Tools and utility equipment", "filters": [{"type": "contains_any", "value": ["tool", "hammer", "wrench", "screwdriver"]}, {"type": "not_starts_with", "value": "Weapon_"}]}, "military": {"name": "🎖️ Military Gear", "description": "Military equipment (non-weapon)", "filters": [{"type": "contains_any", "value": ["Military", "Army", "Combat"]}, {"type": "not_starts_with", "value": "Weapon_"}]}}