#!/usr/bin/env python3
"""
SCUM Economy Chooser - Enhanced GUI v1.45c-DevBuild
Complete GUI implementation with CLI integration, Undo/Redo, F.I.S.H. Logic, Build Mode
Author: V1nceTD (Enhanced by Augment Agent)

🔧 DevBuild Features:
- Build Mode: Long right-click to drag GUI elements
- Snap-to-alignment system with visual guides
- Live layout editing and persistence
- Process-safe saving to prevent CLI/GUI conflicts
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import sys
import subprocess
import threading
import time
import platform
from datetime import datetime
import logging
import math
import glob
import copy
from collections import defaultdict, Counter

# Set appearance
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class DebugTracer:
    """4-Tier Debug Tracing System for XconomyChooser v2.01 (Per Scum Admin Suite Spec)"""

    # Debug tiers (matching your documentation)
    TIER_0 = 0   # Dev Mode off – no debug features visible
    TIER_1 = 1   # Safe UI debug – button hover logs, tooltips, minor read-only views
    TIER_2 = 2   # Interactive debugging – toggle fields, reset options, local config injection
    TIER_3 = 3   # Runtime tracing – log interceptors, simulation overlays, cross-module data tests
    TIER_4 = 4   # Experimental tools – unstable prototypes, internal module dev menus

    def __init__(self):
        # Get debug tier from environment variable (matches your spec)
        self.debug_tier = int(os.getenv('XCHOOSE_DEBUG_LEVEL', '0'))
        self.dev_mode = os.getenv('XCHOOSE_DEV_MODE', '0') == '1'

        # Master Dev Mode switch controls all tiers (per your spec)
        if self.dev_mode:
            self.debug_tier = max(self.debug_tier, self.TIER_3)  # Auto-enable runtime tracing in dev mode

        # Setup logging
        self.setup_logging()

        # Function call stack for runtime tracing
        self.call_stack = []
        self.ui_interactions = []  # Track UI interactions for Tier 1
        self.config_changes = []   # Track config changes for Tier 2

        if self.debug_tier > self.TIER_0:
            self.tier_1_ui_log("Debug System", f"Tracer initialized - Tier: {self.debug_tier}, Dev Mode: {self.dev_mode}")

    def setup_logging(self):
        """Setup logging configuration"""
        if self.debug_tier > self.TIER_0:
            log_format = '%(asctime)s [%(levelname)s] %(message)s'
            logging.basicConfig(
                level=logging.DEBUG if self.debug_tier >= self.TIER_3 else logging.INFO,
                format=log_format,
                handlers=[
                    logging.StreamHandler(),
                    logging.FileHandler('xconomy_debug.log', mode='a')
                ]
            )
            self.logger = logging.getLogger('XconomyChooser')
        else:
            self.logger = None

    def error(self, message, exception=None):
        """Log ERROR level messages (Tier 1+)"""
        if self.debug_tier >= self.TIER_1:
            full_message = f"❌ ERROR: {message}"
            if exception:
                full_message += f" | Exception: {str(exception)}"
            print(full_message)
            if self.logger:
                self.logger.error(full_message)

    def info(self, message):
        """Log INFO level messages (Tier 2+)"""
        if self.debug_tier >= self.TIER_2:
            full_message = f"ℹ️ INFO: {message}"
            print(full_message)
            if self.logger:
                self.logger.info(full_message)

    def debug(self, message):
        """Log DEBUG level messages (Tier 3+)"""
        if self.debug_tier >= self.TIER_3:
            full_message = f"🔍 DEBUG: {message}"
            print(full_message)
            if self.logger:
                self.logger.debug(full_message)

    def trace(self, message):
        """Log TRACE level messages (Tier 4)"""
        if self.debug_tier >= self.TIER_4:
            indent = "  " * len(self.call_stack)
            full_message = f"🔬 TRACE: {indent}{message}"
            print(full_message)
            if self.logger:
                self.logger.debug(full_message)

    def trace_enter(self, function_name, args=None, kwargs=None):
        """Trace function entry (Tier 4)"""
        if self.debug_tier >= self.TIER_4:
            self.call_stack.append(function_name)
            args_str = f" args={args}" if args else ""
            kwargs_str = f" kwargs={kwargs}" if kwargs else ""
            self.trace(f"→ ENTER {function_name}{args_str}{kwargs_str}")

    def trace_exit(self, function_name, result=None):
        """Trace function exit (Tier 4)"""
        if self.debug_tier >= self.TIER_4:
            if self.call_stack and self.call_stack[-1] == function_name:
                self.call_stack.pop()
            result_str = f" result={result}" if result is not None else ""
            self.trace(f"← EXIT {function_name}{result_str}")

    def trace_var(self, var_name, value):
        """Trace variable state (Tier 4)"""
        if self.debug_tier >= self.TIER_4:
            self.trace(f"📊 VAR {var_name} = {value}")

    def operation_start(self, operation_name, details=None):
        """Log start of major operation"""
        if self.debug_tier >= self.TIER_2:
            details_str = f" | {details}" if details else ""
            self.info(f"🚀 OPERATION START: {operation_name}{details_str}")

    def operation_end(self, operation_name, success=True, details=None):
        """Log end of major operation"""
        if self.debug_tier >= self.TIER_2:
            status = "✅ SUCCESS" if success else "❌ FAILED"
            details_str = f" | {details}" if details else ""
            self.info(f"🏁 OPERATION END: {operation_name} - {status}{details_str}")

    def performance_timer(self, operation_name):
        """Context manager for performance timing"""
        return PerformanceTimer(self, operation_name)

    # Tier-specific debug methods
    def tier_1_ui_log(self, action, details):
        """Tier 1: Safe UI debug - button hover logs, tooltips, minor read-only views"""
        if self.debug_tier >= self.TIER_1:
            message = f"🖱️ UI: {action} | {details}"
            print(message)
            if hasattr(self, 'logger') and self.logger:
                self.logger.info(message)

    def tier_2_config_change(self, setting, value):
        """Tier 2: Interactive debugging - toggle fields, reset options, local config injection"""
        if self.debug_tier >= self.TIER_2:
            message = f"⚙️ CONFIG: {setting} = {value}"
            print(message)
            if hasattr(self, 'logger') and self.logger:
                self.logger.debug(message)

    def tier_2_reset_option(self, component, action):
        """Tier 2: Reset options"""
        if self.debug_tier >= self.TIER_2:
            message = f"🔄 RESET: {component} | {action}"
            print(message)
            if hasattr(self, 'logger') and self.logger:
                self.logger.debug(message)

    def tier_2_config_injection(self, target, data):
        """Tier 2: Local config injection"""
        if self.debug_tier >= self.TIER_2:
            message = f"💉 INJECT: {target} | {data}"
            print(message)
            if hasattr(self, 'logger') and self.logger:
                self.logger.debug(message)

    def tier_3_runtime_trace(self, operation, details):
        """Tier 3: Runtime tracing - log interceptors, simulation overlays, cross-module data tests"""
        if self.debug_tier >= self.TIER_3:
            message = f"🔬 RUNTIME: {operation} | {details}"
            print(message)
            if hasattr(self, 'logger') and self.logger:
                self.logger.debug(message)

    def tier_3_simulation_overlay(self, simulation, details):
        """Tier 3: Simulation overlays"""
        if self.debug_tier >= self.TIER_3:
            message = f"🎭 SIMULATE: {simulation} | {details}"
            print(message)
            if hasattr(self, 'logger') and self.logger:
                self.logger.debug(message)

    def tier_3_cross_module_test(self, module, result):
        """Tier 3: Cross-module data tests"""
        if self.debug_tier >= self.TIER_3:
            message = f"🔗 CROSS-MODULE: {module} | {result}"
            print(message)
            if hasattr(self, 'logger') and self.logger:
                self.logger.debug(message)

    def tier_3_log_interceptor(self, source, details):
        """Tier 3: Log interceptors"""
        if self.debug_tier >= self.TIER_3:
            message = f"📡 INTERCEPT: {source} | {details}"
            print(message)
            if hasattr(self, 'logger') and self.logger:
                self.logger.debug(message)

    def tier_4_experimental(self, feature, status):
        """Tier 4: Experimental tools - unstable prototypes, internal module dev menus"""
        if self.debug_tier >= self.TIER_4:
            message = f"🧪 EXPERIMENTAL: {feature} | {status}"
            print(message)
            if hasattr(self, 'logger') and self.logger:
                self.logger.debug(message)

    def tier_4_prototype(self, prototype, details):
        """Tier 4: Unstable prototypes"""
        if self.debug_tier >= self.TIER_4:
            message = f"🚧 PROTOTYPE: {prototype} | {details}"
            print(message)
            if hasattr(self, 'logger') and self.logger:
                self.logger.debug(message)

    def tier_4_internal_dev_menu(self, menu, action):
        """Tier 4: Internal module dev menus"""
        if self.debug_tier >= self.TIER_4:
            message = f"🛠️ DEV-MENU: {menu} | {action}"
            print(message)
            if hasattr(self, 'logger') and self.logger:
                self.logger.debug(message)

    def tier_4_unstable_feature(self, feature, details):
        """Tier 4: Unstable features"""
        if self.debug_tier >= self.TIER_4:
            message = f"⚠️ UNSTABLE: {feature} | {details}"
            print(message)
            if hasattr(self, 'logger') and self.logger:
                self.logger.debug(message)

class PerformanceTimer:
    """Context manager for timing operations"""

    def __init__(self, tracer, operation_name):
        self.tracer = tracer
        self.operation_name = operation_name
        self.start_time = None

    def __enter__(self):
        self.start_time = time.time()
        self.tracer.debug(f"⏱️ TIMER START: {self.operation_name}")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        self.tracer.debug(f"⏱️ TIMER END: {self.operation_name} - {duration:.3f}s")

# Initialize global debug tracer
debug_tracer = DebugTracer()

class EconomyChangeLogger:
    """CLI-Compatible Economy Change Logger (Separate from Debug System)"""

    def __init__(self):
        self.log_file = None
        self.show_log_on_exit = True  # Default setting
        self.initialize_economy_logging()

    def initialize_economy_logging(self):
        """Initialize economy change logging (CLI-compatible)"""
        # Create logs directory if it doesn't exist
        log_dir = "logs"
        os.makedirs(log_dir, exist_ok=True)

        # Get timestamp for log filename (same format as CLI)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create log filename (same format as CLI)
        self.log_file = os.path.join(log_dir, f"economyovveride_{timestamp}.log")

        # Create the log file if it doesn't exist
        if not os.path.exists(self.log_file):
            with open(self.log_file, "w") as f:
                pass

        # Setup economy logger (separate from debug logger)
        self.economy_logger = logging.getLogger('EconomyChanges')
        self.economy_logger.setLevel(logging.INFO)

        # Remove any existing handlers
        for handler in self.economy_logger.handlers[:]:
            self.economy_logger.removeHandler(handler)

        # Add file handler for economy changes
        file_handler = logging.FileHandler(self.log_file, mode='a')
        file_handler.setLevel(logging.INFO)

        # Use same format as CLI
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)

        self.economy_logger.addHandler(file_handler)

        # Don't propagate to root logger (keep separate from debug)
        self.economy_logger.propagate = False

        debug_tracer.tier_2_config_change("economy_logger", f"Initialized: {self.log_file}")

    def log_function_call(self, function_name, parameters=None):
        """Log function calls (CLI-compatible)"""
        if parameters:
            self.economy_logger.info(f"Function '{function_name}' called with parameters: {parameters}")
        else:
            self.economy_logger.info(f"Function '{function_name}' called")

    def log_edit(self, edit_message):
        """Log economy edits (CLI-compatible)"""
        self.economy_logger.info(edit_message)

    def log_file_operation(self, operation, filename):
        """Log file operations (CLI-compatible)"""
        self.economy_logger.info(f"{operation}: {filename}")

    def log_global_change(self, field, old_value, new_value, percentage=None):
        """Log global changes (CLI-compatible)"""
        if percentage:
            message = f"Globally edited '{field}' by {percentage}%"
        else:
            message = f"Globally edited '{field}' from '{old_value}' to '{new_value}'"
        self.economy_logger.info(message)

    def log_batch_edit(self, operation_type, items_affected, details=None):
        """Log batch operations (CLI-compatible)"""
        message = f"Batch {operation_type}: {items_affected} items affected"
        if details:
            message += f" - {details}"
        self.economy_logger.info(message)

    def log_item_edit(self, outpost, trader, item_code, field, old_value, new_value):
        """Log individual item edits (CLI-compatible)"""
        edit_msg = f"{outpost} - {trader}: Updated '{item_code}' {field} from {old_value} to {new_value}"
        self.economy_logger.info(edit_msg)

    def log_session_end(self, save_filename=None):
        """Log session end and optionally show log"""
        if save_filename:
            self.economy_logger.info(f"Changes saved to '{save_filename}'")
        self.economy_logger.info("Session ended.")

        # Show log on exit if setting is enabled
        if self.show_log_on_exit:
            self.show_log_file()

    def show_log_file(self):
        """Show log file (CLI-compatible behavior)"""
        if self.log_file and os.path.exists(self.log_file):
            try:
                if platform.system() == "Windows":
                    subprocess.Popen(["notepad.exe", self.log_file], shell=True)
                else:
                    # For Unix/Linux/Mac, try common text editors
                    editors = ["gedit", "kate", "nano", "vim"]
                    for editor in editors:
                        try:
                            subprocess.Popen([editor, self.log_file])
                            break
                        except FileNotFoundError:
                            continue
                debug_tracer.tier_1_ui_log("Log Display", f"Opened economy log: {self.log_file}")
            except Exception as e:
                debug_tracer.error(f"Error opening economy log file: {str(e)}")
        else:
            debug_tracer.error("No economy log file found")

    def get_log_file_path(self):
        """Get current log file path"""
        return self.log_file

    def set_show_log_on_exit(self, show_log):
        """Set the show log on exit setting"""
        self.show_log_on_exit = show_log
        debug_tracer.tier_2_config_change("show_log_on_exit", show_log)

# Initialize global economy logger
economy_logger = EconomyChangeLogger()

class UndoRedoManager:
    """Manages undo/redo operations for the economy data"""
    def __init__(self, max_history=50):
        self.history = []
        self.current_index = -1
        self.max_history = max_history
    
    def save_state(self, data, description=""):
        """Save current state to history"""
        # Remove any future states if we're not at the end
        if self.current_index < len(self.history) - 1:
            self.history = self.history[:self.current_index + 1]
        
        # Add new state
        state = {
            'data': copy.deepcopy(data),
            'description': description,
            'timestamp': datetime.now().strftime("%H:%M:%S")
        }
        self.history.append(state)
        
        # Limit history size
        if len(self.history) > self.max_history:
            self.history.pop(0)
        else:
            self.current_index += 1
    
    def can_undo(self):
        """Check if undo is possible"""
        return self.current_index > 0 and len(self.history) > 1
    
    def can_redo(self):
        """Check if redo is possible"""
        return self.current_index < len(self.history) - 1
    
    def undo(self):
        """Undo last operation"""
        if self.can_undo():
            self.current_index -= 1
            return copy.deepcopy(self.history[self.current_index]['data'])
        return None
    
    def redo(self):
        """Redo next operation"""
        if self.can_redo():
            self.current_index += 1
            return copy.deepcopy(self.history[self.current_index]['data'])
        return None
    
    def get_current_description(self):
        """Get description of current state"""
        if 0 <= self.current_index < len(self.history):
            return self.history[self.current_index]['description']
        return ""
    
    def get_history_list(self):
        """Get list of all history entries"""
        return [(i, entry['description'], entry['timestamp'])
                for i, entry in enumerate(self.history)]

    def mass_undo(self, steps=5):
        """Undo multiple steps at once"""
        if self.can_undo():
            target_index = max(0, self.current_index - steps)
            self.current_index = target_index
            return copy.deepcopy(self.history[self.current_index]['data'])
        return None

    def intelligent_undo(self):
        """Intelligently undo the last logical operation"""
        if not self.can_undo():
            return None

        current_state = self.history[self.current_index]
        description = current_state['description']

        # If it's a batch operation, undo to before the batch started
        if any(keyword in description.lower() for keyword in ['batch', 'global', 'all', 'mass']):
            # Look for the state before this batch operation
            for i in range(self.current_index - 1, -1, -1):
                prev_description = self.history[i]['description']
                if not any(keyword in prev_description.lower() for keyword in ['batch', 'global', 'all', 'mass']):
                    self.current_index = i
                    return copy.deepcopy(self.history[self.current_index]['data'])

        # Default single undo
        return self.undo()

    def undo_to_description(self, target_description):
        """Undo back to a specific operation description"""
        for i in range(self.current_index - 1, -1, -1):
            if target_description.lower() in self.history[i]['description'].lower():
                self.current_index = i
                return copy.deepcopy(self.history[self.current_index]['data'])
        return None

    def remove_specific_change(self, target_index):
        """Remove a specific change from history (advanced operation)"""
        if 0 <= target_index < len(self.history) and target_index != self.current_index:
            # Mark as removed rather than actually removing to maintain indices
            self.history[target_index]['removed'] = True
            return True
        return False

    def get_undo_preview(self, steps=1):
        """Preview what would be undone"""
        if not self.can_undo():
            return None

        target_index = max(0, self.current_index - steps)
        undone_operations = []

        for i in range(self.current_index, target_index, -1):
            if i < len(self.history):
                undone_operations.append(self.history[i]['description'])

        return {
            'target_description': self.history[target_index]['description'] if target_index < len(self.history) else "Initial state",
            'undone_operations': undone_operations,
            'steps': len(undone_operations)
        }

class CategoryLogicManager:
    """Manages different categorization logic engines and custom buckets"""

    def __init__(self):
        self.current_engine = "fish_logic"  # Default engine
        self.custom_buckets = {}
        self.bucket_file = "custom_buckets.json"

        # Available categorization engines
        self.engines = {
            "fish_logic": FISHLogicEngine(),
            "simple_prefix": SimplePrefixEngine(),
            "smart_ai": SmartAIEngine(),
            "user_custom": UserCustomEngine()
        }

        # Load saved custom buckets
        self.load_custom_buckets()
        debug_tracer.tier_2_config_change("category_logic_manager", f"Initialized with {len(self.engines)} engines")

    def get_current_engine(self):
        """Get the currently active categorization engine"""
        return self.engines.get(self.current_engine, self.engines["fish_logic"])

    def switch_engine(self, engine_name):
        """Switch to a different categorization engine"""
        if engine_name in self.engines:
            self.current_engine = engine_name
            debug_tracer.tier_2_config_change("engine_switched", f"Changed to {engine_name}")
            return True
        return False

    def get_available_engines(self):
        """Get list of available categorization engines"""
        return {
            "fish_logic": "🐟 F.I.S.H. Logic - Advanced pattern matching",
            "simple_prefix": "📝 Simple Prefix - Basic prefix matching",
            "smart_ai": "🧠 Smart AI - Intelligent categorization",
            "user_custom": "👤 User Custom - Your saved buckets"
        }

    def save_custom_bucket(self, bucket_name, bucket_config):
        """Save a custom bucket configuration"""
        self.custom_buckets[bucket_name] = bucket_config
        self.save_buckets_to_file()
        debug_tracer.tier_2_config_change("bucket_saved", f"Saved bucket: {bucket_name}")

    def load_custom_buckets(self):
        """Load custom buckets from file"""
        try:
            if os.path.exists(self.bucket_file):
                with open(self.bucket_file, 'r') as f:
                    self.custom_buckets = json.load(f)
                debug_tracer.tier_2_config_change("buckets_loaded", f"Loaded {len(self.custom_buckets)} custom buckets")
        except Exception as e:
            debug_tracer.error(f"Failed to load custom buckets: {e}")
            self.custom_buckets = {}

    def save_buckets_to_file(self):
        """Save custom buckets to file"""
        try:
            with open(self.bucket_file, 'w') as f:
                json.dump(self.custom_buckets, f, indent=2)
            debug_tracer.tier_2_config_change("buckets_saved", f"Saved {len(self.custom_buckets)} buckets to file")
        except Exception as e:
            debug_tracer.error(f"Failed to save custom buckets: {e}")

    def delete_custom_bucket(self, bucket_name):
        """Delete a custom bucket"""
        if bucket_name in self.custom_buckets:
            del self.custom_buckets[bucket_name]
            self.save_buckets_to_file()
            debug_tracer.tier_2_config_change("bucket_deleted", f"Deleted bucket: {bucket_name}")
            return True
        return False

    def get_custom_buckets(self):
        """Get all custom buckets"""
        return self.custom_buckets

class FISHLogicEngine:
    """F.I.S.H. Logic - Filtered, Indexed, Scored, Hierarchical"""

    def __init__(self):
        self.rules = self.load_default_rules()
        self.filters = self.load_default_filters()
        self.scoring_weights = self.load_scoring_weights()
        self.hierarchy_rules = self.load_hierarchy_rules()
        self.debug_mode = os.getenv('XCHOOSE_DEBUG', '0') == '1'

    def load_default_filters(self):
        """Load F.I.S.H. filtering rules - removes inappropriate files"""
        return {
            'enabled_check': lambda item: item.get('enabled', True) != False,
            'version_check': lambda item: item.get('version', 1) >= 1,
            'valid_price': lambda item: self._is_valid_price(item.get('base-purchase-price', '0')),
            'not_test': lambda item: 'test' not in item.get('tradeable-code', '').lower(),
            'not_deprecated': lambda item: 'deprecated' not in item.get('tradeable-code', '').lower()
        }

    def load_default_rules(self):
        """Load F.I.S.H. categorization rules based on REAL economy file patterns"""
        return {
            # === WEAPONS - Based on actual Weapon_ patterns ===
            'Weapons_Firearms': {
                'condition': lambda code: (
                    code.startswith('Weapon_') and
                    not any(x in code for x in ['Parts', 'Charm', 'Attachment']) and
                    any(x in code for x in ['AK', 'M16', 'M4', 'MP5', 'M9', 'M1911', 'DEagle', 'Block21', 'Viper', 'Judge'])
                ),
                'priority': 'critical',
                'tags': ['weapon', 'firearm', 'combat'],
                'audience': 'pvp',
                'description': 'Firearms and guns'
            },
            'Weapons_Two_Handed': {
                'condition': lambda code: (
                    code.startswith('2H_') or
                    any(x in code for x in ['2H_Axe', '2H_Baseball_Bat', '2H_Katana', 'Sledgehammer'])
                ),
                'priority': 'high',
                'tags': ['weapon', 'melee', 'two-handed'],
                'audience': 'pvp',
                'description': 'Two-handed melee weapons'
            },
            'Weapons_One_Handed': {
                'condition': lambda code: (
                    code.startswith('1H_') or
                    any(x in code for x in ['1H_ImprovisedKnife', '1H_Shuriken', '1H_Kunai', '1H_Bushman'])
                ),
                'priority': 'high',
                'tags': ['weapon', 'melee', 'one-handed'],
                'audience': 'pvp',
                'description': 'One-handed melee weapons'
            },
            'Weapons_Melee_Other': {
                'condition': lambda code: (
                    any(x in code for x in ['Knife', 'Axe', 'Bat', 'Katana', 'Sledgehammer', 'Bushman', 'Kunai', 'Shuriken']) and
                    not code.startswith('1H_') and not code.startswith('2H_') and not code.startswith('Weapon_')
                ),
                'priority': 'normal',
                'tags': ['weapon', 'melee', 'close-combat'],
                'audience': 'pvp',
                'description': 'Other melee weapons'
            },
            'Weapons_Improvised': {
                'condition': lambda code: (
                    ('Improvised' in code and any(x in code for x in ['Weapon', 'Knife', 'Handgun'])) or
                    code.startswith('Weapon_Improvised')
                ),
                'priority': 'normal',
                'tags': ['weapon', 'improvised', 'makeshift'],
                'audience': 'survival',
                'description': 'Improvised weapons'
            },
            'Weapon_Parts': {
                'condition': lambda code: (
                    code.startswith('Weapon_Parts_') or 'Weapon_Parts' in code
                ),
                'priority': 'high',
                'tags': ['weapon', 'parts', 'crafting'],
                'audience': 'crafting',
                'description': 'Weapon parts and components'
            },

            # === AMMUNITION - Based on actual Cal_ patterns ===
            'Ammunition': {
                'condition': lambda code: (
                    code.startswith('Cal_') or
                    any(x in code for x in ['_50_AE', '_45', '_44_Viper', '_357_Viper', '_9mm', '_7_62x39mm', '_5_45x39mm'])
                ),
                'priority': 'critical',
                'tags': ['ammunition', 'combat', 'consumable'],
                'audience': 'pvp',
                'description': 'Ammunition and bullets'
            },
            'Magazines': {
                'condition': lambda code: (
                    code.startswith('Magazine_') or 'Magazine' in code
                ),
                'priority': 'high',
                'tags': ['weapon', 'magazine', 'accessory'],
                'audience': 'pvp',
                'description': 'Weapon magazines'
            },

            # === WEAPON ACCESSORIES - Based on actual patterns ===
            'Weapon_Scopes': {
                'condition': lambda code: (
                    any(x in code for x in ['WeaponScope_', 'WeaponSights_', 'ScopeRail_']) or
                    any(x in code for x in ['Scope', 'Holographic', 'RedDot', 'ImprovisedScope'])
                ),
                'priority': 'high',
                'tags': ['weapon', 'scope', 'accessory'],
                'audience': 'pvp',
                'description': 'Weapon scopes and sights'
            },
            'Weapon_Suppressors': {
                'condition': lambda code: (
                    code.startswith('WeaponSuppressor_') or 'Suppressor' in code
                ),
                'priority': 'high',
                'tags': ['weapon', 'suppressor', 'accessory'],
                'audience': 'stealth',
                'description': 'Weapon suppressors'
            },
            'Weapon_Charms': {
                'condition': lambda code: (
                    code.startswith('WeaponCharm_') or 'WeaponCharm' in code
                ),
                'priority': 'normal',
                'tags': ['weapon', 'charm', 'cosmetic'],
                'audience': 'cosmetic',
                'description': 'Weapon charms and decorations'
            },

            # === MILITARY & TACTICAL GEAR - Based on actual patterns ===
            'Military_Clothing': {
                'condition': lambda code: (
                    any(x in code for x in ['Military_Shirt', 'MilitaryPants', 'Military_Backpack', 'CombatBoots', 'Tactical_Gloves'])
                ),
                'priority': 'high',
                'tags': ['military', 'clothing', 'equipment'],
                'audience': 'tactical',
                'description': 'Military clothing and gear'
            },
            'Military_Equipment': {
                'condition': lambda code: (
                    any(x in code for x in ['Night_Vision', 'Parachute', 'Flashbang', 'Frag_Grenade', 'Smoke_Grenade']) and
                    not code.startswith('Weapon_')
                ),
                'priority': 'critical',
                'tags': ['military', 'equipment', 'tactical'],
                'audience': 'tactical',
                'description': 'Military equipment and gadgets'
            },

            # === CRAFTED ITEMS - Player-made items ===
            'Crafted_Items': {
                'condition': lambda code: (
                    code.startswith('Crafted_') or 'Crafted' in code
                ),
                'priority': 'high',
                'tags': ['crafted', 'player-made', 'survival'],
                'audience': 'crafting',
                'description': 'Player-crafted items and equipment'
            },

            # === IMPROVISED ITEMS - Based on actual economy patterns ===
            'Improvised_Weapons': {
                'condition': lambda code: (
                    code.startswith('Weapon_Improvised_') or
                    code.startswith('1H_Improvised') or
                    code.startswith('2H_Improvised') or
                    'Weapon_Improvised' in code
                ),
                'priority': 'high',
                'tags': ['improvised', 'weapon', 'makeshift'],
                'audience': 'survival',
                'description': 'Improvised weapons (Weapon_Improvised_, 1H_Improvised, etc.)'
            },
            'Improvised_Weapon_Parts': {
                'condition': lambda code: (
                    code.startswith('Weapon_Parts_Impro') or
                    'Parts_Improvised' in code or
                    ('Weapon_Parts' in code and 'Impro' in code)
                ),
                'priority': 'normal',
                'tags': ['improvised', 'weapon-parts', 'makeshift'],
                'audience': 'survival',
                'description': 'Improvised weapon parts (Weapon_Parts_Impro_Pistol, etc.)'
            },
            'Improvised_Equipment': {
                'condition': lambda code: (
                    'Improvised' in code and
                    not code.startswith('Weapon_') and
                    not code.startswith('1H_') and
                    not code.startswith('2H_') and
                    any(x in code for x in ['Backpack', 'Tool', 'Equipment', 'Scope', 'Knife'])
                ),
                'priority': 'normal',
                'tags': ['improvised', 'equipment', 'makeshift'],
                'audience': 'survival',
                'description': 'Improvised equipment and tools'
            },
            'Improvised_Other': {
                'condition': lambda code: (
                    'Improvised' in code and
                    not code.startswith('Weapon_') and
                    not code.startswith('1H_') and
                    not code.startswith('2H_') and
                    not any(x in code for x in ['Backpack', 'Tool', 'Equipment', 'Scope', 'Knife', 'Parts'])
                ),
                'priority': 'normal',
                'tags': ['improvised', 'makeshift', 'survival'],
                'audience': 'survival',
                'description': 'Other improvised items'
            },

            # === FISH - The original F.I.S.H. discovery! ===
            'Fish_Fresh': {
                'condition': lambda code: (
                    code.startswith('Fish_') and
                    not any(x in code.lower() for x in ['fillet', 'cooked', 'canned'])
                ),
                'priority': 'normal',
                'tags': ['food', 'fish', 'fresh'],
                'audience': 'survival',
                'description': 'Fresh fish for cooking'
            },
            'Fish_Processed': {
                'condition': lambda code: (
                    any(x in code for x in ['fish_Fillet', 'Catfish_Fillet', 'Monkfish_Fillet', 'Scorpionfish_Fillet'])
                ),
                'priority': 'normal',
                'tags': ['food', 'fish', 'processed'],
                'audience': 'survival',
                'description': 'Processed fish fillets'
            }
        }

    def load_scoring_weights(self):
        """Load scoring weights for F.I.S.H. selection algorithm"""
        return {
            'priority_weights': {'critical': 10, 'high': 5, 'normal': 1},
            'tag_weights': {'weapon': 3, 'medical': 2, 'food': 1, 'tool': 1},
            'audience_weights': {'pvp': 2, 'pve': 1},
            'freshness_factor': 0.2,
            'random_bias_range': (0, 1)
        }

    def load_hierarchy_rules(self):
        """Load hierarchical override rules"""
        return {
            'global_rules': [
                'never_deploy_deprecated',
                'admin_overrides_always_win',
                'critical_items_require_approval'
            ],
            'folder_rules': [
                'prefer_pvp_after_18_00',
                'seasonal_configs_by_date',
                'event_configs_on_weekends'
            ],
            'file_rules': [
                'respect_expiration_dates',
                'honor_specific_timing',
                'check_version_compatibility'
            ]
        }

    def _is_valid_price(self, price_str):
        """Check if price string is valid"""
        try:
            price = float(price_str)
            return price >= 0 and price != -1
        except (ValueError, TypeError):
            return False

    def filter_items(self, items):
        """Apply F.I.S.H. filtering - remove inappropriate items"""
        filtered_items = []
        filter_log = []

        for item in items:
            passed_all_filters = True
            for filter_name, filter_func in self.filters.items():
                try:
                    if not filter_func(item):
                        passed_all_filters = False
                        if self.debug_mode:
                            code = item.get('tradeable-code', 'Unknown')
                            filter_log.append(f"[FISH] Filtered out: {code} ({filter_name})")
                        break
                except Exception as e:
                    if self.debug_mode:
                        filter_log.append(f"[FISH] Filter error on {item.get('tradeable-code', 'Unknown')}: {e}")
                    passed_all_filters = False
                    break

            if passed_all_filters:
                filtered_items.append(item)

        if self.debug_mode:
            for log_entry in filter_log:
                print(log_entry)

        return filtered_items

    def index_items(self, items):
        """Apply F.I.S.H. indexing - tag items with metadata"""
        indexed_items = []

        for item in items:
            code = item.get('tradeable-code', '')
            indexed_item = item.copy()

            # Find matching category
            category_match = self.categorize_item(code)

            # Add F.I.S.H. metadata
            indexed_item['fish_metadata'] = {
                'category': category_match['category'],
                'priority': category_match['priority'],
                'tags': category_match.get('tags', []),
                'audience': category_match.get('audience', 'general'),
                'description': category_match['description']
            }

            indexed_items.append(indexed_item)

        if self.debug_mode:
            print(f"[FISH] Indexed {len(indexed_items)} items with metadata")

        return indexed_items

    def score_items(self, items, context=None):
        """Apply F.I.S.H. scoring - weight items based on context"""
        import random

        scored_items = []
        context = context or {}

        for item in items:
            metadata = item.get('fish_metadata', {})

            # Base score from priority
            priority = metadata.get('priority', 'normal')
            score = self.scoring_weights['priority_weights'].get(priority, 1)

            # Tag-based scoring
            tags = metadata.get('tags', [])
            for tag in tags:
                score += self.scoring_weights['tag_weights'].get(tag, 0)

            # Audience-based scoring
            audience = metadata.get('audience', 'general')
            score += self.scoring_weights['audience_weights'].get(audience, 1)

            # Add random bias for selection variety
            random_bias = random.uniform(*self.scoring_weights['random_bias_range'])
            score += random_bias

            # Context-based adjustments
            if context.get('prefer_pvp') and audience == 'pvp':
                score *= 1.5
            if context.get('prefer_medical') and 'medical' in tags:
                score *= 1.3

            scored_item = item.copy()
            scored_item['fish_score'] = score
            scored_items.append(scored_item)

        # Sort by score (highest first)
        scored_items.sort(key=lambda x: x.get('fish_score', 0), reverse=True)

        if self.debug_mode:
            for item in scored_items[:5]:  # Show top 5
                code = item.get('tradeable-code', 'Unknown')
                score = item.get('fish_score', 0)
                print(f"[FISH] Score: {code} = {score:.2f}")

        return scored_items

    def apply_hierarchy(self, items, rules_context=None):
        """Apply F.I.S.H. hierarchical rules"""
        rules_context = rules_context or {}

        # Apply global rules first
        filtered_items = []
        for item in items:
            code = item.get('tradeable-code', '')

            # Global rule: Never deploy deprecated
            if 'deprecated' in code.lower():
                if self.debug_mode:
                    print(f"[FISH] Hierarchy: Blocked deprecated item {code}")
                continue

            # Global rule: Admin overrides
            if rules_context.get('admin_override'):
                item['fish_admin_override'] = True
                if self.debug_mode:
                    print(f"[FISH] Hierarchy: Admin override applied to {code}")

            filtered_items.append(item)

        return filtered_items
    
    def categorize_item(self, item_code):
        """Categorize an item using F.I.S.H. logic"""
        matches = []
        for category, rule in self.rules.items():
            if rule['condition'](item_code):
                matches.append({
                    'category': category,
                    'priority': rule['priority'],
                    'description': rule['description']
                })
        
        # Sort by priority (critical > high > normal)
        priority_order = {'critical': 3, 'high': 2, 'normal': 1}
        matches.sort(key=lambda x: priority_order.get(x['priority'], 0), reverse=True)
        
        return matches[0] if matches else {
            'category': 'Uncategorized',
            'priority': 'normal',
            'description': 'Items that don\'t match any category'
        }
    
    def analyze_economy(self, data):
        """Analyze entire economy using F.I.S.H. logic"""
        analysis = {
            'categories': defaultdict(list),
            'priority_distribution': defaultdict(int),
            'total_items': 0
        }
        
        traders = data.get("economy-override", {}).get("traders", {})
        for trader_name, trader_items in traders.items():
            for item in trader_items:
                if isinstance(item, dict):
                    code = item.get("tradeable-code", "")
                    if code:
                        result = self.categorize_item(code)
                        analysis['categories'][result['category']].append({
                            'code': code,
                            'trader': trader_name,
                            'item': item
                        })
                        analysis['priority_distribution'][result['priority']] += 1
                        analysis['total_items'] += 1
        
        return analysis

class SimplePrefixEngine:
    """Simple prefix-based categorization engine"""

    def __init__(self):
        self.prefixes = {
            'Weapon_': 'Firearms',
            '2H_': 'Two-Handed Weapons',
            '1H_': 'One-Handed Weapons',
            'Cal_': 'Ammunition',
            'Fish_': 'Fish',
            'Military_': 'Military Gear',
            'Police_': 'Police Equipment',
            'Crafted_': 'Crafted Items',
            'Weapon_Improvised_': 'Improvised Weapons',
            '1H_Improvised': 'Improvised One-Handed',
            '2H_Improvised': 'Improvised Two-Handed',
            'Weapon_Parts_Impro': 'Improvised Weapon Parts',
            'Magazine_': 'Magazines',
            'WeaponScope_': 'Weapon Scopes',
            'WeaponSights_': 'Weapon Sights',
            'WeaponSuppressor_': 'Suppressors',
            'WeaponCharm_': 'Weapon Charms',
            'Weapon_Parts_': 'Weapon Parts',
            'Tactical_': 'Tactical Gear',
            'Night_Vision': 'Night Vision',
            'Combat': 'Combat Equipment'
        }

    def categorize_item(self, item_code):
        """Simple prefix matching"""
        for prefix, category in self.prefixes.items():
            if item_code.startswith(prefix):
                return {
                    'category': category,
                    'priority': 'normal',
                    'description': f'Items starting with {prefix}'
                }
        return {
            'category': 'Other',
            'priority': 'normal',
            'description': 'Items not matching any prefix'
        }

class SmartAIEngine:
    """Smart AI-powered categorization engine"""

    def __init__(self):
        self.patterns = self.load_smart_patterns()

    def load_smart_patterns(self):
        """Load intelligent pattern matching rules"""
        return {
            'combat_weapons': {
                'patterns': ['AK', 'M16', 'M4', 'MP5', 'rifle', 'pistol'],
                'exclusions': ['parts', 'attachment'],
                'category': 'Combat Weapons'
            },
            'survival_tools': {
                'patterns': ['knife', 'axe', 'hammer', 'tool'],
                'exclusions': ['weapon_'],
                'category': 'Survival Tools'
            },
            'consumables': {
                'patterns': ['food', 'can_', 'meat', 'fish_', 'bandage'],
                'exclusions': ['weapon', 'tool'],
                'category': 'Consumables'
            }
        }

    def categorize_item(self, item_code):
        """AI-powered categorization"""
        code_lower = item_code.lower()

        for pattern_name, pattern_data in self.patterns.items():
            # Check if any pattern matches
            pattern_match = any(p.lower() in code_lower for p in pattern_data['patterns'])
            # Check if any exclusion matches
            exclusion_match = any(e.lower() in code_lower for e in pattern_data['exclusions'])

            if pattern_match and not exclusion_match:
                return {
                    'category': pattern_data['category'],
                    'priority': 'normal',
                    'description': f'AI categorized based on pattern: {pattern_name}'
                }

        return {
            'category': 'Uncategorized',
            'priority': 'normal',
            'description': 'AI could not categorize this item'
        }

class UserCustomEngine:
    """User-defined custom bucket engine"""

    def __init__(self):
        self.custom_rules = {}

    def load_custom_rules(self, custom_buckets):
        """Load user-defined custom bucket rules"""
        self.custom_rules = custom_buckets

    def categorize_item(self, item_code):
        """Categorize using user-defined rules"""
        for bucket_name, bucket_config in self.custom_rules.items():
            if self.item_matches_bucket(item_code, bucket_config):
                return {
                    'category': bucket_config.get('name', bucket_name),
                    'priority': 'normal',
                    'description': bucket_config.get('description', 'User-defined category')
                }

        return {
            'category': 'Uncategorized',
            'priority': 'normal',
            'description': 'Not in any custom bucket'
        }

    def item_matches_bucket(self, item_code, bucket_config):
        """Check if item matches bucket filters"""
        filters = bucket_config.get('filters', [])
        code_lower = item_code.lower()

        for filter_rule in filters:
            filter_type = filter_rule.get('type', '')
            filter_value = filter_rule.get('value', '')

            if filter_type == 'starts_with':
                if not item_code.startswith(filter_value):
                    return False
            elif filter_type == 'contains':
                if filter_value.lower() not in code_lower:
                    return False
            elif filter_type == 'not_contains':
                if filter_value.lower() in code_lower:
                    return False
            elif filter_type == 'contains_any':
                if not any(v.lower() in code_lower for v in filter_value):
                    return False

        return True

class EditFrequencyTracker:
    """Tracks edit frequency for heat map visualization"""

    def __init__(self):
        self.edit_counts = {}  # item_key -> count
        self.edit_history = {}  # item_key -> [timestamps]
        self.session_start = datetime.now()
        self.heat_levels = {
            'cold': (0, 2),      # 0-2 edits: cold (blue)
            'warm': (3, 5),      # 3-5 edits: warm (yellow)
            'hot': (6, 10),      # 6-10 edits: hot (orange)
            'burning': (11, float('inf'))  # 11+ edits: burning (red)
        }

    def track_edit(self, outpost, trader, item_code, edit_type="general"):
        """Track an edit to an item"""
        item_key = f"{outpost}::{trader}::{item_code}"

        # Increment count
        if item_key not in self.edit_counts:
            self.edit_counts[item_key] = 0
            self.edit_history[item_key] = []

        self.edit_counts[item_key] += 1
        self.edit_history[item_key].append({
            'timestamp': datetime.now(),
            'edit_type': edit_type
        })

    def get_heat_level(self, item_key):
        """Get heat level for an item"""
        count = self.edit_counts.get(item_key, 0)

        for level, (min_count, max_count) in self.heat_levels.items():
            if min_count <= count <= max_count:
                return level

        return 'cold'

    def get_heat_color(self, heat_level):
        """Get color for heat level"""
        colors = {
            'cold': '#0066cc',      # Blue
            'warm': '#ffcc00',      # Yellow
            'hot': '#ff6600',       # Orange
            'burning': '#cc0000'    # Red
        }
        return colors.get(heat_level, '#0066cc')

    def get_top_edited_items(self, limit=50):
        """Get most edited items"""
        sorted_items = sorted(self.edit_counts.items(), key=lambda x: x[1], reverse=True)
        return sorted_items[:limit]

    def get_edit_statistics(self):
        """Get overall edit statistics"""
        total_edits = sum(self.edit_counts.values())
        unique_items = len(self.edit_counts)

        heat_distribution = {'cold': 0, 'warm': 0, 'hot': 0, 'burning': 0}
        for item_key in self.edit_counts:
            heat_level = self.get_heat_level(item_key)
            heat_distribution[heat_level] += 1

        return {
            'total_edits': total_edits,
            'unique_items': unique_items,
            'session_duration': datetime.now() - self.session_start,
            'heat_distribution': heat_distribution,
            'average_edits_per_item': total_edits / unique_items if unique_items > 0 else 0
        }

    def reset_tracking(self):
        """Reset all tracking data"""
        self.edit_counts.clear()
        self.edit_history.clear()
        self.session_start = datetime.now()

class ValidationEngine:
    """CLI-compatible validation engine to prevent file corruption"""

    def __init__(self):
        self.validation_rules = self.load_validation_rules()

    def load_validation_rules(self):
        """Load validation rules based on CLI logic from 1_45c.py"""
        return {
            'percentage_range': {
                'min': -99,  # CLI: "percentage range stops at -99 to avoid 0 values"
                'max': 100,
                'description': "Percentage must be between -99 and 100 to prevent zero values"
            },
            'price_fields': {
                'valid_fields': ['base-purchase-price', 'base-sell-price', 'delta-price'],
                'min_value': -1,  # CLI: values can be -1 (default), but not less
                'description': "Price values must be -1 (default) or positive numbers"
            },
            'fame_points': {
                'default_value': -1,
                'min_value': -1,
                'description': "Fame points: -1 for default, or positive numbers"
            },
            'can_be_purchased': {
                'valid_values': ['default', 'true', 'false'],
                'description': "Can-be-purchased must be 'default', 'true', or 'false'"
            },
            'economy_fields': {
                'required_fields': [
                    'economy-reset-time-hours',
                    'prices-randomization-time-hours',
                    'tradeable-rotation-time-ingame-hours-min',
                    'tradeable-rotation-time-ingame-hours-max',
                    'fully-restock-tradeable-hours',
                    'trader-funds-change-rate-per-hour-multiplier'
                ],
                'description': "Economy fields must maintain proper structure"
            }
        }

    def validate_percentage(self, percentage_str, mode="normal"):
        """Validate percentage input with mode-aware limits"""
        try:
            percentage = float(percentage_str)

            # Mode-specific validation
            if mode == "advanced":
                # Advanced mode: Allow above 100% but keep -99% minimum
                min_val = self.validation_rules['percentage_range']['min']  # -99
                if percentage < min_val:
                    return False, f"Invalid percentage. Must be {min_val}% or higher (Advanced mode allows unlimited increases)."
            else:
                # Normal mode: CLI-compatible limits (-99% to +100%)
                min_val = self.validation_rules['percentage_range']['min']  # -99
                max_val = self.validation_rules['percentage_range']['max']  # 100
                if percentage < min_val or percentage > max_val:
                    return False, f"Invalid percentage. Must be between {min_val}% and {max_val}% (CLI-compatible range)."

            return True, percentage
        except ValueError:
            return False, "Invalid input. Please enter a valid number."

    def validate_price_value(self, value_str):
        """Validate price field values (CLI logic from line 1588-1591)"""
        try:
            if value_str.strip() == "":
                return False, "Value cannot be empty."

            value = float(value_str)
            if value < self.validation_rules['price_fields']['min_value']:
                return False, f"Price value must be -1 (default) or positive. Cannot be less than {self.validation_rules['price_fields']['min_value']}."
            return True, value
        except ValueError:
            return False, "Invalid input. Please enter a valid number."

    def validate_fame_points(self, value_str):
        """Validate fame points (CLI logic)"""
        try:
            if value_str.strip() == "":
                return True, -1  # Default value

            value = int(value_str)
            if value < self.validation_rules['fame_points']['min_value']:
                return False, f"Fame points must be -1 (default) or positive numbers."
            return True, value
        except ValueError:
            return False, "Invalid input. Please enter a valid number."

    def validate_can_be_purchased(self, value_str):
        """Validate can-be-purchased field (CLI logic from line 1583-1586)"""
        value_lower = value_str.lower().strip()
        if value_lower not in self.validation_rules['can_be_purchased']['valid_values']:
            return False, f"Invalid input. Please enter 'default', 'true', or 'false'."
        return True, value_lower

    def validate_item_code_locked(self, old_code, new_code):
        """Ensure tradeable-code cannot be changed (CLI protection)"""
        if old_code != new_code:
            return False, "Tradeable-code is locked and cannot be modified to prevent item corruption."
        return True, new_code

    def show_validation_warning(self, parent, title, message, warning_type="warning"):
        """Show CLI-style validation warnings"""
        import tkinter.messagebox as messagebox

        if warning_type == "error":
            messagebox.showerror(title, f"⚠️ VALIDATION ERROR ⚠️\n\n{message}\n\nThis change has been blocked to protect your file integrity.")
        elif warning_type == "warning":
            return messagebox.askyesno(title, f"⚠️ VALIDATION WARNING ⚠️\n\n{message}\n\nDo you want to proceed anyway?")
        else:
            messagebox.showinfo(title, message)

    def get_safe_percentage_range_info(self, mode="normal"):
        """Get information about safe percentage ranges based on mode"""
        if mode == "advanced":
            return f"""
🛡️ SAFE PERCENTAGE RANGES (Advanced Mode)

📊 Price Adjustments:
• Minimum: {self.validation_rules['percentage_range']['min']}% (prevents zero prices)
• Maximum: ∞ (unlimited increases allowed in Advanced mode)
• Why: Advanced users can handle extreme changes responsibly
• Example: -50% reduces prices by half, +200% triples prices

⚠️ IMPORTANT PROTECTIONS:
• Values already set to -1 (default) will NOT be affected
• Values set to 'null' or 'none' will be SKIPPED
• Tradeable-codes are LOCKED to prevent corruption
• All changes are logged for safety

💡 ADVANCED TIPS:
• Large increases (>100%) can dramatically affect economy balance
• Consider economic impact before applying extreme changes
• Use F.I.S.H. Analysis to review changes
• Test extreme changes on backup files first
            """
        else:
            return f"""
🛡️ SAFE PERCENTAGE RANGES (CLI-Compatible)

📊 Price Adjustments:
• Range: {self.validation_rules['percentage_range']['min']}% to {self.validation_rules['percentage_range']['max']}%
• Why: Prevents prices from going to zero (which breaks the economy)
• Example: -50% reduces prices by half, +100% doubles prices

⚠️ IMPORTANT PROTECTIONS:
• Values already set to -1 (default) will NOT be affected
• Values set to 'null' or 'none' will be SKIPPED
• Tradeable-codes are LOCKED to prevent corruption
• All changes are logged for safety

💡 TIPS:
• Start with small changes (±10-20%)
• Test on a backup file first
• Use F.I.S.H. Analysis to review changes
• Check the Changes tab before applying
• Switch to Advanced Mode for unlimited increases
            """

class SCUMEconomyGUI:
    def __init__(self):
        # Initialize debug tracing for this instance
        debug_tracer.trace_enter("SCUMEconomyGUI.__init__")
        debug_tracer.operation_start("GUI Initialization", "XconomyChooser v2.01")

        # Create main window
        debug_tracer.debug("Creating main window...")
        self.root = ctk.CTk()
        self.root.title("🎮 XconomyChooser v2.01 - Enhanced GUI")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)

        # Initialize mode system
        debug_tracer.debug("Initializing mode system...")
        self.user_mode = "normal"  # "normal" or "advanced"
        self.mode_descriptions = {
            "normal": "🎯 Normal Mode - CLI-like interface with categories (Beginner-friendly)",
            "advanced": "⚡ Advanced Mode - Full feature set with F.I.S.H. Logic (Expert users)"
        }
        debug_tracer.trace_var("user_mode", self.user_mode)

        # Initialize core systems
        debug_tracer.debug("Initializing core systems...")
        self.current_data = None
        self.current_filename = None
        self.current_file_path = None
        self.categories = []
        self.smart_categories = {}

        # Initialize advanced systems
        self.undo_manager = UndoRedoManager()
        self.category_manager = CategoryLogicManager()  # Multiple categorization engines
        self.fish_engine = self.category_manager.get_current_engine()  # Direct access to F.I.S.H. engine
        self.validation_engine = ValidationEngine()  # CLI-compatible validation
        self.pending_changes = {}  # Store changes before applying

        # Initialize bucket system for normal mode
        self.custom_buckets = {}  # User-defined buckets
        self.bucket_file = "custom_buckets.json"
        self.load_custom_buckets()

        # Initialize heat map system
        self.edit_frequency_tracker = EditFrequencyTracker()
        self.heat_map_window = None

        # Initialize economy change logging (CLI parity)
        debug_tracer.debug("Initializing economy change logging...")
        self.economy_logger = economy_logger
        self.economy_logger.log_function_call("SCUMEconomyGUI.__init__")

        # Create GUI components
        debug_tracer.debug("Creating GUI components...")
        self.create_main_interface()

        # Auto-scan for JSON files on startup
        debug_tracer.debug("Auto-scanning for JSON files...")
        self.auto_scan_json_files()

        debug_tracer.operation_end("GUI Initialization", True, "All systems initialized successfully")
        debug_tracer.trace_exit("SCUMEconomyGUI.__init__")

    def load_custom_buckets(self):
        """Load custom buckets from file"""
        try:
            if os.path.exists(self.bucket_file):
                with open(self.bucket_file, 'r', encoding='utf-8') as f:
                    self.custom_buckets = json.load(f)
            else:
                # Initialize with default buckets
                self.custom_buckets = {
                    "fish": {
                        "name": "🐟 Fish Items",
                        "description": "All fish items for consumption",
                        "filters": [
                            {"type": "starts_with", "value": "Fish_"},
                            {"type": "not_contains", "value": "fishing"},
                            {"type": "not_contains", "value": "rod"}
                        ]
                    },
                    "weapons": {
                        "name": "🔫 Weapons",
                        "description": "All combat weapons",
                        "filters": [
                            {"type": "starts_with", "value": "Weapon_"},
                            {"type": "not_contains", "value": "parts"},
                            {"type": "not_contains", "value": "attachment"}
                        ]
                    },
                    "food": {
                        "name": "🥫 Food & Canned",
                        "description": "Food items and canned goods",
                        "filters": [
                            {"type": "contains_any", "value": ["food", "can_", "meat"]},
                            {"type": "not_starts_with", "value": "Weapon_"}
                        ]
                    },
                    "medical": {
                        "name": "💊 Medical Items",
                        "description": "Medical supplies and healing items",
                        "filters": [
                            {"type": "contains_any", "value": ["bandage", "pill", "syringe", "medical"]}
                        ]
                    },
                    "tools": {
                        "name": "🔧 Tools & Equipment",
                        "description": "Tools and utility equipment",
                        "filters": [
                            {"type": "contains_any", "value": ["tool", "hammer", "wrench", "screwdriver"]},
                            {"type": "not_starts_with", "value": "Weapon_"}
                        ]
                    },
                    "military": {
                        "name": "🎖️ Military Gear",
                        "description": "Military equipment (non-weapon)",
                        "filters": [
                            {"type": "contains_any", "value": ["Military", "Army", "Combat"]},
                            {"type": "not_starts_with", "value": "Weapon_"}
                        ]
                    }
                }
                self.save_custom_buckets()
        except Exception as e:
            print(f"Error loading custom buckets: {e}")
            self.custom_buckets = {}

    def save_custom_buckets(self):
        """Save custom buckets to file"""
        try:
            with open(self.bucket_file, 'w', encoding='utf-8') as f:
                json.dump(self.custom_buckets, f, indent=2)
        except Exception as e:
            print(f"Error saving custom buckets: {e}")

    def apply_bucket_filters(self, item_code, bucket_filters):
        """Apply bucket filters to determine if item matches"""
        item_code_lower = item_code.lower()

        for filter_rule in bucket_filters:
            filter_type = filter_rule["type"]
            filter_value = filter_rule["value"]

            if filter_type == "starts_with":
                if not item_code.startswith(filter_value):
                    return False
            elif filter_type == "not_starts_with":
                if item_code.startswith(filter_value):
                    return False
            elif filter_type == "contains":
                if filter_value.lower() not in item_code_lower:
                    return False
            elif filter_type == "not_contains":
                if filter_value.lower() in item_code_lower:
                    return False
            elif filter_type == "contains_any":
                if not any(val.lower() in item_code_lower for val in filter_value):
                    return False
            elif filter_type == "not_contains_any":
                if any(val.lower() in item_code_lower for val in filter_value):
                    return False

        return True

    def create_main_interface(self):
        """Create the main GUI interface"""
        # Create main container with grid layout
        self.main_container = ctk.CTkFrame(self.root)
        self.main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Configure grid weights
        self.main_container.grid_rowconfigure(2, weight=1)
        self.main_container.grid_columnconfigure(1, weight=1)
        
        # Create sections
        self.create_header()
        self.create_toolbar()
        self.create_main_content()
        self.create_status_bar()
    
    def create_header(self):
        """Create the application header"""
        header_frame = ctk.CTkFrame(self.main_container)
        header_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        
        # Title
        title_label = ctk.CTkLabel(
            header_frame,
            text="🎮 SCUM Economy Chooser - Enhanced GUI",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=10)
        
        # Version and author info
        info_frame = ctk.CTkFrame(header_frame)
        info_frame.pack(pady=5)
        
        version_label = ctk.CTkLabel(
            info_frame,
            text="XconomyChooser v2.01 - Dual-Mode Edition with F.I.S.H. Logic & Advanced Validation",
            font=ctk.CTkFont(size=12)
        )
        version_label.pack(side="left", padx=10)

        # Mode toggle section
        mode_frame = ctk.CTkFrame(info_frame)
        mode_frame.pack(side="left", padx=20)

        # Mode description
        self.mode_description_label = ctk.CTkLabel(
            mode_frame,
            text=self.mode_descriptions[self.user_mode],
            font=ctk.CTkFont(size=11),
            text_color="#ffc107"
        )
        self.mode_description_label.pack(pady=2)

        # Mode toggle button
        self.mode_toggle_btn = ctk.CTkButton(
            mode_frame,
            text="🔄 Switch to Advanced Mode",
            command=self.toggle_user_mode,
            width=180,
            height=25,
            fg_color="#6c757d",
            hover_color="#5a6268",
            font=ctk.CTkFont(size=11)
        )
        self.mode_toggle_btn.pack()

        # Quick action buttons
        button_frame = ctk.CTkFrame(info_frame)
        button_frame.pack(side="right", padx=10)
        
        cli_button = ctk.CTkButton(
            button_frame,
            text="⬛ CLI",
            command=self.launch_cli_terminal,
            width=80,
            height=30,
            fg_color="#1a1a1a",      # Dark terminal background
            hover_color="#2d2d2d",   # Slightly lighter on hover
            text_color="#00ff00",    # Classic green terminal text
            font=ctk.CTkFont(family="Courier New", size=11, weight="bold"),  # Monospace font
            border_width=1,
            border_color="#333333"   # Subtle border
        )
        cli_button.pack(side="left", padx=2)
        
        help_button = ctk.CTkButton(
            button_frame,
            text="❓ Help",
            command=self.show_help,
            width=80,
            height=30
        )
        help_button.pack(side="left", padx=2)

    def toggle_user_mode(self):
        """Toggle between normal and advanced user modes"""
        if self.user_mode == "normal":
            self.user_mode = "advanced"
            self.mode_toggle_btn.configure(text="🎯 Switch to Normal Mode")
            self.mode_description_label.configure(text=self.mode_descriptions["advanced"])
        else:
            self.user_mode = "normal"
            self.mode_toggle_btn.configure(text="🔄 Switch to Advanced Mode")
            self.mode_description_label.configure(text=self.mode_descriptions["normal"])

        # Refresh the tools panel to show/hide features based on mode
        self.refresh_tools_panel()

        # Update window title to show current mode
        mode_indicator = "🎯 Normal" if self.user_mode == "normal" else "⚡ Advanced"
        self.root.title(f"🎮 XconomyChooser v2.01 - Enhanced GUI [{mode_indicator}]")

    def refresh_tools_panel(self):
        """Refresh tools panel based on current user mode"""
        # Clear existing tools
        for widget in self.left_panel.winfo_children():
            if hasattr(widget, 'pack_info') and widget.pack_info():
                widget.destroy()

        # Recreate tools panel
        self.create_tools_panel()

    def create_toolbar(self):
        """Create toolbar with undo/redo and file operations"""
        toolbar_frame = ctk.CTkFrame(self.main_container)
        toolbar_frame.grid(row=1, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        
        # File operations
        file_frame = ctk.CTkFrame(toolbar_frame)
        file_frame.pack(side="left", padx=10, pady=5)
        
        # Current file display
        self.current_file_label = ctk.CTkLabel(
            file_frame,
            text="📁 No file loaded",
            font=ctk.CTkFont(size=14),
            text_color="orange"
        )
        self.current_file_label.pack(side="left", padx=10)
        
        # File action buttons
        file_buttons_frame = ctk.CTkFrame(file_frame)
        file_buttons_frame.pack(side="left", padx=10)
        
        load_btn = ctk.CTkButton(
            file_buttons_frame,
            text="📂 Load",
            command=self.load_json_file,
            width=80
        )
        load_btn.pack(side="left", padx=2)
        
        scan_btn = ctk.CTkButton(
            file_buttons_frame,
            text="🔍 Scan",
            command=self.scan_directory,
            width=80
        )
        scan_btn.pack(side="left", padx=2)
        
        reload_btn = ctk.CTkButton(
            file_buttons_frame,
            text="🔄 Reload",
            command=self.reload_file,
            width=80
        )
        reload_btn.pack(side="left", padx=2)
        
        # Undo/Redo operations
        undo_frame = ctk.CTkFrame(toolbar_frame)
        undo_frame.pack(side="right", padx=10, pady=5)
        
        self.undo_btn = ctk.CTkButton(
            undo_frame,
            text="↶ Undo",
            command=self.undo_operation,
            width=80,
            state="disabled"
        )
        self.undo_btn.pack(side="left", padx=2)
        
        self.redo_btn = ctk.CTkButton(
            undo_frame,
            text="↷ Redo",
            command=self.redo_operation,
            width=80,
            state="disabled"
        )
        self.redo_btn.pack(side="left", padx=2)
        
        history_btn = ctk.CTkButton(
            undo_frame,
            text="📜 History",
            command=self.show_history,
            width=80
        )
        history_btn.pack(side="left", padx=2)

    def create_main_content(self):
        """Create the main content area with tools and preview"""
        # Left panel - Tools
        self.left_panel = ctk.CTkFrame(self.main_container)
        self.left_panel.grid(row=2, column=0, sticky="nsew", padx=(5, 2), pady=5)

        # Right panel - Preview and details
        self.right_panel = ctk.CTkFrame(self.main_container)
        self.right_panel.grid(row=2, column=1, sticky="nsew", padx=(2, 5), pady=5)

        # Configure column weights
        self.main_container.grid_columnconfigure(0, weight=1)
        self.main_container.grid_columnconfigure(1, weight=2)

        self.create_tools_panel()
        self.create_preview_panel()

    def create_tools_panel(self):
        """Create the tools panel with mode-specific editing options"""
        # Tools title with mode indicator
        mode_indicator = "🎯 Normal" if self.user_mode == "normal" else "⚡ Advanced"
        tools_title = ctk.CTkLabel(
            self.left_panel,
            text=f"⚙️ Economy Editor Tools [{mode_indicator}]",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        tools_title.pack(pady=10)

        # Scrollable frame for tools
        tools_scroll = ctk.CTkScrollableFrame(self.left_panel)
        tools_scroll.pack(fill="both", expand=True, padx=10, pady=10)

        # Tool categories based on mode
        if self.user_mode == "normal":
            # Normal mode: CLI-style edits first, then essential categories, then buckets, then advanced editing
            self.create_cli_style_tools(tools_scroll)
            self.create_essential_categories_section(tools_scroll)
            self.create_custom_buckets_section(tools_scroll)
            self.create_advanced_editing_section(tools_scroll)
        else:
            # Advanced mode: Everything from Normal + F.I.S.H. Logic + Professional tools
            self.create_cli_style_tools(tools_scroll)
            self.create_essential_categories_section(tools_scroll)
            self.create_custom_buckets_section(tools_scroll, advanced_mode=True)
            self.create_advanced_editing_section(tools_scroll)
            self.create_fish_tools(tools_scroll)
            self.create_professional_tools(tools_scroll)

    def create_cli_style_tools(self, parent):
        """Create CLI-style tools available in both modes"""
        cli_frame = ctk.CTkFrame(parent)
        cli_frame.pack(fill="x", pady=5)

        cli_title = ctk.CTkLabel(
            cli_frame,
            text="🖥️ CLI-Style Operations",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        cli_title.pack(pady=5)

        # Global price changes (CLI equivalent)
        global_price_btn = ctk.CTkButton(
            cli_frame,
            text="💰 Global Price Changes",
            command=self.open_global_price_changes,
            height=40
        )
        global_price_btn.pack(fill="x", padx=10, pady=2)

        # Economy fields (CLI equivalent)
        economy_fields_btn = ctk.CTkButton(
            cli_frame,
            text="⚙️ Economy Settings",
            command=self.open_economy_fields,
            height=40
        )
        economy_fields_btn.pack(fill="x", padx=10, pady=2)

        # Merchant operations (CLI equivalent)
        merchant_btn = ctk.CTkButton(
            cli_frame,
            text="👤 Merchant Operations",
            command=self.open_merchant_level,
            height=40
        )
        merchant_btn.pack(fill="x", padx=10, pady=2)

        # Outpost level editing (CLI equivalent)
        outpost_level_btn = ctk.CTkButton(
            cli_frame,
            text="🏢 Outpost Level",
            command=self.open_outpost_level,
            height=40
        )
        outpost_level_btn.pack(fill="x", padx=10, pady=2)

        # Fine tune items (CLI equivalent)
        fine_tune_btn = ctk.CTkButton(
            cli_frame,
            text="🔧 Fine Tune Items",
            command=self.open_fine_tune,
            height=40
        )
        fine_tune_btn.pack(fill="x", padx=10, pady=2)

        # Spread edit (CLI equivalent)
        spread_edit_btn = ctk.CTkButton(
            cli_frame,
            text="📊 Spread Edit Items",
            command=self.open_spread_edit,
            height=40
        )
        spread_edit_btn.pack(fill="x", padx=10, pady=2)

        # Purchase settings (CLI equivalent)
        purchase_settings_btn = ctk.CTkButton(
            cli_frame,
            text="🛒 Purchase Settings",
            command=self.open_purchase_settings,
            height=40
        )
        purchase_settings_btn.pack(fill="x", padx=10, pady=2)

    def create_essential_categories_section(self, parent):
        """Create essential categories section (available in both modes)"""
        categories_frame = ctk.CTkFrame(parent)
        categories_frame.pack(fill="x", pady=5)

        categories_title = ctk.CTkLabel(
            categories_frame,
            text="📂 Essential Categories",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        categories_title.pack(pady=5)

        # Essential category buttons - these are the most important for SCUM economy management
        essential_categories = [
            ("improvised", "🔧 Improvised Items"),
            ("crafted", "🛠️ Crafted Items"),
            ("two_handed", "⚔️ Two-Handed Weapons"),
            ("weapons", "🔫 Weapons"),
            ("fish", "🐟 Fish Items"),
            ("medical", "💊 Medical Items"),
            ("military", "🎖️ Military Gear")
        ]

        # Create buttons in a grid layout for better organization
        buttons_frame = ctk.CTkFrame(categories_frame)
        buttons_frame.pack(fill="x", padx=10, pady=5)

        for i, (category_key, category_name) in enumerate(essential_categories):
            category_btn = ctk.CTkButton(
                buttons_frame,
                text=category_name,
                command=lambda k=category_key: self.open_category_editor(k),
                height=35
            )
            category_btn.pack(fill="x", padx=5, pady=1)

    def create_custom_buckets_section(self, parent, advanced_mode=False):
        """Create custom buckets section (available in both modes)"""
        buckets_frame = ctk.CTkFrame(parent)
        buckets_frame.pack(fill="x", pady=5)

        # Header with bucket management
        bucket_header = ctk.CTkFrame(buckets_frame)
        bucket_header.pack(fill="x", padx=5, pady=5)

        bucket_title = ctk.CTkLabel(
            bucket_header,
            text="🪣 Custom Buckets",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        bucket_title.pack(side="left", padx=10, pady=5)

        # Bucket management buttons
        bucket_mgmt_frame = ctk.CTkFrame(bucket_header)
        bucket_mgmt_frame.pack(side="right", padx=5)

        create_bucket_btn = ctk.CTkButton(
            bucket_mgmt_frame,
            text="➕ Create",
            command=self.create_custom_bucket,
            width=80,
            height=30
        )
        create_bucket_btn.pack(side="left", padx=2)

        manage_bucket_btn = ctk.CTkButton(
            bucket_mgmt_frame,
            text="⚙️ Manage",
            command=lambda: self.manage_custom_buckets(advanced_mode),
            width=80,
            height=30
        )
        manage_bucket_btn.pack(side="left", padx=2)

        # Dynamic bucket buttons
        self.bucket_buttons_frame = ctk.CTkFrame(buckets_frame)
        self.bucket_buttons_frame.pack(fill="x", padx=5, pady=5)

        self.refresh_bucket_buttons()

    def create_advanced_editing_section(self, parent):
        """Create advanced editing section (available in both modes)"""
        advanced_frame = ctk.CTkFrame(parent)
        advanced_frame.pack(fill="x", pady=5)

        advanced_title = ctk.CTkLabel(
            advanced_frame,
            text="🔧 Advanced Editing",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        advanced_title.pack(pady=5)

        # Outpost/Trader/Item editing
        outpost_btn = ctk.CTkButton(
            advanced_frame,
            text="🏢 Edit by Outpost",
            command=self.open_outpost_editor,
            height=35
        )
        outpost_btn.pack(fill="x", padx=10, pady=1)

        trader_btn = ctk.CTkButton(
            advanced_frame,
            text="👤 Edit by Trader",
            command=self.open_trader_editor,
            height=35
        )
        trader_btn.pack(fill="x", padx=10, pady=1)

        item_btn = ctk.CTkButton(
            advanced_frame,
            text="📦 Edit Individual Items",
            command=self.open_item_editor,
            height=35
        )
        item_btn.pack(fill="x", padx=10, pady=1)

    def create_professional_tools(self, parent):
        """Create professional tools (Advanced mode only)"""
        # Note about current mode
        if self.user_mode == "normal":
            note_frame = ctk.CTkFrame(parent)
            note_frame.pack(fill="x", pady=10)

            note_label = ctk.CTkLabel(
                note_frame,
                text="💡 Switch to Advanced Mode for F.I.S.H. Logic,\nSpread Editing, and Professional Analytics",
                font=ctk.CTkFont(size=11),
                text_color="#ffc107",
                justify="center"
            )
            note_label.pack(pady=10)
        else:
            # Professional tools for advanced mode
            prof_frame = ctk.CTkFrame(parent)
            prof_frame.pack(fill="x", pady=5)

            prof_title = ctk.CTkLabel(
                prof_frame,
                text="🎓 Professional Tools",
                font=ctk.CTkFont(size=14, weight="bold")
            )
            prof_title.pack(pady=5)

            # Advanced analytics only (Spread Edit and Purchase Settings moved to CLI-style tools)
            # Keep only truly advanced features here

            # Heat map visualization
            heatmap_btn = ctk.CTkButton(
                prof_frame,
                text="🔥 Edit Heat Map",
                command=self.open_heat_map_window,
                height=35,
                fg_color="#ff6600",
                hover_color="#ff8533"
            )
            heatmap_btn.pack(fill="x", padx=10, pady=1)

    def refresh_bucket_buttons(self):
        """Refresh the dynamic bucket buttons"""
        # Clear existing buttons
        for widget in self.bucket_buttons_frame.winfo_children():
            widget.destroy()

        # Create buttons for each custom bucket
        for bucket_key, bucket_data in self.custom_buckets.items():
            bucket_btn = ctk.CTkButton(
                self.bucket_buttons_frame,
                text=bucket_data['name'],
                command=lambda k=bucket_key: self.open_bucket_editor(k),
                height=35
            )
            bucket_btn.pack(fill="x", padx=10, pady=1)

    def create_custom_bucket(self):
        """Create a new custom bucket"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Create Custom Bucket")
        dialog.geometry("500x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # Header
        header_label = ctk.CTkLabel(
            dialog,
            text="🪣 Create Custom Bucket",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        header_label.pack(pady=10)

        # Bucket name
        name_frame = ctk.CTkFrame(dialog)
        name_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(name_frame, text="Bucket Name:").pack(anchor="w", padx=5, pady=2)
        name_var = ctk.StringVar()
        name_entry = ctk.CTkEntry(name_frame, textvariable=name_var, placeholder_text="e.g., My Custom Items")
        name_entry.pack(fill="x", padx=5, pady=2)

        # Bucket icon
        icon_frame = ctk.CTkFrame(dialog)
        icon_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(icon_frame, text="Icon (emoji):").pack(anchor="w", padx=5, pady=2)
        icon_var = ctk.StringVar(value="📦")
        icon_entry = ctk.CTkEntry(icon_frame, textvariable=icon_var, width=60)
        icon_entry.pack(anchor="w", padx=5, pady=2)

        # Description
        desc_frame = ctk.CTkFrame(dialog)
        desc_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(desc_frame, text="Description:").pack(anchor="w", padx=5, pady=2)
        desc_var = ctk.StringVar()
        desc_entry = ctk.CTkEntry(desc_frame, textvariable=desc_var, placeholder_text="Brief description of this bucket")
        desc_entry.pack(fill="x", padx=5, pady=2)

        # Filters section
        filters_frame = ctk.CTkFrame(dialog)
        filters_frame.pack(fill="both", expand=True, padx=20, pady=5)

        ctk.CTkLabel(filters_frame, text="Filters (define what items go in this bucket):", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=5, pady=5)

        # Filter list
        self.filter_list = []
        filters_scroll = ctk.CTkScrollableFrame(filters_frame)
        filters_scroll.pack(fill="both", expand=True, padx=5, pady=5)

        def add_filter():
            filter_frame = ctk.CTkFrame(filters_scroll)
            filter_frame.pack(fill="x", pady=2)

            # Filter type
            filter_type_var = ctk.StringVar(value="contains")
            filter_type_menu = ctk.CTkOptionMenu(
                filter_frame,
                variable=filter_type_var,
                values=["contains", "not_contains", "starts_with", "not_starts_with"],
                width=120
            )
            filter_type_menu.pack(side="left", padx=2)

            # Filter value
            filter_value_var = ctk.StringVar()
            filter_value_entry = ctk.CTkEntry(filter_frame, textvariable=filter_value_var, placeholder_text="filter text")
            filter_value_entry.pack(side="left", fill="x", expand=True, padx=2)

            # Remove button
            def remove_filter():
                filter_frame.destroy()
                self.filter_list.remove((filter_type_var, filter_value_var))

            remove_btn = ctk.CTkButton(filter_frame, text="❌", command=remove_filter, width=30)
            remove_btn.pack(side="right", padx=2)

            self.filter_list.append((filter_type_var, filter_value_var))

        # Add filter button
        add_filter_btn = ctk.CTkButton(filters_frame, text="➕ Add Filter", command=add_filter)
        add_filter_btn.pack(pady=5)

        # Add initial filter
        add_filter()

        # Buttons
        button_frame = ctk.CTkFrame(dialog)
        button_frame.pack(fill="x", padx=20, pady=10)

        def save_bucket():
            name = name_var.get().strip()
            icon = icon_var.get().strip()
            description = desc_var.get().strip()

            if not name:
                messagebox.showerror("Error", "Please enter a bucket name")
                return

            # Build filters
            filters = []
            for filter_type_var, filter_value_var in self.filter_list:
                filter_value = filter_value_var.get().strip()
                if filter_value:
                    filters.append({
                        "type": filter_type_var.get(),
                        "value": filter_value
                    })

            if not filters:
                messagebox.showerror("Error", "Please add at least one filter")
                return

            # Generate bucket key
            bucket_key = name.lower().replace(" ", "_").replace("-", "_")

            # Save bucket
            self.custom_buckets[bucket_key] = {
                "name": f"{icon} {name}",
                "description": description,
                "filters": filters
            }

            self.save_custom_buckets()
            self.refresh_bucket_buttons()
            dialog.destroy()
            messagebox.showinfo("Success", f"Custom bucket '{name}' created successfully!")

        save_btn = ctk.CTkButton(button_frame, text="💾 Save Bucket", command=save_bucket)
        save_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(button_frame, text="❌ Cancel", command=dialog.destroy)
        cancel_btn.pack(side="right", padx=5)

    def manage_custom_buckets(self, advanced_mode=False):
        """Manage existing custom buckets"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Manage Custom Buckets")
        dialog.geometry("700x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # Header
        mode_text = " (Advanced Mode)" if advanced_mode else ""
        header_label = ctk.CTkLabel(
            dialog,
            text=f"⚙️ Manage Custom Buckets{mode_text}",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        header_label.pack(pady=10)

        # Bucket list
        buckets_frame = ctk.CTkScrollableFrame(dialog)
        buckets_frame.pack(fill="both", expand=True, padx=20, pady=10)

        for bucket_key, bucket_data in self.custom_buckets.items():
            bucket_frame = ctk.CTkFrame(buckets_frame)
            bucket_frame.pack(fill="x", pady=5)

            # Bucket info
            info_frame = ctk.CTkFrame(bucket_frame)
            info_frame.pack(side="left", fill="x", expand=True, padx=5, pady=5)

            name_label = ctk.CTkLabel(info_frame, text=bucket_data['name'], font=ctk.CTkFont(size=14, weight="bold"))
            name_label.pack(anchor="w")

            desc_label = ctk.CTkLabel(info_frame, text=bucket_data['description'], font=ctk.CTkFont(size=11))
            desc_label.pack(anchor="w")

            filters_text = f"Filters: {len(bucket_data['filters'])} rules"
            filters_label = ctk.CTkLabel(info_frame, text=filters_text, font=ctk.CTkFont(size=10), text_color="gray")
            filters_label.pack(anchor="w")

            # Action buttons
            actions_frame = ctk.CTkFrame(bucket_frame)
            actions_frame.pack(side="right", padx=5, pady=5)

            # Edit button (Advanced mode only)
            if advanced_mode:
                def edit_bucket(key=bucket_key):
                    dialog.destroy()
                    self.edit_bucket_rules(key)

                edit_btn = ctk.CTkButton(actions_frame, text="✏️", command=edit_bucket, width=40, fg_color="#007bff")
                edit_btn.pack(pady=2)

            def delete_bucket(key=bucket_key):
                if messagebox.askyesno("Confirm Delete", f"Delete bucket '{bucket_data['name']}'?"):
                    del self.custom_buckets[key]
                    self.save_custom_buckets()
                    self.refresh_bucket_buttons()
                    dialog.destroy()
                    self.manage_custom_buckets(advanced_mode)  # Refresh dialog

            delete_btn = ctk.CTkButton(actions_frame, text="🗑️", command=delete_bucket, width=40, fg_color="#dc3545")
            delete_btn.pack(pady=2)

        # Close button
        close_btn = ctk.CTkButton(dialog, text="✅ Close", command=dialog.destroy)
        close_btn.pack(pady=10)

    def edit_bucket_rules(self, bucket_key):
        """Edit bucket rules (Advanced mode only)"""
        if bucket_key not in self.custom_buckets:
            messagebox.showerror("Error", f"Bucket '{bucket_key}' not found")
            return

        bucket_data = self.custom_buckets[bucket_key]

        dialog = ctk.CTkToplevel(self.root)
        dialog.title(f"Edit Bucket Rules: {bucket_data['name']}")
        dialog.geometry("600x700")
        dialog.transient(self.root)
        dialog.grab_set()

        # Header
        header_label = ctk.CTkLabel(
            dialog,
            text=f"✏️ Edit Bucket Rules",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        header_label.pack(pady=10)

        # Bucket info
        info_label = ctk.CTkLabel(
            dialog,
            text=f"Editing: {bucket_data['name']}\n{bucket_data['description']}",
            font=ctk.CTkFont(size=12)
        )
        info_label.pack(pady=5)

        # Bucket name and description editing
        name_frame = ctk.CTkFrame(dialog)
        name_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(name_frame, text="Bucket Name:").pack(anchor="w", padx=5, pady=2)
        name_var = ctk.StringVar(value=bucket_data['name'].split(' ', 1)[1] if ' ' in bucket_data['name'] else bucket_data['name'])
        name_entry = ctk.CTkEntry(name_frame, textvariable=name_var)
        name_entry.pack(fill="x", padx=5, pady=2)

        icon_frame = ctk.CTkFrame(dialog)
        icon_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(icon_frame, text="Icon:").pack(anchor="w", padx=5, pady=2)
        icon_var = ctk.StringVar(value=bucket_data['name'].split(' ')[0] if ' ' in bucket_data['name'] else "📦")
        icon_entry = ctk.CTkEntry(icon_frame, textvariable=icon_var, width=60)
        icon_entry.pack(anchor="w", padx=5, pady=2)

        desc_frame = ctk.CTkFrame(dialog)
        desc_frame.pack(fill="x", padx=20, pady=5)

        ctk.CTkLabel(desc_frame, text="Description:").pack(anchor="w", padx=5, pady=2)
        desc_var = ctk.StringVar(value=bucket_data['description'])
        desc_entry = ctk.CTkEntry(desc_frame, textvariable=desc_var)
        desc_entry.pack(fill="x", padx=5, pady=2)

        # Filters editing
        filters_frame = ctk.CTkFrame(dialog)
        filters_frame.pack(fill="both", expand=True, padx=20, pady=5)

        ctk.CTkLabel(filters_frame, text="Filter Rules:", font=ctk.CTkFont(weight="bold")).pack(anchor="w", padx=5, pady=5)

        filters_scroll = ctk.CTkScrollableFrame(filters_frame)
        filters_scroll.pack(fill="both", expand=True, padx=5, pady=5)

        # Load existing filters
        self.edit_filter_list = []

        def add_filter(filter_type="contains", filter_value=""):
            filter_frame = ctk.CTkFrame(filters_scroll)
            filter_frame.pack(fill="x", pady=2)

            filter_type_var = ctk.StringVar(value=filter_type)
            filter_type_menu = ctk.CTkOptionMenu(
                filter_frame,
                variable=filter_type_var,
                values=["contains", "not_contains", "starts_with", "not_starts_with", "contains_any", "not_contains_any"],
                width=150
            )
            filter_type_menu.pack(side="left", padx=2)

            filter_value_var = ctk.StringVar(value=filter_value)
            filter_value_entry = ctk.CTkEntry(filter_frame, textvariable=filter_value_var, placeholder_text="filter text")
            filter_value_entry.pack(side="left", fill="x", expand=True, padx=2)

            def remove_filter():
                filter_frame.destroy()
                self.edit_filter_list.remove((filter_type_var, filter_value_var))

            remove_btn = ctk.CTkButton(filter_frame, text="❌", command=remove_filter, width=30)
            remove_btn.pack(side="right", padx=2)

            self.edit_filter_list.append((filter_type_var, filter_value_var))

        # Load existing filters
        for filter_rule in bucket_data['filters']:
            add_filter(filter_rule['type'], filter_rule['value'])

        # Add filter button
        add_filter_btn = ctk.CTkButton(filters_frame, text="➕ Add Filter", command=lambda: add_filter())
        add_filter_btn.pack(pady=5)

        # Buttons
        button_frame = ctk.CTkFrame(dialog)
        button_frame.pack(fill="x", padx=20, pady=10)

        def save_changes():
            name = name_var.get().strip()
            icon = icon_var.get().strip()
            description = desc_var.get().strip()

            if not name:
                messagebox.showerror("Error", "Please enter a bucket name")
                return

            # Build filters
            filters = []
            for filter_type_var, filter_value_var in self.edit_filter_list:
                filter_value = filter_value_var.get().strip()
                if filter_value:
                    filters.append({
                        "type": filter_type_var.get(),
                        "value": filter_value
                    })

            if not filters:
                messagebox.showerror("Error", "Please add at least one filter")
                return

            # Update bucket
            self.custom_buckets[bucket_key] = {
                "name": f"{icon} {name}",
                "description": description,
                "filters": filters
            }

            self.save_custom_buckets()
            self.refresh_bucket_buttons()
            dialog.destroy()
            messagebox.showinfo("Success", f"Bucket '{name}' updated successfully!")

        save_btn = ctk.CTkButton(button_frame, text="💾 Save Changes", command=save_changes)
        save_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(button_frame, text="❌ Cancel", command=dialog.destroy)
        cancel_btn.pack(side="right", padx=5)

    def open_bucket_editor(self, bucket_key):
        """Open bucket editor for custom bucket"""
        if bucket_key not in self.custom_buckets:
            messagebox.showerror("Error", f"Bucket '{bucket_key}' not found")
            return

        bucket_data = self.custom_buckets[bucket_key]

        # Find matching items using bucket filters with correct JSON structure
        matching_items = []
        if self.current_data:
            traders = self.current_data.get("economy-override", {}).get("traders", {})
            for trader_name, trader_items in traders.items():
                if isinstance(trader_items, list):
                    for item in trader_items:
                        if isinstance(item, dict):
                            item_code = item.get("tradeable-code", "")
                            if item_code and self.apply_bucket_filters(item_code, bucket_data['filters']):
                                # Extract outpost from trader name
                                outpost_name = trader_name.split("_")[0] if "_" in trader_name else "Unknown"
                                matching_items.append({
                                    'outpost': outpost_name,
                                    'trader': trader_name,
                                    'code': item_code,
                                    'data': item
                                })

        if not matching_items:
            messagebox.showinfo("No Items", f"No items found in bucket: {bucket_data['name']}")
            return

        # Open bucket editor dialog
        self.show_bucket_editor(bucket_data, matching_items)

    def show_bucket_editor(self, bucket_data, items):
        """Show bucket editor dialog (similar to category editor but for custom buckets)"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title(f"Edit {bucket_data['name']} - {len(items)} items")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # Header
        header_label = ctk.CTkLabel(
            dialog,
            text=f"{bucket_data['name']} Editor",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        header_label.pack(pady=10)

        info_label = ctk.CTkLabel(
            dialog,
            text=f"{bucket_data['description']}\nFound {len(items)} matching items",
            font=ctk.CTkFont(size=12)
        )
        info_label.pack(pady=5)

        # Batch editing controls (same as category editor)
        controls_frame = ctk.CTkFrame(dialog)
        controls_frame.pack(fill="x", padx=20, pady=10)

        # Price adjustment
        price_frame = ctk.CTkFrame(controls_frame)
        price_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(price_frame, text="💰 Price Adjustment:").pack(side="left", padx=5)
        price_var = ctk.StringVar(value="0")
        price_entry = ctk.CTkEntry(price_frame, textvariable=price_var, width=100)
        price_entry.pack(side="left", padx=5)
        ctk.CTkLabel(price_frame, text="% (e.g., +10 or -20)").pack(side="left", padx=5)

        # Fame points
        fame_frame = ctk.CTkFrame(controls_frame)
        fame_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(fame_frame, text="⭐ Fame Points:").pack(side="left", padx=5)
        fame_var = ctk.StringVar(value="")
        fame_entry = ctk.CTkEntry(fame_frame, textvariable=fame_var, width=100)
        fame_entry.pack(side="left", padx=5)
        ctk.CTkLabel(fame_frame, text="(leave empty to keep current)").pack(side="left", padx=5)

        # Can be purchased
        purchase_var = ctk.BooleanVar(value=True)
        purchase_check = ctk.CTkCheckBox(
            controls_frame,
            text="🛒 Can be purchased",
            variable=purchase_var
        )
        purchase_check.pack(pady=5)

        # Buttons
        button_frame = ctk.CTkFrame(dialog)
        button_frame.pack(fill="x", padx=20, pady=10)

        def show_validation_info():
            """Show CLI validation rules and safety information"""
            info_msg = self.validation_engine.get_safe_percentage_range_info()
            messagebox.showinfo("🛡️ CLI Safety Rules & Validation", info_msg)

        def apply_changes():
            # Use the same validation and application logic as category editor
            # This ensures consistency across all bucket types
            try:
                price_change = price_var.get().strip()
                fame_points = fame_var.get().strip()
                can_purchase = purchase_var.get()

                # Apply the same validation logic as category editor
                validation_errors = []

                if price_change and price_change != "0":
                    if price_change.startswith(('+', '-')):
                        is_valid, result = self.validation_engine.validate_percentage(price_change, mode="normal")
                        if not is_valid:
                            validation_errors.append(f"Price change: {result}")
                    else:
                        is_valid, result = self.validation_engine.validate_price_value(price_change)
                        if not is_valid:
                            validation_errors.append(f"Price value: {result}")

                if fame_points:
                    is_valid, result = self.validation_engine.validate_fame_points(fame_points)
                    if not is_valid:
                        validation_errors.append(f"Fame points: {result}")

                if validation_errors:
                    error_message = "⚠️ VALIDATION ERRORS DETECTED ⚠️\n\n" + "\n".join([f"• {error}" for error in validation_errors])
                    error_message += f"\n\n{self.validation_engine.get_safe_percentage_range_info()}"
                    messagebox.showerror("Validation Error", error_message)
                    return

                # Apply changes with same logic as category editor
                changes_made = 0
                skipped_items = 0

                for item in items:
                    item_data = item['data']
                    changes = []

                    # Apply price change with CLI logic
                    if price_change and price_change != "0":
                        if price_change.startswith(('+', '-')):
                            percentage = float(price_change)
                            price_field = None
                            if 'base-purchase-price' in item_data:
                                price_field = 'base-purchase-price'
                            elif 'BuyPrice' in item_data:
                                price_field = 'BuyPrice'

                            if price_field and item_data[price_field] not in ['-1', 'null', None, '-1.0']:
                                try:
                                    old_price = float(item_data[price_field])
                                    if old_price > 0:
                                        new_price = round(old_price * (1 + percentage / 100), 2)
                                        if new_price < 1:
                                            new_price = 1
                                        item_data[price_field] = str(new_price)
                                        changes.append(f"Price: {old_price} → {new_price}")
                                    else:
                                        skipped_items += 1
                                except (ValueError, TypeError):
                                    skipped_items += 1
                            else:
                                skipped_items += 1

                    # Apply fame points and purchase settings (same logic as category editor)
                    if fame_points:
                        fame_field = 'required-famepoints' if 'required-famepoints' in item_data else 'RequiredFamePoints'
                        if fame_field in item_data:
                            old_fame = item_data.get(fame_field, -1)
                            new_fame = int(fame_points)
                            item_data[fame_field] = str(new_fame)
                            changes.append(f"Fame: {old_fame} → {new_fame}")

                    purchase_field = 'can-be-purchased' if 'can-be-purchased' in item_data else 'CanBePurchased'
                    if purchase_field in item_data:
                        old_purchase = item_data.get(purchase_field, 'default')
                        new_purchase = 'true' if can_purchase else 'false'
                        item_data[purchase_field] = new_purchase
                        if str(old_purchase).lower() != new_purchase:
                            changes.append(f"Purchase: {old_purchase} → {new_purchase}")

                    if changes:
                        changes_made += 1
                        change_desc = f"{bucket_data['name']}: {item['code']} - {', '.join(changes)}"
                        self.add_pending_change(
                            'bucket_batch_edit',
                            change_desc,
                            None,
                            None,
                            lambda: None
                        )

                dialog.destroy()

                result_msg = f"✅ BUCKET OPERATION COMPLETED\n\n"
                result_msg += f"📊 Results for {bucket_data['name']}:\n"
                result_msg += f"• Modified items: {changes_made}\n"
                if skipped_items > 0:
                    result_msg += f"• Skipped items: {skipped_items} (default values preserved)\n"
                result_msg += f"• Total items processed: {len(items)}\n\n"
                result_msg += "💡 Skipped items had default values (-1, null, or none) and were protected from changes as per CLI safety rules."

                messagebox.showinfo("Bucket Operation Complete", result_msg)
                self.refresh_displays()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to apply changes:\n\n{str(e)}\n\nNo changes have been made to protect your file integrity.")

        # Info button for validation rules
        info_btn = ctk.CTkButton(
            button_frame,
            text="🛡️ Safety Rules",
            command=show_validation_info,
            width=120,
            fg_color="#17a2b8",
            hover_color="#138496"
        )
        info_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="✅ Apply Changes",
            command=apply_changes,
            fg_color="#28a745",
            hover_color="#218838"
        )
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Cancel",
            command=dialog.destroy,
            fg_color="#dc3545",
            hover_color="#c82333"
        )
        cancel_btn.pack(side="right", padx=5)

    def open_outpost_editor(self):
        """Open outpost-level editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        # Get list of outposts using correct JSON structure
        outposts = {}
        traders = self.current_data.get("economy-override", {}).get("traders", {})
        for trader_name in traders.keys():
            # Extract outpost from trader name (e.g., "Z1_Trader_Food" -> "Z1")
            outpost_name = trader_name.split("_")[0] if "_" in trader_name else "Unknown"
            if outpost_name not in outposts:
                outposts[outpost_name] = 0
            outposts[outpost_name] += 1

        if not outposts:
            messagebox.showinfo("No Outposts", "No outposts found in the current file")
            return

        # Show outpost selection dialog
        self.show_outpost_selection_dialog(outposts.items())

    def show_outpost_selection_dialog(self, outposts):
        """Show dialog to select outpost for editing"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Select Outpost to Edit")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # Header
        header_label = ctk.CTkLabel(
            dialog,
            text="🏢 Select Outpost to Edit",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        header_label.pack(pady=10)

        info_label = ctk.CTkLabel(
            dialog,
            text="Choose an outpost to edit all traders within it",
            font=ctk.CTkFont(size=12)
        )
        info_label.pack(pady=5)

        # Outpost list
        outpost_frame = ctk.CTkScrollableFrame(dialog)
        outpost_frame.pack(fill="both", expand=True, padx=20, pady=10)

        selected_outpost = None

        def select_outpost(outpost_name):
            nonlocal selected_outpost
            selected_outpost = outpost_name
            dialog.destroy()
            self.edit_outpost(outpost_name)

        for outpost_name, trader_count in outposts:
            outpost_btn_frame = ctk.CTkFrame(outpost_frame)
            outpost_btn_frame.pack(fill="x", pady=5)

            outpost_btn = ctk.CTkButton(
                outpost_btn_frame,
                text=f"🏢 {outpost_name}",
                command=lambda name=outpost_name: select_outpost(name),
                height=40,
                anchor="w"
            )
            outpost_btn.pack(side="left", fill="x", expand=True, padx=5, pady=5)

            trader_info = ctk.CTkLabel(
                outpost_btn_frame,
                text=f"{trader_count} traders",
                font=ctk.CTkFont(size=11),
                text_color="gray"
            )
            trader_info.pack(side="right", padx=10, pady=5)

        # Cancel button
        cancel_btn = ctk.CTkButton(dialog, text="❌ Cancel", command=dialog.destroy)
        cancel_btn.pack(pady=10)

    def edit_outpost(self, outpost_name):
        """Edit all traders in the selected outpost"""
        # Collect all items from all traders in this outpost using correct JSON structure
        all_items = []
        traders = self.current_data.get("economy-override", {}).get("traders", {})

        for trader_name, trader_items in traders.items():
            # Check if this trader belongs to the selected outpost
            trader_outpost = trader_name.split("_")[0] if "_" in trader_name else "Unknown"
            if trader_outpost == outpost_name:
                if isinstance(trader_items, list):
                    for item in trader_items:
                        if isinstance(item, dict):
                            item_code = item.get("tradeable-code", "")
                            if item_code:
                                all_items.append({
                                    'outpost': outpost_name,
                                    'trader': trader_name,
                                    'code': item_code,
                                    'data': item
                                })

        if not all_items:
            messagebox.showinfo("No Items", f"No items found in outpost '{outpost_name}'")
            return

        # Create outpost data for editor
        outpost_info = {
            'name': f"🏢 {outpost_name}",
            'description': f"All traders and items in {outpost_name} outpost ({len(all_items)} items total)"
        }

        # Open outpost editor (reuse bucket editor logic)
        self.show_outpost_editor(outpost_info, all_items)

    def show_outpost_editor(self, outpost_info, items):
        """Show outpost editor dialog (reuses bucket editor pattern)"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title(f"Edit {outpost_info['name']} - {len(items)} items")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # Header
        header_label = ctk.CTkLabel(
            dialog,
            text=f"{outpost_info['name']} Editor",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        header_label.pack(pady=10)

        info_label = ctk.CTkLabel(
            dialog,
            text=outpost_info['description'],
            font=ctk.CTkFont(size=12)
        )
        info_label.pack(pady=5)

        # Batch editing controls (same as bucket editor)
        controls_frame = ctk.CTkFrame(dialog)
        controls_frame.pack(fill="x", padx=20, pady=10)

        # Price adjustment
        price_frame = ctk.CTkFrame(controls_frame)
        price_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(price_frame, text="💰 Price Adjustment:").pack(side="left", padx=5)
        price_var = ctk.StringVar(value="0")
        price_entry = ctk.CTkEntry(price_frame, textvariable=price_var, width=100)
        price_entry.pack(side="left", padx=5)
        ctk.CTkLabel(price_frame, text="% (e.g., +10 or -20)").pack(side="left", padx=5)

        # Fame points
        fame_frame = ctk.CTkFrame(controls_frame)
        fame_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(fame_frame, text="⭐ Fame Points:").pack(side="left", padx=5)
        fame_var = ctk.StringVar(value="")
        fame_entry = ctk.CTkEntry(fame_frame, textvariable=fame_var, width=100)
        fame_entry.pack(side="left", padx=5)
        ctk.CTkLabel(fame_frame, text="(leave empty to keep current)").pack(side="left", padx=5)

        # Can be purchased
        purchase_var = ctk.BooleanVar(value=True)
        purchase_check = ctk.CTkCheckBox(
            controls_frame,
            text="🛒 Can be purchased",
            variable=purchase_var
        )
        purchase_check.pack(pady=5)

        # Buttons
        button_frame = ctk.CTkFrame(dialog)
        button_frame.pack(fill="x", padx=20, pady=10)

        def show_validation_info():
            info_msg = self.validation_engine.get_safe_percentage_range_info()
            messagebox.showinfo("🛡️ CLI Safety Rules & Validation", info_msg)

        def apply_changes():
            # Reuse the same validation and application logic as bucket editor
            try:
                price_change = price_var.get().strip()
                fame_points = fame_var.get().strip()
                can_purchase = purchase_var.get()

                # Apply validation (same as bucket editor)
                validation_errors = []

                if price_change and price_change != "0":
                    if price_change.startswith(('+', '-')):
                        is_valid, result = self.validation_engine.validate_percentage(price_change)
                        if not is_valid:
                            validation_errors.append(f"Price change: {result}")
                    else:
                        is_valid, result = self.validation_engine.validate_price_value(price_change)
                        if not is_valid:
                            validation_errors.append(f"Price value: {result}")

                if fame_points:
                    is_valid, result = self.validation_engine.validate_fame_points(fame_points)
                    if not is_valid:
                        validation_errors.append(f"Fame points: {result}")

                if validation_errors:
                    error_message = "⚠️ VALIDATION ERRORS DETECTED ⚠️\n\n" + "\n".join([f"• {error}" for error in validation_errors])
                    error_message += f"\n\n{self.validation_engine.get_safe_percentage_range_info()}"
                    messagebox.showerror("Validation Error", error_message)
                    return

                # Apply changes (same logic as bucket editor)
                changes_made = 0
                skipped_items = 0

                for item in items:
                    item_data = item['data']
                    changes = []

                    # Apply price change with CLI logic
                    if price_change and price_change != "0":
                        if price_change.startswith(('+', '-')):
                            percentage = float(price_change)
                            price_field = None
                            if 'base-purchase-price' in item_data:
                                price_field = 'base-purchase-price'
                            elif 'BuyPrice' in item_data:
                                price_field = 'BuyPrice'

                            if price_field and item_data[price_field] not in ['-1', 'null', None, '-1.0']:
                                try:
                                    old_price = float(item_data[price_field])
                                    if old_price > 0:
                                        new_price = round(old_price * (1 + percentage / 100), 2)
                                        if new_price < 1:
                                            new_price = 1
                                        item_data[price_field] = str(new_price)
                                        changes.append(f"Price: {old_price} → {new_price}")
                                    else:
                                        skipped_items += 1
                                except (ValueError, TypeError):
                                    skipped_items += 1
                            else:
                                skipped_items += 1

                    # Apply fame points and purchase settings
                    if fame_points:
                        fame_field = 'required-famepoints' if 'required-famepoints' in item_data else 'RequiredFamePoints'
                        if fame_field in item_data:
                            old_fame = item_data.get(fame_field, -1)
                            new_fame = int(fame_points)
                            item_data[fame_field] = str(new_fame)
                            changes.append(f"Fame: {old_fame} → {new_fame}")

                    purchase_field = 'can-be-purchased' if 'can-be-purchased' in item_data else 'CanBePurchased'
                    if purchase_field in item_data:
                        old_purchase = item_data.get(purchase_field, 'default')
                        new_purchase = 'true' if can_purchase else 'false'
                        item_data[purchase_field] = new_purchase
                        if str(old_purchase).lower() != new_purchase:
                            changes.append(f"Purchase: {old_purchase} → {new_purchase}")

                    if changes:
                        changes_made += 1
                        change_desc = f"{outpost_info['name']}: {item['code']} - {', '.join(changes)}"
                        self.add_pending_change(
                            'outpost_batch_edit',
                            change_desc,
                            None,
                            None,
                            lambda: None
                        )

                dialog.destroy()

                result_msg = f"✅ OUTPOST OPERATION COMPLETED\n\n"
                result_msg += f"📊 Results for {outpost_info['name']}:\n"
                result_msg += f"• Modified items: {changes_made}\n"
                if skipped_items > 0:
                    result_msg += f"• Skipped items: {skipped_items} (default values preserved)\n"
                result_msg += f"• Total items processed: {len(items)}\n\n"
                result_msg += "💡 Skipped items had default values (-1, null, or none) and were protected from changes as per CLI safety rules."

                messagebox.showinfo("Outpost Operation Complete", result_msg)
                self.refresh_displays()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to apply changes:\n\n{str(e)}\n\nNo changes have been made to protect your file integrity.")

        # Buttons
        info_btn = ctk.CTkButton(
            button_frame,
            text="🛡️ Safety Rules",
            command=show_validation_info,
            width=120,
            fg_color="#17a2b8",
            hover_color="#138496"
        )
        info_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="✅ Apply Changes",
            command=apply_changes,
            fg_color="#28a745",
            hover_color="#218838"
        )
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Cancel",
            command=dialog.destroy,
            fg_color="#dc3545",
            hover_color="#c82333"
        )
        cancel_btn.pack(side="right", padx=5)

    def open_trader_editor(self):
        """Open trader-level editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        # Get list of all traders using correct JSON structure
        traders = []
        traders_data = self.current_data.get("economy-override", {}).get("traders", {})
        for trader_name, trader_items in traders_data.items():
            if isinstance(trader_items, list):
                # Extract outpost from trader name
                outpost_name = trader_name.split("_")[0] if "_" in trader_name else "Unknown"
                item_count = len(trader_items)
                traders.append((outpost_name, trader_name, item_count))

        if not traders:
            messagebox.showinfo("No Traders", "No traders found in the current file")
            return

        # Show trader selection dialog
        self.show_trader_selection_dialog(traders)

    def show_trader_selection_dialog(self, traders):
        """Show dialog to select trader for editing"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Select Trader to Edit")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # Header
        header_label = ctk.CTkLabel(
            dialog,
            text="👤 Select Trader to Edit",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        header_label.pack(pady=10)

        info_label = ctk.CTkLabel(
            dialog,
            text="Choose a trader to edit all items they sell",
            font=ctk.CTkFont(size=12)
        )
        info_label.pack(pady=5)

        # Trader list
        trader_frame = ctk.CTkScrollableFrame(dialog)
        trader_frame.pack(fill="both", expand=True, padx=20, pady=10)

        def select_trader(outpost_name, trader_name):
            dialog.destroy()
            self.edit_trader(outpost_name, trader_name)

        for outpost_name, trader_name, item_count in traders:
            trader_btn_frame = ctk.CTkFrame(trader_frame)
            trader_btn_frame.pack(fill="x", pady=3)

            trader_btn = ctk.CTkButton(
                trader_btn_frame,
                text=f"👤 {trader_name}",
                command=lambda o=outpost_name, t=trader_name: select_trader(o, t),
                height=40,
                anchor="w"
            )
            trader_btn.pack(side="left", fill="x", expand=True, padx=5, pady=5)

            # Outpost and item info
            info_frame = ctk.CTkFrame(trader_btn_frame)
            info_frame.pack(side="right", padx=5, pady=5)

            outpost_label = ctk.CTkLabel(
                info_frame,
                text=f"🏢 {outpost_name}",
                font=ctk.CTkFont(size=10),
                text_color="gray"
            )
            outpost_label.pack(pady=1)

            items_label = ctk.CTkLabel(
                info_frame,
                text=f"{item_count} items",
                font=ctk.CTkFont(size=10),
                text_color="gray"
            )
            items_label.pack(pady=1)

        # Cancel button
        cancel_btn = ctk.CTkButton(dialog, text="❌ Cancel", command=dialog.destroy)
        cancel_btn.pack(pady=10)

    def edit_trader(self, outpost_name, trader_name):
        """Edit all items for the selected trader"""
        # Get trader data using correct JSON structure
        traders_data = self.current_data.get("economy-override", {}).get("traders", {})
        if trader_name not in traders_data:
            messagebox.showerror("Error", f"Trader '{trader_name}' not found")
            return

        trader_items_list = traders_data[trader_name]
        if not isinstance(trader_items_list, list):
            messagebox.showerror("Error", f"Invalid data structure for trader '{trader_name}'")
            return

        # Collect all items from this trader
        trader_items = []
        for item in trader_items_list:
            if isinstance(item, dict):
                item_code = item.get("tradeable-code", "")
                if item_code:
                    trader_items.append({
                        'outpost': outpost_name,
                        'trader': trader_name,
                        'code': item_code,
                        'data': item
                    })

        if not trader_items:
            messagebox.showinfo("No Items", f"No items found for trader '{trader_name}'")
            return

        # Create trader data for editor
        trader_info = {
            'name': f"👤 {trader_name}",
            'description': f"All items sold by {trader_name} in {outpost_name} ({len(trader_items)} items total)"
        }

        # Open trader editor (reuse bucket editor logic)
        self.show_trader_editor(trader_info, trader_items)

    def show_trader_editor(self, trader_info, items):
        """Show trader editor dialog (reuses bucket/outpost editor pattern)"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title(f"Edit {trader_info['name']} - {len(items)} items")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # Header
        header_label = ctk.CTkLabel(
            dialog,
            text=f"{trader_info['name']} Editor",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        header_label.pack(pady=10)

        info_label = ctk.CTkLabel(
            dialog,
            text=trader_info['description'],
            font=ctk.CTkFont(size=12)
        )
        info_label.pack(pady=5)

        # Batch editing controls (same as other editors)
        controls_frame = ctk.CTkFrame(dialog)
        controls_frame.pack(fill="x", padx=20, pady=10)

        # Price adjustment
        price_frame = ctk.CTkFrame(controls_frame)
        price_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(price_frame, text="💰 Price Adjustment:").pack(side="left", padx=5)
        price_var = ctk.StringVar(value="0")
        price_entry = ctk.CTkEntry(price_frame, textvariable=price_var, width=100)
        price_entry.pack(side="left", padx=5)
        ctk.CTkLabel(price_frame, text="% (e.g., +10 or -20)").pack(side="left", padx=5)

        # Fame points
        fame_frame = ctk.CTkFrame(controls_frame)
        fame_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(fame_frame, text="⭐ Fame Points:").pack(side="left", padx=5)
        fame_var = ctk.StringVar(value="")
        fame_entry = ctk.CTkEntry(fame_frame, textvariable=fame_var, width=100)
        fame_entry.pack(side="left", padx=5)
        ctk.CTkLabel(fame_frame, text="(leave empty to keep current)").pack(side="left", padx=5)

        # Can be purchased
        purchase_var = ctk.BooleanVar(value=True)
        purchase_check = ctk.CTkCheckBox(
            controls_frame,
            text="🛒 Can be purchased",
            variable=purchase_var
        )
        purchase_check.pack(pady=5)

        # Buttons
        button_frame = ctk.CTkFrame(dialog)
        button_frame.pack(fill="x", padx=20, pady=10)

        def show_validation_info():
            info_msg = self.validation_engine.get_safe_percentage_range_info()
            messagebox.showinfo("🛡️ CLI Safety Rules & Validation", info_msg)

        def apply_changes():
            # Reuse the same validation and application logic
            try:
                price_change = price_var.get().strip()
                fame_points = fame_var.get().strip()
                can_purchase = purchase_var.get()

                # Apply validation
                validation_errors = []

                if price_change and price_change != "0":
                    if price_change.startswith(('+', '-')):
                        is_valid, result = self.validation_engine.validate_percentage(price_change)
                        if not is_valid:
                            validation_errors.append(f"Price change: {result}")
                    else:
                        is_valid, result = self.validation_engine.validate_price_value(price_change)
                        if not is_valid:
                            validation_errors.append(f"Price value: {result}")

                if fame_points:
                    is_valid, result = self.validation_engine.validate_fame_points(fame_points)
                    if not is_valid:
                        validation_errors.append(f"Fame points: {result}")

                if validation_errors:
                    error_message = "⚠️ VALIDATION ERRORS DETECTED ⚠️\n\n" + "\n".join([f"• {error}" for error in validation_errors])
                    error_message += f"\n\n{self.validation_engine.get_safe_percentage_range_info()}"
                    messagebox.showerror("Validation Error", error_message)
                    return

                # Apply changes
                changes_made = 0
                skipped_items = 0

                for item in items:
                    item_data = item['data']
                    changes = []

                    # Apply price change with CLI logic
                    if price_change and price_change != "0":
                        if price_change.startswith(('+', '-')):
                            percentage = float(price_change)
                            price_field = None
                            if 'base-purchase-price' in item_data:
                                price_field = 'base-purchase-price'
                            elif 'BuyPrice' in item_data:
                                price_field = 'BuyPrice'

                            if price_field and item_data[price_field] not in ['-1', 'null', None, '-1.0']:
                                try:
                                    old_price = float(item_data[price_field])
                                    if old_price > 0:
                                        new_price = round(old_price * (1 + percentage / 100), 2)
                                        if new_price < 1:
                                            new_price = 1
                                        item_data[price_field] = str(new_price)
                                        changes.append(f"Price: {old_price} → {new_price}")
                                    else:
                                        skipped_items += 1
                                except (ValueError, TypeError):
                                    skipped_items += 1
                            else:
                                skipped_items += 1

                    # Apply fame points and purchase settings
                    if fame_points:
                        fame_field = 'required-famepoints' if 'required-famepoints' in item_data else 'RequiredFamePoints'
                        if fame_field in item_data:
                            old_fame = item_data.get(fame_field, -1)
                            new_fame = int(fame_points)
                            item_data[fame_field] = str(new_fame)
                            changes.append(f"Fame: {old_fame} → {new_fame}")

                    purchase_field = 'can-be-purchased' if 'can-be-purchased' in item_data else 'CanBePurchased'
                    if purchase_field in item_data:
                        old_purchase = item_data.get(purchase_field, 'default')
                        new_purchase = 'true' if can_purchase else 'false'
                        item_data[purchase_field] = new_purchase
                        if str(old_purchase).lower() != new_purchase:
                            changes.append(f"Purchase: {old_purchase} → {new_purchase}")

                    if changes:
                        changes_made += 1
                        change_desc = f"{trader_info['name']}: {item['code']} - {', '.join(changes)}"
                        self.add_pending_change(
                            'trader_batch_edit',
                            change_desc,
                            None,
                            None,
                            lambda: None
                        )

                dialog.destroy()

                result_msg = f"✅ TRADER OPERATION COMPLETED\n\n"
                result_msg += f"📊 Results for {trader_info['name']}:\n"
                result_msg += f"• Modified items: {changes_made}\n"
                if skipped_items > 0:
                    result_msg += f"• Skipped items: {skipped_items} (default values preserved)\n"
                result_msg += f"• Total items processed: {len(items)}\n\n"
                result_msg += "💡 Skipped items had default values (-1, null, or none) and were protected from changes as per CLI safety rules."

                messagebox.showinfo("Trader Operation Complete", result_msg)
                self.refresh_displays()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to apply changes:\n\n{str(e)}\n\nNo changes have been made to protect your file integrity.")

        # Buttons
        info_btn = ctk.CTkButton(
            button_frame,
            text="🛡️ Safety Rules",
            command=show_validation_info,
            width=120,
            fg_color="#17a2b8",
            hover_color="#138496"
        )
        info_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="✅ Apply Changes",
            command=apply_changes,
            fg_color="#28a745",
            hover_color="#218838"
        )
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Cancel",
            command=dialog.destroy,
            fg_color="#dc3545",
            hover_color="#c82333"
        )
        cancel_btn.pack(side="right", padx=5)

    def open_item_editor(self):
        """Open individual item editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        # Show item search dialog
        self.show_item_search_dialog()

    def show_item_search_dialog(self):
        """Show dialog to search and select individual items"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Search Individual Items")
        dialog.geometry("700x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # Header
        header_label = ctk.CTkLabel(
            dialog,
            text="📦 Search Individual Items",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        header_label.pack(pady=10)

        # Search controls
        search_frame = ctk.CTkFrame(dialog)
        search_frame.pack(fill="x", padx=20, pady=10)

        ctk.CTkLabel(search_frame, text="🔍 Search for items:").pack(anchor="w", padx=5, pady=2)

        search_input_frame = ctk.CTkFrame(search_frame)
        search_input_frame.pack(fill="x", padx=5, pady=5)

        search_var = ctk.StringVar()
        search_entry = ctk.CTkEntry(
            search_input_frame,
            textvariable=search_var,
            placeholder_text="Enter item code (e.g., Weapon_AK, Fish_Carp, Cal_556)"
        )
        search_entry.pack(side="left", fill="x", expand=True, padx=5)

        def search_items():
            search_term = search_var.get().strip().lower()
            if not search_term:
                messagebox.showwarning("Warning", "Please enter a search term")
                return

            # Search for matching items using correct JSON structure
            matching_items = []
            traders = self.current_data.get("economy-override", {}).get("traders", {})
            for trader_name, trader_items in traders.items():
                if isinstance(trader_items, list):
                    for item in trader_items:
                        if isinstance(item, dict):
                            item_code = item.get("tradeable-code", "")
                            if item_code and search_term in item_code.lower():
                                # Extract outpost from trader name
                                outpost_name = trader_name.split("_")[0] if "_" in trader_name else "Unknown"
                                matching_items.append({
                                    'outpost': outpost_name,
                                    'trader': trader_name,
                                    'code': item_code,
                                    'data': item
                                })

            # Update results
            update_results(matching_items)

        search_btn = ctk.CTkButton(
            search_input_frame,
            text="🔍 Search",
            command=search_items,
            width=80
        )
        search_btn.pack(side="right", padx=5)

        # Results area
        results_label = ctk.CTkLabel(
            dialog,
            text="Search results will appear here",
            font=ctk.CTkFont(size=12)
        )
        results_label.pack(pady=5)

        results_frame = ctk.CTkScrollableFrame(dialog)
        results_frame.pack(fill="both", expand=True, padx=20, pady=10)

        def update_results(items):
            # Clear existing results
            for widget in results_frame.winfo_children():
                widget.destroy()

            if not items:
                no_results = ctk.CTkLabel(
                    results_frame,
                    text="No items found matching your search",
                    font=ctk.CTkFont(size=12),
                    text_color="gray"
                )
                no_results.pack(pady=20)
                results_label.configure(text="No results found")
                return

            results_label.configure(text=f"Found {len(items)} matching items")

            def select_item(item):
                dialog.destroy()
                self.edit_individual_item(item)

            for item in items:
                item_frame = ctk.CTkFrame(results_frame)
                item_frame.pack(fill="x", pady=3)

                item_btn = ctk.CTkButton(
                    item_frame,
                    text=f"📦 {item['code']}",
                    command=lambda i=item: select_item(i),
                    height=40,
                    anchor="w"
                )
                item_btn.pack(side="left", fill="x", expand=True, padx=5, pady=5)

                # Location info
                location_frame = ctk.CTkFrame(item_frame)
                location_frame.pack(side="right", padx=5, pady=5)

                outpost_label = ctk.CTkLabel(
                    location_frame,
                    text=f"🏢 {item['outpost']}",
                    font=ctk.CTkFont(size=10),
                    text_color="gray"
                )
                outpost_label.pack(pady=1)

                trader_label = ctk.CTkLabel(
                    location_frame,
                    text=f"👤 {item['trader']}",
                    font=ctk.CTkFont(size=10),
                    text_color="gray"
                )
                trader_label.pack(pady=1)

        # Initial search for common items
        search_var.set("Weapon_")
        search_items()

        # Cancel button
        cancel_btn = ctk.CTkButton(dialog, text="❌ Cancel", command=dialog.destroy)
        cancel_btn.pack(pady=10)

    def edit_individual_item(self, item):
        """Edit individual item with full field access"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title(f"Edit Item: {item['code']}")
        dialog.geometry("600x700")
        dialog.transient(self.root)
        dialog.grab_set()

        # Header
        header_label = ctk.CTkLabel(
            dialog,
            text=f"📦 Edit Individual Item",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        header_label.pack(pady=10)

        # Item info
        info_frame = ctk.CTkFrame(dialog)
        info_frame.pack(fill="x", padx=20, pady=5)

        item_info = ctk.CTkLabel(
            info_frame,
            text=f"Item: {item['code']}\nLocation: {item['outpost']} → {item['trader']}",
            font=ctk.CTkFont(size=12),
            justify="left"
        )
        item_info.pack(pady=10)

        # Scrollable fields area
        fields_frame = ctk.CTkScrollableFrame(dialog)
        fields_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # Store field variables
        field_vars = {}

        # Create editable fields for all item data
        item_data = item['data']
        for field_name, field_value in item_data.items():
            field_frame = ctk.CTkFrame(fields_frame)
            field_frame.pack(fill="x", pady=3)

            # Field label
            field_label = ctk.CTkLabel(
                field_frame,
                text=f"{field_name}:",
                font=ctk.CTkFont(size=12, weight="bold"),
                width=150,
                anchor="w"
            )
            field_label.pack(side="left", padx=5, pady=5)

            # Field value entry
            field_var = ctk.StringVar(value=str(field_value))
            field_vars[field_name] = field_var

            field_entry = ctk.CTkEntry(
                field_frame,
                textvariable=field_var,
                width=300
            )
            field_entry.pack(side="left", fill="x", expand=True, padx=5, pady=5)

            # Lock tradeable-code field
            if field_name == 'tradeable-code':
                field_entry.configure(state="disabled")
                lock_label = ctk.CTkLabel(
                    field_frame,
                    text="🔒",
                    font=ctk.CTkFont(size=12),
                    text_color="red"
                )
                lock_label.pack(side="right", padx=5)

        # Buttons
        button_frame = ctk.CTkFrame(dialog)
        button_frame.pack(fill="x", padx=20, pady=10)

        def show_validation_info():
            info_msg = self.validation_engine.get_safe_percentage_range_info()
            messagebox.showinfo("🛡️ CLI Safety Rules & Validation", info_msg)

        def apply_changes():
            try:
                changes = []
                validation_errors = []

                # Validate and apply changes
                for field_name, field_var in field_vars.items():
                    new_value = field_var.get().strip()
                    old_value = str(item_data.get(field_name, ''))

                    if new_value != old_value:
                        # Validate specific fields
                        if field_name in ['base-purchase-price', 'base-sell-price']:
                            is_valid, result = self.validation_engine.validate_price_value(new_value)
                            if not is_valid:
                                validation_errors.append(f"{field_name}: {result}")
                                continue
                        elif field_name == 'required-famepoints':
                            is_valid, result = self.validation_engine.validate_fame_points(new_value)
                            if not is_valid:
                                validation_errors.append(f"{field_name}: {result}")
                                continue
                        elif field_name == 'can-be-purchased':
                            is_valid, result = self.validation_engine.validate_can_be_purchased(new_value)
                            if not is_valid:
                                validation_errors.append(f"{field_name}: {result}")
                                continue
                        elif field_name == 'tradeable-code':
                            is_valid, result = self.validation_engine.validate_item_code_locked(old_value, new_value)
                            if not is_valid:
                                validation_errors.append(f"{field_name}: {result}")
                                continue

                        # Apply change
                        item_data[field_name] = new_value
                        changes.append(f"{field_name}: {old_value} → {new_value}")

                if validation_errors:
                    error_message = "⚠️ VALIDATION ERRORS DETECTED ⚠️\n\n" + "\n".join([f"• {error}" for error in validation_errors])
                    error_message += f"\n\n{self.validation_engine.get_safe_percentage_range_info()}"
                    messagebox.showerror("Validation Error", error_message)
                    return

                if not changes:
                    messagebox.showinfo("No Changes", "No changes were made to the item")
                    return

                # Add to pending changes
                change_desc = f"Individual Item: {item['code']} - {', '.join(changes)}"
                self.add_pending_change(
                    'individual_item_edit',
                    change_desc,
                    None,
                    None,
                    lambda: None
                )

                dialog.destroy()

                result_msg = f"✅ INDIVIDUAL ITEM UPDATED\n\n"
                result_msg += f"📦 Item: {item['code']}\n"
                result_msg += f"📍 Location: {item['outpost']} → {item['trader']}\n\n"
                result_msg += f"📊 Changes made:\n"
                for change in changes:
                    result_msg += f"• {change}\n"

                messagebox.showinfo("Item Updated", result_msg)
                self.refresh_displays()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to apply changes:\n\n{str(e)}\n\nNo changes have been made to protect your file integrity.")

        # Buttons
        info_btn = ctk.CTkButton(
            button_frame,
            text="🛡️ Safety Rules",
            command=show_validation_info,
            width=120,
            fg_color="#17a2b8",
            hover_color="#138496"
        )
        info_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="✅ Apply Changes",
            command=apply_changes,
            fg_color="#28a745",
            hover_color="#218838"
        )
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Cancel",
            command=dialog.destroy,
            fg_color="#dc3545",
            hover_color="#c82333"
        )
        cancel_btn.pack(side="right", padx=5)

    def open_category_editor(self, category_key):
        """Open simplified category editor for normal mode"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        # Category definitions for normal mode
        category_definitions = {
            "fish": {
                "name": "🐟 Fish Items",
                "filter": lambda code: code.startswith('Fish_') and not any(x in code.lower() for x in ['fishing', 'rod']),
                "description": "All fish items for consumption"
            },
            "weapons": {
                "name": "🔫 Weapons",
                "filter": lambda code: code.startswith('Weapon_') and not any(x in code.lower() for x in ['parts', 'attachment']),
                "description": "All combat weapons"
            },
            "improvised": {
                "name": "🔧 Improvised Items",
                "filter": lambda code: 'improvised' in code.lower() or any(x in code for x in ['1H_Improvised', '2H_Improvised', 'Weapon_Improvised']),
                "description": "Player-crafted improvised weapons and tools"
            },
            "crafted": {
                "name": "🛠️ Crafted Items",
                "filter": lambda code: any(x in code.lower() for x in ['crafted', 'handmade', 'makeshift']) or code.startswith('Craft_'),
                "description": "Items that can be crafted by players"
            },
            "two_handed": {
                "name": "⚔️ Two-Handed Weapons",
                "filter": lambda code: code.startswith('2H_') or '2h_' in code.lower(),
                "description": "Two-handed weapons and tools"
            },
            "medical": {
                "name": "💊 Medical Items",
                "filter": lambda code: any(x in code.lower() for x in ['bandage', 'pill', 'syringe', 'medical']),
                "description": "Medical supplies and healing items"
            },
            "military": {
                "name": "🎖️ Military Gear",
                "filter": lambda code: any(x in code for x in ['Military', 'Army', 'Combat']) and not code.startswith('Weapon_'),
                "description": "Military equipment (non-weapon)"
            }
        }

        if category_key not in category_definitions:
            messagebox.showerror("Error", f"Unknown category: {category_key}")
            return

        category = category_definitions[category_key]

        # Find matching items using correct JSON structure
        matching_items = []
        traders = self.current_data.get("economy-override", {}).get("traders", {})
        for trader_name, trader_items in traders.items():
            if isinstance(trader_items, list):
                for item in trader_items:
                    if isinstance(item, dict):
                        item_code = item.get("tradeable-code", "")
                        if item_code and category['filter'](item_code):
                            # Extract outpost from trader name (e.g., "Z1_Trader_Food" -> "Z1")
                            outpost_name = trader_name.split("_")[0] if "_" in trader_name else "Unknown"
                            matching_items.append({
                                'outpost': outpost_name,
                                'trader': trader_name,
                                'code': item_code,
                                'data': item
                            })

        if not matching_items:
            messagebox.showinfo("No Items", f"No items found in category: {category['name']}")
            return

        # Open simplified category editor dialog
        self.show_simple_category_editor(category, matching_items)

    def show_simple_category_editor(self, category, items):
        """Show simplified category editor dialog"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title(f"Edit {category['name']} - {len(items)} items")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # Header
        header_label = ctk.CTkLabel(
            dialog,
            text=f"{category['name']} Editor",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        header_label.pack(pady=10)

        info_label = ctk.CTkLabel(
            dialog,
            text=f"{category['description']}\nFound {len(items)} matching items",
            font=ctk.CTkFont(size=12)
        )
        info_label.pack(pady=5)

        # Items list with scrollable frame
        list_frame = ctk.CTkFrame(dialog)
        list_frame.pack(fill="both", expand=True, padx=20, pady=10)

        list_title = ctk.CTkLabel(
            list_frame,
            text="📦 Items in this category:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        list_title.pack(pady=5)

        # Scrollable frame for items
        items_scrollable = ctk.CTkScrollableFrame(list_frame, height=200)
        items_scrollable.pack(fill="both", expand=True, padx=10, pady=5)

        # Display items in a grid
        for i, item in enumerate(items):
            item_frame = ctk.CTkFrame(items_scrollable)
            item_frame.pack(fill="x", padx=5, pady=2)

            # Item info
            item_text = f"🔹 {item['code']}"
            if 'base-purchase-price' in item['data']:
                price = item['data']['base-purchase-price']
                item_text += f" - 💰 {price}"
            item_text += f" ({item['trader']})"

            item_label = ctk.CTkLabel(
                item_frame,
                text=item_text,
                anchor="w"
            )
            item_label.pack(side="left", fill="x", expand=True, padx=10, pady=5)

            # Add to bucket button
            add_bucket_btn = ctk.CTkButton(
                item_frame,
                text="🪣 Add to Bucket",
                command=lambda item=item: self.add_item_to_bucket_dialog(item),
                width=120,
                height=25,
                fg_color="#6c757d",
                hover_color="#5a6268"
            )
            add_bucket_btn.pack(side="right", padx=5, pady=2)

        # Batch editing controls
        controls_frame = ctk.CTkFrame(dialog)
        controls_frame.pack(fill="x", padx=20, pady=10)

        # Price adjustment
        price_frame = ctk.CTkFrame(controls_frame)
        price_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(price_frame, text="💰 Price Adjustment:").pack(side="left", padx=5)
        price_var = ctk.StringVar(value="0")
        price_entry = ctk.CTkEntry(price_frame, textvariable=price_var, width=100)
        price_entry.pack(side="left", padx=5)
        ctk.CTkLabel(price_frame, text="% (e.g., +10 or -20)").pack(side="left", padx=5)

        # Fame points
        fame_frame = ctk.CTkFrame(controls_frame)
        fame_frame.pack(fill="x", pady=5)

        ctk.CTkLabel(fame_frame, text="⭐ Fame Points:").pack(side="left", padx=5)
        fame_var = ctk.StringVar(value="")
        fame_entry = ctk.CTkEntry(fame_frame, textvariable=fame_var, width=100)
        fame_entry.pack(side="left", padx=5)
        ctk.CTkLabel(fame_frame, text="(leave empty to keep current)").pack(side="left", padx=5)

        # Can be purchased
        purchase_var = ctk.BooleanVar(value=True)
        purchase_check = ctk.CTkCheckBox(
            controls_frame,
            text="🛒 Can be purchased",
            variable=purchase_var
        )
        purchase_check.pack(pady=5)

        # Buttons
        button_frame = ctk.CTkFrame(dialog)
        button_frame.pack(fill="x", padx=20, pady=10)

        def show_validation_info():
            """Show CLI validation rules and safety information"""
            info_msg = self.validation_engine.get_safe_percentage_range_info()
            messagebox.showinfo("🛡️ CLI Safety Rules & Validation", info_msg)

        def apply_changes():
            try:
                price_change = price_var.get().strip()
                fame_points = fame_var.get().strip()
                can_purchase = purchase_var.get()

                # Validate inputs using CLI-compatible validation
                validation_errors = []

                # Validate percentage if provided
                if price_change and price_change != "0":
                    if price_change.startswith(('+', '-')):
                        is_valid, result = self.validation_engine.validate_percentage(price_change)
                        if not is_valid:
                            validation_errors.append(f"Price change: {result}")
                    else:
                        is_valid, result = self.validation_engine.validate_price_value(price_change)
                        if not is_valid:
                            validation_errors.append(f"Price value: {result}")

                # Validate fame points if provided
                if fame_points:
                    is_valid, result = self.validation_engine.validate_fame_points(fame_points)
                    if not is_valid:
                        validation_errors.append(f"Fame points: {result}")

                # Show validation errors if any
                if validation_errors:
                    error_message = "⚠️ VALIDATION ERRORS DETECTED ⚠️\n\n" + "\n".join([f"• {error}" for error in validation_errors])
                    error_message += f"\n\n{self.validation_engine.get_safe_percentage_range_info()}"
                    messagebox.showerror("Validation Error", error_message)
                    return

                # Show safety warning for large changes
                if price_change and price_change != "0":
                    try:
                        if price_change.startswith(('+', '-')):
                            percentage = float(price_change)
                            if abs(percentage) > 50:
                                warning_msg = f"⚠️ LARGE CHANGE WARNING ⚠️\n\nYou are about to apply a {percentage}% change to {len(items)} items.\n\nThis is a significant modification that could drastically affect your economy.\n\nCLI Safety Rules:\n• Changes are limited to -99% to +100%\n• Values set to -1 (default) will be skipped\n• This prevents prices from going to zero\n\nDo you want to proceed with this large change?"
                                if not messagebox.askyesno("Large Change Warning", warning_msg):
                                    return
                    except ValueError:
                        pass

                changes_made = 0
                skipped_items = 0

                for item in items:
                    item_data = item['data']
                    changes = []

                    # Apply price change with CLI logic
                    if price_change and price_change != "0":
                        if price_change.startswith(('+', '-')):
                            percentage = float(price_change)
                            # Check for base-purchase-price field (CLI uses this field name)
                            price_field = None
                            if 'base-purchase-price' in item_data:
                                price_field = 'base-purchase-price'
                            elif 'BuyPrice' in item_data:
                                price_field = 'BuyPrice'

                            if price_field and item_data[price_field] not in ['-1', 'null', None, '-1.0']:
                                try:
                                    old_price = float(item_data[price_field])
                                    if old_price > 0:  # Only modify positive prices
                                        new_price = round(old_price * (1 + percentage / 100), 2)
                                        # Ensure price doesn't go below 1 (CLI protection)
                                        if new_price < 1:
                                            new_price = 1
                                        item_data[price_field] = str(new_price)
                                        changes.append(f"Price: {old_price} → {new_price}")
                                    else:
                                        skipped_items += 1
                                except (ValueError, TypeError):
                                    skipped_items += 1
                            else:
                                skipped_items += 1  # Skip default values as per CLI logic
                        else:
                            # Direct price setting
                            new_price = float(price_change)
                            price_field = 'base-purchase-price' if 'base-purchase-price' in item_data else 'BuyPrice'
                            if price_field in item_data:
                                old_price = item_data[price_field]
                                item_data[price_field] = str(new_price)
                                changes.append(f"Price: {old_price} → {new_price}")

                    # Apply fame points with CLI validation
                    if fame_points:
                        fame_field = 'required-famepoints' if 'required-famepoints' in item_data else 'RequiredFamePoints'
                        if fame_field in item_data:
                            old_fame = item_data.get(fame_field, -1)
                            new_fame = int(fame_points)
                            item_data[fame_field] = str(new_fame)
                            changes.append(f"Fame: {old_fame} → {new_fame}")

                    # Apply purchase setting with CLI validation
                    purchase_field = 'can-be-purchased' if 'can-be-purchased' in item_data else 'CanBePurchased'
                    if purchase_field in item_data:
                        old_purchase = item_data.get(purchase_field, 'default')
                        new_purchase = 'true' if can_purchase else 'false'
                        item_data[purchase_field] = new_purchase
                        if str(old_purchase).lower() != new_purchase:
                            changes.append(f"Purchase: {old_purchase} → {new_purchase}")

                    if changes:
                        changes_made += 1
                        # Add to pending changes
                        change_desc = f"{category['name']}: {item['code']} - {', '.join(changes)}"
                        self.add_pending_change(
                            'category_batch_edit',
                            change_desc,
                            None,  # We don't track before/after for batch operations in normal mode
                            None,
                            lambda: None  # Changes already applied above
                        )

                dialog.destroy()

                # Show results with CLI-style messaging
                result_msg = f"✅ BATCH OPERATION COMPLETED\n\n"
                result_msg += f"📊 Results for {category['name']}:\n"
                result_msg += f"• Modified items: {changes_made}\n"
                if skipped_items > 0:
                    result_msg += f"• Skipped items: {skipped_items} (default values preserved)\n"
                result_msg += f"• Total items processed: {len(items)}\n\n"
                result_msg += "💡 Skipped items had default values (-1, null, or none) and were protected from changes as per CLI safety rules."

                messagebox.showinfo("Batch Operation Complete", result_msg)
                self.refresh_displays()

            except ValueError as e:
                messagebox.showerror("Validation Error", f"Invalid input detected:\n\n{str(e)}\n\nPlease check your values and try again.")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to apply changes:\n\n{str(e)}\n\nNo changes have been made to protect your file integrity.")

        # Info button for validation rules
        info_btn = ctk.CTkButton(
            button_frame,
            text="🛡️ Safety Rules",
            command=show_validation_info,
            width=120,
            fg_color="#17a2b8",
            hover_color="#138496"
        )
        info_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="✅ Apply Changes",
            command=apply_changes,
            fg_color="#28a745",
            hover_color="#218838"
        )
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Cancel",
            command=dialog.destroy,
            fg_color="#dc3545",
            hover_color="#c82333"
        )
        cancel_btn.pack(side="right", padx=5)

    def add_item_to_bucket_dialog(self, item):
        """Show dialog to add item to a custom bucket"""
        if not self.custom_buckets:
            messagebox.showinfo("No Buckets", "No custom buckets available. Create a bucket first.")
            return

        # Create selection dialog
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Add to Bucket")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # Header
        header_label = ctk.CTkLabel(
            dialog,
            text=f"Add {item['code']} to bucket:",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        header_label.pack(pady=10)

        # Bucket selection
        bucket_frame = ctk.CTkScrollableFrame(dialog, height=150)
        bucket_frame.pack(fill="both", expand=True, padx=20, pady=10)

        selected_bucket = ctk.StringVar()

        for bucket_name, bucket_data in self.custom_buckets.items():
            bucket_radio = ctk.CTkRadioButton(
                bucket_frame,
                text=f"🪣 {bucket_name} ({len(bucket_data.get('items', []))} items)",
                variable=selected_bucket,
                value=bucket_name
            )
            bucket_radio.pack(anchor="w", pady=2)

        # Buttons
        button_frame = ctk.CTkFrame(dialog)
        button_frame.pack(fill="x", padx=20, pady=10)

        def add_to_bucket():
            bucket_name = selected_bucket.get()
            if not bucket_name:
                messagebox.showwarning("No Selection", "Please select a bucket.")
                return

            # Add item to bucket
            if 'items' not in self.custom_buckets[bucket_name]:
                self.custom_buckets[bucket_name]['items'] = []

            # Check if item already exists
            item_exists = any(
                existing_item['code'] == item['code'] and existing_item['trader'] == item['trader']
                for existing_item in self.custom_buckets[bucket_name]['items']
            )

            if item_exists:
                messagebox.showinfo("Already Added", f"Item {item['code']} is already in bucket {bucket_name}.")
            else:
                self.custom_buckets[bucket_name]['items'].append(item)
                self.save_custom_buckets()
                self.refresh_bucket_buttons()
                self.refresh_advanced_bucket_buttons()
                messagebox.showinfo("Added", f"Item {item['code']} added to bucket {bucket_name}.")

            dialog.destroy()

        add_btn = ctk.CTkButton(
            button_frame,
            text="✅ Add to Bucket",
            command=add_to_bucket,
            fg_color="#28a745",
            hover_color="#218838"
        )
        add_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Cancel",
            command=dialog.destroy,
            fg_color="#dc3545",
            hover_color="#c82333"
        )
        cancel_btn.pack(side="right", padx=5)

    def create_global_tools(self, parent):
        """Create global editing tools"""
        global_frame = ctk.CTkFrame(parent)
        global_frame.pack(fill="x", pady=5)

        global_title = ctk.CTkLabel(
            global_frame,
            text="🌍 Global Operations",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        global_title.pack(pady=5)

        # Global price changes
        global_price_btn = ctk.CTkButton(
            global_frame,
            text="💰 Global Price Changes",
            command=self.open_global_price_changes,
            height=40
        )
        global_price_btn.pack(fill="x", padx=10, pady=2)

        # Economy fields
        economy_fields_btn = ctk.CTkButton(
            global_frame,
            text="⚙️ Economy Fields",
            command=self.open_economy_fields,
            height=40
        )
        economy_fields_btn.pack(fill="x", padx=10, pady=2)

    def create_merchant_tools(self, parent):
        """Create merchant-specific tools"""
        merchant_frame = ctk.CTkFrame(parent)
        merchant_frame.pack(fill="x", pady=5)

        merchant_title = ctk.CTkLabel(
            merchant_frame,
            text="👤 Merchant Operations",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        merchant_title.pack(pady=5)

        # Individual merchant editing
        merchant_btn = ctk.CTkButton(
            merchant_frame,
            text="👤 Edit Merchant Level",
            command=self.open_merchant_level,
            height=40
        )
        merchant_btn.pack(fill="x", padx=10, pady=2)

        # Outpost level editing
        outpost_btn = ctk.CTkButton(
            merchant_frame,
            text="🏢 Edit Outpost Level",
            command=self.open_outpost_level,
            height=40
        )
        outpost_btn.pack(fill="x", padx=10, pady=2)

        # Fine tuning
        fine_tune_btn = ctk.CTkButton(
            merchant_frame,
            text="🔧 Fine Tune Items",
            command=self.open_fine_tune,
            height=40
        )
        fine_tune_btn.pack(fill="x", padx=10, pady=2)

    def create_advanced_tools(self, parent):
        """Create advanced tools"""
        # Custom buckets section (also available in advanced mode)
        buckets_frame = ctk.CTkFrame(parent)
        buckets_frame.pack(fill="x", pady=5)

        # Header with bucket management
        bucket_header = ctk.CTkFrame(buckets_frame)
        bucket_header.pack(fill="x", padx=5, pady=5)

        bucket_title = ctk.CTkLabel(
            bucket_header,
            text="🪣 Custom Buckets (Advanced)",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        bucket_title.pack(side="left", padx=10, pady=5)

        # Bucket management buttons
        bucket_mgmt_frame = ctk.CTkFrame(bucket_header)
        bucket_mgmt_frame.pack(side="right", padx=5)

        create_bucket_btn = ctk.CTkButton(
            bucket_mgmt_frame,
            text="➕ Create",
            command=self.create_custom_bucket,
            width=80,
            height=30
        )
        create_bucket_btn.pack(side="left", padx=2)

        manage_bucket_btn = ctk.CTkButton(
            bucket_mgmt_frame,
            text="⚙️ Manage",
            command=lambda: self.manage_custom_buckets(advanced_mode=True),
            width=80,
            height=30
        )
        manage_bucket_btn.pack(side="left", padx=2)

        # Dynamic bucket buttons (same as normal mode)
        self.advanced_bucket_buttons_frame = ctk.CTkFrame(buckets_frame)
        self.advanced_bucket_buttons_frame.pack(fill="x", padx=5, pady=5)

        self.refresh_advanced_bucket_buttons()

        # Advanced tools section
        advanced_frame = ctk.CTkFrame(parent)
        advanced_frame.pack(fill="x", pady=5)

        advanced_title = ctk.CTkLabel(
            advanced_frame,
            text="🔬 Advanced Tools",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        advanced_title.pack(pady=5)

        # Spread editing
        spread_btn = ctk.CTkButton(
            advanced_frame,
            text="📊 Spread Edit Items",
            command=self.open_spread_edit,
            height=40
        )
        spread_btn.pack(fill="x", padx=10, pady=2)

        # Purchase settings
        purchase_btn = ctk.CTkButton(
            advanced_frame,
            text="🛒 Purchase Settings",
            command=self.open_purchase_settings,
            height=40
        )
        purchase_btn.pack(fill="x", padx=10, pady=2)

    def create_fish_tools(self, parent):
        """Create F.I.S.H. Logic tools"""
        fish_frame = ctk.CTkFrame(parent)
        fish_frame.pack(fill="x", pady=5)

        fish_title = ctk.CTkLabel(
            fish_frame,
            text="🐟 F.I.S.H. Logic Tools",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        fish_title.pack(pady=5)

        # F.I.S.H. Analysis
        analysis_btn = ctk.CTkButton(
            fish_frame,
            text="🔍 F.I.S.H. Analysis",
            command=self.open_fish_analysis,
            height=40
        )
        analysis_btn.pack(fill="x", padx=10, pady=2)

        # Smart Categories
        categories_btn = ctk.CTkButton(
            fish_frame,
            text="📂 Smart Categories",
            command=self.open_smart_categories,
            height=40
        )
        categories_btn.pack(fill="x", padx=10, pady=2)

        # Scenario Rules
        rules_btn = ctk.CTkButton(
            fish_frame,
            text="📋 Scenario Rules",
            command=self.open_scenario_rules,
            height=40
        )
        rules_btn.pack(fill="x", padx=10, pady=2)

    def create_preview_panel(self):
        """Create the preview and details panel"""
        # Preview title
        preview_title = ctk.CTkLabel(
            self.right_panel,
            text="📊 Data Preview & Analysis",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        preview_title.pack(pady=10)

        # Tabview for different views
        self.tabview = ctk.CTkTabview(self.right_panel)
        self.tabview.pack(fill="both", expand=True, padx=10, pady=10)

        # Add tabs
        self.tabview.add("📈 Overview")
        self.tabview.add("🌳 Data Tree")
        self.tabview.add("📋 Changes")
        self.tabview.add("🐟 F.I.S.H.")
        self.tabview.add("📊 Statistics")

        self.create_overview_tab()
        self.create_tree_tab()
        self.create_changes_tab()
        self.create_fish_tab()
        self.create_stats_tab()

    def create_overview_tab(self):
        """Create overview tab"""
        overview_frame = self.tabview.tab("📈 Overview")

        self.overview_text = ctk.CTkTextbox(overview_frame)
        self.overview_text.pack(fill="both", expand=True, padx=10, pady=10)

        # Initial content
        self.overview_text.insert("1.0", """
🎮 SCUM Economy Chooser - Enhanced GUI v1.45c

Welcome to the enhanced economy editor with F.I.S.H. Logic!

📁 Getting Started:
1. Load your economyoverride.json file
2. Choose an editing tool from the left panel
3. Preview changes in the Changes tab
4. Apply changes with proper undo/redo support

🌍 Global Operations:
- Adjust all prices by percentage
- Modify economy configuration

👤 Merchant Operations:
- Edit individual merchants
- Modify entire outposts
- Fine-tune specific items

🔬 Advanced Tools:
- Spread changes across traders
- Manage purchase settings

🐟 F.I.S.H. Logic Features:
- Flexible Intelligence for Scenario Handling
- Smart categorization with priority rules
- Automated analysis and recommendations
- Scenario-based rule engine

🔄 Undo/Redo System:
- Full operation history tracking
- Visual before/after comparisons
- Safe experimentation with rollback

💡 Tips:
- Always backup your files before editing
- Use F.I.S.H. Analysis for insights
- Preview changes before applying
- Check the Changes tab for pending modifications
        """)
        self.overview_text.configure(state="disabled")

    def create_tree_tab(self):
        """Create data tree tab"""
        tree_frame = self.tabview.tab("🌳 Data Tree")

        # Search frame
        search_frame = ctk.CTkFrame(tree_frame)
        search_frame.pack(fill="x", padx=10, pady=5)

        search_label = ctk.CTkLabel(search_frame, text="🔍 Search:")
        search_label.pack(side="left", padx=5)

        self.search_entry = ctk.CTkEntry(search_frame, placeholder_text="Enter item code...")
        self.search_entry.pack(side="left", fill="x", expand=True, padx=5)

        search_btn = ctk.CTkButton(search_frame, text="Go", command=self.search_items, width=60)
        search_btn.pack(side="right", padx=5)

        # Tree view
        tree_container = ctk.CTkFrame(tree_frame)
        tree_container.pack(fill="both", expand=True, padx=10, pady=5)

        # Create treeview with tkinter (CustomTkinter doesn't have treeview yet)
        self.tree = ttk.Treeview(tree_container, height=20)
        self.tree.heading("#0", text="Outpost > Trader > Item")

        # Configure tree colors and styles
        style = ttk.Style()
        style.theme_use('clam')

        # Configure colors for different item types
        style.configure("Treeview",
                       background="#2b2b2b",
                       foreground="#ffffff",
                       fieldbackground="#2b2b2b",
                       borderwidth=0)
        style.configure("Treeview.Heading",
                       background="#1f538d",
                       foreground="#ffffff",
                       borderwidth=1)
        style.map("Treeview.Heading",
                 background=[('active', '#1f538d')])

        # Configure tags for colored icons
        self.tree.tag_configure("outpost", foreground="#28a745")  # Green for outposts
        self.tree.tag_configure("trader", foreground="#17a2b8")   # Blue for traders
        self.tree.tag_configure("weapon", foreground="#dc3545")   # Red for weapons
        self.tree.tag_configure("fish", foreground="#20c997")     # Teal for fish
        self.tree.tag_configure("medical", foreground="#e83e8c")  # Pink for medical
        self.tree.tag_configure("military", foreground="#6f42c1") # Purple for military
        self.tree.tag_configure("improvised", foreground="#fd7e14") # Orange for improvised
        self.tree.tag_configure("crafted", foreground="#ffc107")  # Yellow for crafted
        self.tree.tag_configure("item", foreground="#6c757d")     # Gray for other items

        self.tree.pack(fill="both", expand=True, padx=5, pady=5)

        # Bind selection event
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)

    def create_changes_tab(self):
        """Create enhanced changes preview tab with visual tracking"""
        changes_frame = self.tabview.tab("📋 Changes")

        # Changes header with statistics
        changes_header = ctk.CTkFrame(changes_frame)
        changes_header.pack(fill="x", padx=10, pady=5)

        # Left side - title and stats
        header_left = ctk.CTkFrame(changes_header)
        header_left.pack(side="left", fill="x", expand=True)

        changes_title = ctk.CTkLabel(
            header_left,
            text="📋 Change Tracker",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        changes_title.pack(anchor="w", padx=10, pady=5)

        # Statistics display
        self.changes_stats_frame = ctk.CTkFrame(header_left)
        self.changes_stats_frame.pack(fill="x", padx=10, pady=2)

        self.changes_count_label = ctk.CTkLabel(
            self.changes_stats_frame,
            text="� 0 pending changes",
            font=ctk.CTkFont(size=12)
        )
        self.changes_count_label.pack(side="left", padx=5)

        self.changes_types_label = ctk.CTkLabel(
            self.changes_stats_frame,
            text="🏷️ No types",
            font=ctk.CTkFont(size=12)
        )
        self.changes_types_label.pack(side="left", padx=15)

        # Right side - action buttons
        button_frame = ctk.CTkFrame(changes_header)
        button_frame.pack(side="right", pady=5)

        # View mode toggle
        self.view_mode_var = ctk.StringVar(value="detailed")
        view_toggle = ctk.CTkSegmentedButton(
            button_frame,
            values=["Summary", "Detailed", "Timeline"],
            variable=self.view_mode_var,
            command=self.update_changes_view
        )
        view_toggle.pack(side="left", padx=5)

        self.apply_all_btn = ctk.CTkButton(
            button_frame,
            text="✅ Apply All",
            command=self.apply_all_changes,
            width=100,
            state="disabled",
            fg_color="#28a745",
            hover_color="#218838"
        )
        self.apply_all_btn.pack(side="left", padx=2)

        self.clear_all_btn = ctk.CTkButton(
            button_frame,
            text="❌ Clear All",
            command=self.clear_all_changes,
            width=100,
            state="disabled",
            fg_color="#dc3545",
            hover_color="#c82333"
        )
        self.clear_all_btn.pack(side="left", padx=2)

        # Enhanced changes display with scrollable frame
        changes_container = ctk.CTkFrame(changes_frame)
        changes_container.pack(fill="both", expand=True, padx=10, pady=5)

        # Scrollable frame for individual change cards
        self.changes_scroll_frame = ctk.CTkScrollableFrame(changes_container)
        self.changes_scroll_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # Initial welcome message
        self.create_welcome_message()

        # Change cards list
        self.change_cards = {}

    def create_welcome_message(self):
        """Create welcome message for empty changes tab"""
        welcome_frame = ctk.CTkFrame(self.changes_scroll_frame)
        welcome_frame.pack(fill="x", padx=10, pady=20)

        # Welcome icon and title
        welcome_title = ctk.CTkLabel(
            welcome_frame,
            text="🎯 Change Tracker Ready",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        welcome_title.pack(pady=10)

        # Instructions
        instructions = ctk.CTkLabel(
            welcome_frame,
            text="Make changes using the tools in the left panel.\nAll modifications will be tracked here with visual before/after comparisons.",
            font=ctk.CTkFont(size=12),
            justify="center"
        )
        instructions.pack(pady=5)

        # Feature highlights
        features_frame = ctk.CTkFrame(welcome_frame)
        features_frame.pack(fill="x", padx=20, pady=10)

        features = [
            "📊 Visual change cards with before/after comparison",
            "🏷️ Change type categorization and filtering",
            "⏰ Timeline view with timestamps",
            "✅ Individual change approval/rejection",
            "🔄 Batch operations with undo/redo support"
        ]

        for feature in features:
            feature_label = ctk.CTkLabel(
                features_frame,
                text=feature,
                font=ctk.CTkFont(size=11),
                anchor="w"
            )
            feature_label.pack(fill="x", padx=10, pady=2)

        self.welcome_frame = welcome_frame

    def update_changes_view(self, view_mode=None):
        """Update the changes display based on view mode"""
        if view_mode:
            self.view_mode_var.set(view_mode)

        mode = self.view_mode_var.get().lower()

        # Clear existing display
        for widget in self.changes_scroll_frame.winfo_children():
            widget.destroy()

        if not self.pending_changes:
            self.create_welcome_message()
            return

        if mode == "summary":
            self.create_summary_view()
        elif mode == "detailed":
            self.create_detailed_view()
        elif mode == "timeline":
            self.create_timeline_view()

    def create_summary_view(self):
        """Create summary view of changes"""
        summary_frame = ctk.CTkFrame(self.changes_scroll_frame)
        summary_frame.pack(fill="x", padx=10, pady=5)

        # Summary statistics
        stats_title = ctk.CTkLabel(
            summary_frame,
            text="📊 Changes Summary",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        stats_title.pack(pady=10)

        # Group changes by type
        change_types = {}
        for change in self.pending_changes.values():
            change_type = change['type']
            if change_type not in change_types:
                change_types[change_type] = []
            change_types[change_type].append(change)

        # Display type summaries
        for change_type, changes in change_types.items():
            type_frame = ctk.CTkFrame(summary_frame)
            type_frame.pack(fill="x", padx=10, pady=5)

            type_icon = self.get_change_type_icon(change_type)
            type_color = self.get_change_type_color(change_type)

            type_label = ctk.CTkLabel(
                type_frame,
                text=f"{type_icon} {change_type.replace('_', ' ').title()}: {len(changes)} changes",
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color=type_color
            )
            type_label.pack(side="left", padx=10, pady=10)

            # Show sample descriptions
            sample_text = ", ".join([change['description'][:30] + "..." if len(change['description']) > 30
                                   else change['description'] for change in changes[:3]])
            if len(changes) > 3:
                sample_text += f" and {len(changes) - 3} more..."

            sample_label = ctk.CTkLabel(
                type_frame,
                text=sample_text,
                font=ctk.CTkFont(size=11),
                text_color="gray"
            )
            sample_label.pack(side="left", padx=10, pady=10)

    def create_detailed_view(self):
        """Create detailed view with individual change cards"""
        for change_id, change in self.pending_changes.items():
            self.create_change_card(change_id, change)

    def create_timeline_view(self):
        """Create timeline view of changes"""
        timeline_frame = ctk.CTkFrame(self.changes_scroll_frame)
        timeline_frame.pack(fill="x", padx=10, pady=5)

        timeline_title = ctk.CTkLabel(
            timeline_frame,
            text="⏰ Changes Timeline",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        timeline_title.pack(pady=10)

        # Sort changes by timestamp
        sorted_changes = sorted(self.pending_changes.items(),
                              key=lambda x: x[1]['timestamp'], reverse=True)

        for i, (change_id, change) in enumerate(sorted_changes):
            timeline_item = ctk.CTkFrame(timeline_frame)
            timeline_item.pack(fill="x", padx=10, pady=2)

            # Timeline connector
            if i < len(sorted_changes) - 1:
                connector = ctk.CTkLabel(timeline_item, text="│", text_color="gray")
                connector.pack(side="left", padx=5)
            else:
                connector = ctk.CTkLabel(timeline_item, text="└", text_color="gray")
                connector.pack(side="left", padx=5)

            # Change info
            change_info = ctk.CTkFrame(timeline_item)
            change_info.pack(side="left", fill="x", expand=True, padx=5, pady=5)

            # Time and type
            time_type_label = ctk.CTkLabel(
                change_info,
                text=f"🕒 {change['timestamp']} - {self.get_change_type_icon(change['type'])} {change['type'].replace('_', ' ').title()}",
                font=ctk.CTkFont(size=12, weight="bold")
            )
            time_type_label.pack(anchor="w", padx=10, pady=2)

            # Description
            desc_label = ctk.CTkLabel(
                change_info,
                text=change['description'],
                font=ctk.CTkFont(size=11),
                anchor="w"
            )
            desc_label.pack(anchor="w", padx=10, pady=2)

    def create_change_card(self, change_id, change):
        """Create a visual change card"""
        card_frame = ctk.CTkFrame(self.changes_scroll_frame)
        card_frame.pack(fill="x", padx=10, pady=5)

        # Card header
        header_frame = ctk.CTkFrame(card_frame)
        header_frame.pack(fill="x", padx=5, pady=5)

        # Change type icon and title
        type_icon = self.get_change_type_icon(change['type'])
        type_color = self.get_change_type_color(change['type'])

        header_left = ctk.CTkFrame(header_frame)
        header_left.pack(side="left", fill="x", expand=True)

        title_label = ctk.CTkLabel(
            header_left,
            text=f"{type_icon} {change['description']}",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=type_color,
            anchor="w"
        )
        title_label.pack(side="left", padx=10, pady=5)

        # Timestamp
        time_label = ctk.CTkLabel(
            header_left,
            text=f"🕒 {change['timestamp']}",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        time_label.pack(side="left", padx=10, pady=5)

        # Individual action buttons
        actions_frame = ctk.CTkFrame(header_frame)
        actions_frame.pack(side="right", padx=5)

        apply_btn = ctk.CTkButton(
            actions_frame,
            text="✅",
            command=lambda: self.apply_individual_change(change_id),
            width=30,
            height=30,
            fg_color="#28a745",
            hover_color="#218838"
        )
        apply_btn.pack(side="left", padx=2)

        remove_btn = ctk.CTkButton(
            actions_frame,
            text="❌",
            command=lambda: self.remove_individual_change(change_id),
            width=30,
            height=30,
            fg_color="#dc3545",
            hover_color="#c82333"
        )
        remove_btn.pack(side="left", padx=2)

        # Before/After comparison (if available)
        if 'before' in change and 'after' in change:
            comparison_frame = ctk.CTkFrame(card_frame)
            comparison_frame.pack(fill="x", padx=5, pady=5)

            # Before section
            before_frame = ctk.CTkFrame(comparison_frame)
            before_frame.pack(side="left", fill="both", expand=True, padx=2)

            before_title = ctk.CTkLabel(
                before_frame,
                text="👁️ Before",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color="#ffc107"
            )
            before_title.pack(pady=5)

            before_text = ctk.CTkTextbox(before_frame, height=60)
            before_text.pack(fill="both", expand=True, padx=5, pady=5)
            before_text.insert("1.0", str(change['before']))
            before_text.configure(state="disabled")

            # Arrow
            arrow_label = ctk.CTkLabel(
                comparison_frame,
                text="→",
                font=ctk.CTkFont(size=20, weight="bold"),
                text_color="#17a2b8"
            )
            arrow_label.pack(side="left", padx=10)

            # After section
            after_frame = ctk.CTkFrame(comparison_frame)
            after_frame.pack(side="left", fill="both", expand=True, padx=2)

            after_title = ctk.CTkLabel(
                after_frame,
                text="✨ After",
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color="#28a745"
            )
            after_title.pack(pady=5)

            after_text = ctk.CTkTextbox(after_frame, height=60)
            after_text.pack(fill="both", expand=True, padx=5, pady=5)
            after_text.insert("1.0", str(change['after']))
            after_text.configure(state="disabled")

        # Store card reference
        self.change_cards[change_id] = card_frame

    def get_change_type_icon(self, change_type):
        """Get icon for change type"""
        icons = {
            'global_price_change': '🌍',
            'economy_fields': '⚙️',
            'merchant_level': '👤',
            'outpost_level': '🏢',
            'fine_tune': '🔧',
            'spread_edit': '📊',
            'purchase_settings': '🛒',
            'fish_analysis': '🐟'
        }
        return icons.get(change_type, '📝')

    def get_change_type_color(self, change_type):
        """Get color for change type"""
        colors = {
            'global_price_change': '#ff6b35',
            'economy_fields': '#6c757d',
            'merchant_level': '#17a2b8',
            'outpost_level': '#28a745',
            'fine_tune': '#ffc107',
            'spread_edit': '#6f42c1',
            'purchase_settings': '#fd7e14',
            'fish_analysis': '#20c997'
        }
        return colors.get(change_type, '#6c757d')

    def apply_individual_change(self, change_id):
        """Apply a single change"""
        if change_id in self.pending_changes:
            change = self.pending_changes[change_id]
            try:
                change['apply_function']()
                # Remove from pending changes
                del self.pending_changes[change_id]
                self.update_changes_display()
                self.update_apply_buttons()
                self.update_changes_statistics()
                self.status_label.configure(text=f"Applied: {change['description']}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to apply change: {str(e)}")

    def remove_individual_change(self, change_id):
        """Remove a single change without applying"""
        if change_id in self.pending_changes:
            change = self.pending_changes[change_id]
            del self.pending_changes[change_id]
            self.update_changes_display()
            self.update_apply_buttons()
            self.update_changes_statistics()
            self.status_label.configure(text=f"Removed: {change['description']}")

    def update_changes_statistics(self):
        """Update the changes statistics display"""
        count = len(self.pending_changes)
        self.changes_count_label.configure(text=f"📊 {count} pending changes")

        if count > 0:
            # Get unique change types
            types = set(change['type'] for change in self.pending_changes.values())
            types_text = ", ".join([t.replace('_', ' ').title() for t in sorted(types)])
            if len(types_text) > 50:
                types_text = types_text[:47] + "..."
            self.changes_types_label.configure(text=f"🏷️ {types_text}")
        else:
            self.changes_types_label.configure(text="🏷️ No types")
            if self.has_unsaved_changes:
                self.mark_changes_saved()  # Stop glow when no changes

    def create_fish_tab(self):
        """Create F.I.S.H. Logic tab"""
        fish_frame = self.tabview.tab("🐟 F.I.S.H.")

        # F.I.S.H. header
        fish_header = ctk.CTkFrame(fish_frame)
        fish_header.pack(fill="x", padx=10, pady=5)

        fish_title = ctk.CTkLabel(
            fish_header,
            text="🐟 F.I.S.H. Logic Analysis",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        fish_title.pack(side="left", pady=5)

        # Analyze button
        analyze_btn = ctk.CTkButton(
            fish_header,
            text="🔍 Analyze",
            command=self.run_fish_analysis,
            width=100
        )
        analyze_btn.pack(side="right", pady=5)

        # F.I.S.H. results
        self.fish_text = ctk.CTkTextbox(fish_frame)
        self.fish_text.pack(fill="both", expand=True, padx=10, pady=5)

        # Initial F.I.S.H. info
        self.fish_text.insert("1.0", """
🐟 F.I.S.H. Logic - Flexible Intelligence for Scenario Handling

F.I.S.H. is an advanced categorization and analysis system that uses rule-based logic to:

📊 Smart Categorization:
• Weapon systems (Military, Police, Improvised)
• Consumables (Food, Medical, Fish)
• Equipment (Tools, Crafted items)
• Ammunition and calibers

🎯 Priority-Based Analysis:
• Critical: Military weapons, high-value items
• High: Police equipment, medical supplies, ammo
• Normal: Food, tools, crafted items

🔍 Scenario Detection:
• Identifies item distribution patterns
• Suggests balance improvements
• Detects potential issues

Click 'Analyze' to run F.I.S.H. analysis on your loaded economy data.
        """)
        self.fish_text.configure(state="disabled")

    def create_stats_tab(self):
        """Create statistics tab"""
        stats_frame = self.tabview.tab("📊 Statistics")

        self.stats_text = ctk.CTkTextbox(stats_frame)
        self.stats_text.pack(fill="both", expand=True, padx=10, pady=10)

    def create_status_bar(self):
        """Create status bar at bottom"""
        status_frame = ctk.CTkFrame(self.main_container)
        status_frame.grid(row=3, column=0, columnspan=2, sticky="ew", padx=5, pady=5)

        self.status_label = ctk.CTkLabel(
            status_frame,
            text="Ready - Load a JSON file to begin editing",
            font=ctk.CTkFont(size=11)
        )
        self.status_label.pack(side="left", padx=10, pady=5)

        # Save buttons
        save_frame = ctk.CTkFrame(status_frame)
        save_frame.pack(side="right", padx=10, pady=5)

        self.save_button = ctk.CTkButton(
            save_frame,
            text="💾 Save & Exit",
            command=self.save_and_exit,
            width=120
        )
        self.save_button.pack(side="left", padx=5)

        exit_button = ctk.CTkButton(
            save_frame,
            text="❌ Exit",
            command=self.exit_application,
            width=80
        )
        exit_button.pack(side="left", padx=5)

        # Initialize glow animation variables
        self.glow_active = False
        self.glow_direction = 1
        self.glow_intensity = 0.0
        self.original_save_color = self.save_button.cget("fg_color")
        self.has_unsaved_changes = False

        # Start glow animation timer
        self.animate_glow()

    # Core functionality methods
    def update_undo_redo_buttons(self):
        """Update undo/redo button states"""
        if self.undo_manager.can_undo():
            self.undo_btn.configure(state="normal")
        else:
            self.undo_btn.configure(state="disabled")

        if self.undo_manager.can_redo():
            self.redo_btn.configure(state="normal")
        else:
            self.redo_btn.configure(state="disabled")

    def animate_glow(self):
        """Animate the save button glow when there are unsaved changes"""
        if self.has_unsaved_changes and not self.glow_active:
            self.glow_active = True
        elif not self.has_unsaved_changes and self.glow_active:
            self.glow_active = False
            # Reset to original color
            self.save_button.configure(fg_color=self.original_save_color)

        if self.glow_active:
            # Update glow intensity
            self.glow_intensity += 0.05 * self.glow_direction

            # Reverse direction at limits
            if self.glow_intensity >= 1.0:
                self.glow_intensity = 1.0
                self.glow_direction = -1
            elif self.glow_intensity <= 0.3:
                self.glow_intensity = 0.3
                self.glow_direction = 1

            # Calculate glow color (orange to bright orange)
            base_color = "#1f538d"  # Default blue
            glow_color = self.interpolate_color(base_color, "#ff6b35", self.glow_intensity)

            # Apply glow color
            self.save_button.configure(fg_color=glow_color)

        # Schedule next animation frame
        self.root.after(100, self.animate_glow)

    def interpolate_color(self, color1, color2, factor):
        """Interpolate between two hex colors"""
        # Convert hex to RGB
        def hex_to_rgb(hex_color):
            hex_color = hex_color.lstrip('#')
            return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

        def rgb_to_hex(rgb):
            return f"#{rgb[0]:02x}{rgb[1]:02x}{rgb[2]:02x}"

        rgb1 = hex_to_rgb(color1)
        rgb2 = hex_to_rgb(color2)

        # Interpolate each channel
        r = int(rgb1[0] + (rgb2[0] - rgb1[0]) * factor)
        g = int(rgb1[1] + (rgb2[1] - rgb1[1]) * factor)
        b = int(rgb1[2] + (rgb2[2] - rgb1[2]) * factor)

        return rgb_to_hex((r, g, b))

    def mark_unsaved_changes(self):
        """Mark that there are unsaved changes"""
        self.has_unsaved_changes = True
        self.status_label.configure(text="⚠️ Unsaved changes detected")

    def mark_changes_saved(self):
        """Mark that changes have been saved"""
        self.has_unsaved_changes = False
        self.status_label.configure(text="✅ All changes saved")

    def save_state(self, description=""):
        """Save current state for undo/redo"""
        if self.current_data:
            self.undo_manager.save_state(self.current_data, description)
            self.update_undo_redo_buttons()

    def undo_operation(self):
        """Undo last operation"""
        data = self.undo_manager.undo()
        if data:
            self.current_data = data
            self.refresh_displays()
            self.update_undo_redo_buttons()
            self.status_label.configure(text="Undid operation")

    def redo_operation(self):
        """Redo next operation"""
        data = self.undo_manager.redo()
        if data:
            self.current_data = data
            self.refresh_displays()
            self.update_undo_redo_buttons()
            self.status_label.configure(text="Redid operation")

    def show_history(self):
        """Show operation history"""
        history_window = ctk.CTkToplevel(self.root)
        history_window.title("Operation History")
        history_window.geometry("600x400")
        history_window.transient(self.root)

        # History list
        history_frame = ctk.CTkFrame(history_window)
        history_frame.pack(fill="both", expand=True, padx=20, pady=20)

        title = ctk.CTkLabel(history_frame, text="📜 Operation History",
                           font=ctk.CTkFont(size=16, weight="bold"))
        title.pack(pady=10)

        # History listbox
        history_listbox = tk.Listbox(history_frame, height=15, font=("Arial", 10))
        history_listbox.pack(fill="both", expand=True, padx=10, pady=5)

        # Populate history
        for i, description, timestamp in self.undo_manager.get_history_list():
            current_marker = " ← Current" if i == self.undo_manager.current_index else ""
            history_listbox.insert(tk.END, f"{timestamp} - {description}{current_marker}")

        # Close button
        close_btn = ctk.CTkButton(history_frame, text="Close", command=history_window.destroy)
        close_btn.pack(pady=10)

    # File Management Methods
    def auto_scan_json_files(self):
        """Auto-scan for JSON files in current directory"""
        try:
            json_files = [f for f in os.listdir('.') if f.endswith('.json')]
            if json_files:
                self.status_label.configure(text=f"Found {len(json_files)} JSON file(s) in directory")
            else:
                self.status_label.configure(text="No JSON files found in current directory")
        except Exception as e:
            self.status_label.configure(text="Error scanning directory")

    def scan_directory(self):
        """Scan directory for JSON files and show selection dialog"""
        try:
            json_files = [f for f in os.listdir('.') if f.endswith('.json')]
            if not json_files:
                messagebox.showinfo("No Files", "No JSON files found in current directory")
                return

            # Create selection dialog
            self.show_file_selection_dialog(json_files)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to scan directory: {str(e)}")

    def show_file_selection_dialog(self, json_files):
        """Show dialog to select from available JSON files"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Select JSON File")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # Title
        title = ctk.CTkLabel(dialog, text="📁 Select Economy Override JSON File",
                           font=ctk.CTkFont(size=16, weight="bold"))
        title.pack(pady=10)

        # File list frame
        listbox_frame = ctk.CTkFrame(dialog)
        listbox_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # Create listbox with scrollbar
        file_listbox = tk.Listbox(listbox_frame, height=20, font=("Arial", 10))
        scrollbar = tk.Scrollbar(listbox_frame, orient="vertical", command=file_listbox.yview)
        file_listbox.configure(yscrollcommand=scrollbar.set)

        file_listbox.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        scrollbar.pack(side="right", fill="y", pady=5)

        # Populate listbox
        for file in json_files:
            file_listbox.insert(tk.END, file)

        # Buttons
        button_frame = ctk.CTkFrame(dialog)
        button_frame.pack(fill="x", padx=20, pady=10)

        def load_selected():
            selection = file_listbox.curselection()
            if selection:
                filename = json_files[selection[0]]
                self.load_specific_file(filename)
                dialog.destroy()
            else:
                messagebox.showwarning("No Selection", "Please select a file")

        load_btn = ctk.CTkButton(button_frame, text="📂 Load Selected", command=load_selected)
        load_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(button_frame, text="❌ Cancel", command=dialog.destroy)
        cancel_btn.pack(side="right", padx=5)

    def load_specific_file(self, filename):
        """Load a specific JSON file"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.current_data = json.load(f)
            self.current_filename = filename
            self.current_file_path = os.path.abspath(filename)
            self.current_file_label.configure(text=f"✅ Loaded: {filename}", text_color="green")
            self.status_label.configure(text=f"Successfully loaded {filename}")

            # Save initial state
            self.save_state("File loaded")

            # Update displays
            self.populate_tree()
            self.update_statistics()
            self.build_categories()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load {filename}: {str(e)}")
            self.status_label.configure(text="Error loading file")

    def load_json_file(self):
        """Load a JSON file using file dialog"""
        file_path = filedialog.askopenfilename(
            title="Select Economy Override JSON File",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.current_data = json.load(f)
                self.current_filename = os.path.basename(file_path)
                self.current_file_path = file_path
                self.current_file_label.configure(text=f"✅ Loaded: {self.current_filename}", text_color="green")
                self.status_label.configure(text=f"Successfully loaded {self.current_filename}")

                # Save initial state
                self.save_state("File loaded")

                # Update displays
                self.populate_tree()
                self.update_statistics()
                self.build_categories()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to load JSON file: {str(e)}")
                self.status_label.configure(text="Error loading file")

    def reload_file(self):
        """Reload the current file"""
        if self.current_file_path and os.path.exists(self.current_file_path):
            try:
                with open(self.current_file_path, 'r', encoding='utf-8') as f:
                    self.current_data = json.load(f)
                self.status_label.configure(text=f"Reloaded {self.current_filename}")

                # Save state
                self.save_state("File reloaded")

                # Update displays
                self.populate_tree()
                self.update_statistics()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to reload file: {str(e)}")
        elif self.current_filename and os.path.exists(self.current_filename):
            try:
                with open(self.current_filename, 'r', encoding='utf-8') as f:
                    self.current_data = json.load(f)
                self.status_label.configure(text=f"Reloaded {self.current_filename}")

                # Save state
                self.save_state("File reloaded")

                # Update displays
                self.populate_tree()
                self.update_statistics()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to reload file: {str(e)}")
        else:
            messagebox.showwarning("Warning", "No file to reload. Please load a file first.")

    # Display Update Methods
    def populate_tree(self):
        """Populate the tree view with data"""
        if not self.current_data:
            return

        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        try:
            traders = self.current_data.get("economy-override", {}).get("traders", {})

            # Group by outpost
            outposts = defaultdict(list)
            for trader_key in traders.keys():
                outpost = trader_key.split("_")[0] if "_" in trader_key else "Unknown"
                outposts[outpost].append(trader_key)

            # Build tree
            for outpost, trader_list in sorted(outposts.items()):
                outpost_id = self.tree.insert("", "end", text=f"🏢 {outpost}", open=True)

                for trader_key in sorted(trader_list):
                    trader_items = traders[trader_key]
                    trader_id = self.tree.insert(outpost_id, "end", text=f"👤 {trader_key}")

                    # Add items (limit to first 50 for performance)
                    for idx, item in enumerate(trader_items[:50]):
                        if isinstance(item, dict):
                            code = item.get("tradeable-code", f"Item_{idx}")
                            price = item.get("base-purchase-price", "N/A")
                            item_text = f"📦 {code} (${price})"
                            self.tree.insert(trader_id, "end", text=item_text,
                                           values=(trader_key, idx))

                    if len(trader_items) > 50:
                        self.tree.insert(trader_id, "end", text=f"... and {len(trader_items) - 50} more items")

        except Exception as e:
            self.status_label.configure(text=f"Error populating tree: {str(e)}")

    def refresh_displays(self):
        """Refresh all display elements"""
        self.populate_tree()
        self.update_statistics()
        self.build_categories()
        self.update_changes_display()

    def update_statistics(self):
        """Update the statistics tab"""
        if not self.current_data:
            self.stats_text.delete("1.0", "end")
            self.stats_text.insert("1.0", "No data loaded")
            return

        try:
            traders = self.current_data.get("economy-override", {}).get("traders", {})

            # Calculate statistics
            total_traders = len(traders)
            total_items = sum(len(items) for items in traders.values())

            # Outpost breakdown
            outpost_counts = defaultdict(int)
            for trader_key in traders.keys():
                outpost = trader_key.split("_")[0] if "_" in trader_key else "Unknown"
                outpost_counts[outpost] += 1

            # Item categories using F.I.S.H. logic
            fish_analysis = self.fish_engine.analyze_economy(self.current_data)

            # Build statistics text
            stats_text = f"""📊 Economy Statistics

📈 Overview:
• Total Traders: {total_traders}
• Total Items: {total_items}
• Average Items per Trader: {total_items / total_traders if total_traders > 0 else 0:.1f}

🏢 Outpost Breakdown:
"""
            for outpost, count in sorted(outpost_counts.items()):
                stats_text += f"• {outpost}: {count} traders\n"

            stats_text += f"\n🐟 F.I.S.H. Categories:\n"
            for category, items in sorted(fish_analysis['categories'].items(),
                                        key=lambda x: len(x[1]), reverse=True)[:10]:
                stats_text += f"• {category}: {len(items)} items\n"

            stats_text += f"\n🎯 Priority Distribution:\n"
            for priority, count in sorted(fish_analysis['priority_distribution'].items(),
                                        key=lambda x: {'critical': 3, 'high': 2, 'normal': 1}.get(x[0], 0), reverse=True):
                stats_text += f"• {priority.title()}: {count} items\n"

            self.stats_text.delete("1.0", "end")
            self.stats_text.insert("1.0", stats_text)

        except Exception as e:
            self.stats_text.delete("1.0", "end")
            self.stats_text.insert("1.0", f"Error calculating statistics: {str(e)}")

    def build_categories(self):
        """Build smart categories from loaded data"""
        if not self.current_data:
            return

        try:
            # Use F.I.S.H. engine for smart categorization
            fish_analysis = self.fish_engine.analyze_economy(self.current_data)
            self.categories = [(cat, len(items)) for cat, items in fish_analysis['categories'].items()]
            self.categories.sort(key=lambda x: x[1], reverse=True)

        except Exception as e:
            self.status_label.configure(text=f"Error building categories: {str(e)}")

    # Changes Management Methods
    def add_pending_change(self, change_type, description, before_data, after_data, apply_func):
        """Add a pending change to the changes list"""
        change_id = f"{change_type}_{len(self.pending_changes)}"
        self.pending_changes[change_id] = {
            'type': change_type,
            'description': description,
            'before': before_data,
            'after': after_data,
            'apply_function': apply_func,
            'timestamp': datetime.now().strftime("%H:%M:%S")
        }
        self.update_changes_display()
        self.update_apply_buttons()
        self.mark_unsaved_changes()  # Trigger glow

    def update_changes_display(self):
        """Update the changes tab display"""
        self.changes_text.configure(state="normal")
        self.changes_text.delete("1.0", "end")

        if not self.pending_changes:
            self.changes_text.insert("1.0", """
📋 Pending Changes

No changes pending. Make edits using the tools in the left panel.

Changes will appear here with before/after comparisons:
• Item modifications
• Price adjustments
• Availability changes
• Fame point updates

Use the Apply All button to commit changes or Clear All to discard them.
            """)
        else:
            changes_text = f"📋 Pending Changes ({len(self.pending_changes)} changes)\n\n"

            for change_id, change in self.pending_changes.items():
                changes_text += f"🕒 {change['timestamp']} - {change['description']}\n"
                changes_text += f"Type: {change['type']}\n"

                # Show before/after for relevant changes
                if 'before' in change and 'after' in change:
                    changes_text += f"Before: {change['before']}\n"
                    changes_text += f"After: {change['after']}\n"

                changes_text += "-" * 50 + "\n\n"

            self.changes_text.insert("1.0", changes_text)

        self.changes_text.configure(state="disabled")

    def update_apply_buttons(self):
        """Update apply/clear button states"""
        if self.pending_changes:
            self.apply_all_btn.configure(state="normal")
            self.clear_all_btn.configure(state="normal")
        else:
            self.apply_all_btn.configure(state="disabled")
            self.clear_all_btn.configure(state="disabled")

    def apply_all_changes(self):
        """Apply all pending changes"""
        if not self.pending_changes:
            return

        if messagebox.askyesno("Confirm Apply",
                             f"Apply {len(self.pending_changes)} pending changes?"):

            # Save state before applying changes
            self.save_state(f"Applied {len(self.pending_changes)} changes")

            applied_count = 0
            for change_id, change in self.pending_changes.items():
                try:
                    change['apply_function']()
                    applied_count += 1
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to apply change: {str(e)}")

            # Clear pending changes
            self.pending_changes.clear()
            self.update_changes_display()
            self.update_apply_buttons()
            self.refresh_displays()
            self.mark_changes_saved()  # Stop glow

            self.status_label.configure(text=f"Applied {applied_count} changes")
            messagebox.showinfo("Success", f"Successfully applied {applied_count} changes")

    def clear_all_changes(self):
        """Clear all pending changes"""
        if not self.pending_changes:
            return

        if messagebox.askyesno("Confirm Clear",
                             f"Clear {len(self.pending_changes)} pending changes?"):
            self.pending_changes.clear()
            self.update_changes_display()
            self.update_apply_buttons()
            self.mark_changes_saved()  # Stop glow
            self.status_label.configure(text="Cleared all pending changes")

    # F.I.S.H. Logic Methods
    def run_fish_analysis(self):
        """Run F.I.S.H. analysis on current data"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        try:
            analysis = self.fish_engine.analyze_economy(self.current_data)

            # Build analysis report
            report = f"""🐟 F.I.S.H. Logic Analysis Report
Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

📊 Overview:
• Total Items Analyzed: {analysis['total_items']}
• Categories Detected: {len(analysis['categories'])}
• Priority Distribution: {dict(analysis['priority_distribution'])}

🎯 Category Breakdown:
"""

            for category, items in sorted(analysis['categories'].items(),
                                        key=lambda x: len(x[1]), reverse=True):
                report += f"\n📂 {category} ({len(items)} items):\n"

                # Show sample items
                sample_items = items[:5]
                for item_data in sample_items:
                    code = item_data['code']
                    trader = item_data['trader']
                    report += f"  • {code} ({trader})\n"

                if len(items) > 5:
                    report += f"  ... and {len(items) - 5} more items\n"

            report += f"""

🔍 F.I.S.H. Recommendations:
• High-priority items (Military, Police): {analysis['priority_distribution'].get('high', 0) + analysis['priority_distribution'].get('critical', 0)}
• Consider balancing item distribution across outposts
• Review pricing for critical category items
• Ensure adequate medical supplies availability

📋 Rule Engine Status:
• Active Rules: {len(self.fish_engine.rules)}
• Categories Matched: {len([cat for cat in analysis['categories'] if cat != 'Uncategorized'])}
• Uncategorized Items: {len(analysis['categories'].get('Uncategorized', []))}
"""

            self.fish_text.configure(state="normal")
            self.fish_text.delete("1.0", "end")
            self.fish_text.insert("1.0", report)
            self.fish_text.configure(state="disabled")

            self.status_label.configure(text="F.I.S.H. analysis completed")

        except Exception as e:
            messagebox.showerror("Error", f"F.I.S.H. analysis failed: {str(e)}")

    # Tree interaction methods
    def on_tree_select(self, event):
        """Handle tree selection"""
        selection = self.tree.selection()
        if not selection:
            return

        item = self.tree.item(selection[0])
        values = item.get('values', [])

        if len(values) >= 2:  # This is an item
            trader_key, item_idx = values[0], int(values[1])
            try:
                item_data = self.current_data["economy-override"]["traders"][trader_key][item_idx]

                # Show item details in overview tab
                details = f"📦 Selected Item Details:\n\n"
                details += f"Trader: {trader_key}\n"
                details += f"Item Index: {item_idx}\n\n"
                details += json.dumps(item_data, indent=2)

                self.overview_text.configure(state="normal")
                self.overview_text.delete("1.0", "end")
                self.overview_text.insert("1.0", details)
                self.overview_text.configure(state="disabled")

            except (KeyError, IndexError) as e:
                self.status_label.configure(text=f"Error loading item: {str(e)}")

    def search_items(self):
        """Search for items in the data"""
        search_term = self.search_entry.get().strip().lower()
        if not search_term or not self.current_data:
            return

        matches = []
        traders = self.current_data.get("economy-override", {}).get("traders", {})

        for trader_key, trader_items in traders.items():
            for idx, item in enumerate(trader_items):
                if isinstance(item, dict):
                    code = item.get("tradeable-code", "").lower()
                    if search_term in code:
                        matches.append((trader_key, idx, item))

        if matches:
            # Show first match details
            first_match = matches[0][2]
            details = f"🔍 Search Results ({len(matches)} found):\n\nFirst match:\n"
            details += json.dumps(first_match, indent=2)

            self.overview_text.configure(state="normal")
            self.overview_text.delete("1.0", "end")
            self.overview_text.insert("1.0", details)
            self.overview_text.configure(state="disabled")

            messagebox.showinfo("Search Results", f"Found {len(matches)} items matching '{search_term}'")
        else:
            messagebox.showinfo("Search Results", f"No items found matching '{search_term}'")

    # CLI Integration and Menu Actions
    def launch_cli_terminal(self):
        """Launch the CLI version in a separate terminal with console-style feedback"""
        # Check if CLI file exists first
        if not os.path.exists('1_45c.py'):
            self.show_cli_unavailable_dialog()
            return

        # Show console-style launch dialog
        self.show_cli_launch_dialog()

    def show_cli_launch_dialog(self):
        """Show console-style CLI launch dialog"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("⬛ CLI Terminal Launcher")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.configure(fg_color="#1a1a1a")  # Dark terminal background

        # Header with console styling
        header_frame = ctk.CTkFrame(dialog, fg_color="#000000", corner_radius=0)
        header_frame.pack(fill="x", padx=0, pady=0)

        header_label = ctk.CTkLabel(
            header_frame,
            text="⬛ SCUM Economy Chooser - CLI Terminal Launcher",
            font=ctk.CTkFont(family="Courier New", size=14, weight="bold"),
            text_color="#00ff00"
        )
        header_label.pack(pady=10)

        # Console output area
        console_frame = ctk.CTkFrame(dialog, fg_color="#1a1a1a")
        console_frame.pack(fill="both", expand=True, padx=10, pady=10)

        console_text = ctk.CTkTextbox(
            console_frame,
            font=ctk.CTkFont(family="Courier New", size=11),
            fg_color="#000000",
            text_color="#00ff00",
            corner_radius=0
        )
        console_text.pack(fill="both", expand=True, padx=5, pady=5)

        # Console-style startup text
        startup_text = """
⬛ SCUM Economy Chooser CLI Terminal Launcher v2.01
================================================================================

🖥️  TERMINAL INFORMATION:
   • CLI Version: 1_45c.py (Original CLI - Untouched)
   • GUI Version: v2.01 (Enhanced with dual-mode system)
   • Compatibility: Full CLI feature parity maintained

🔧 LAUNCH OPTIONS:
   • Windows: Opens new Command Prompt window
   • Linux/Mac: Opens new terminal window
   • Working Directory: Current application directory

⚠️  IMPORTANT NOTES:
   • CLI operates independently from GUI
   • CLI maintains original -99% to +100% validation
   • Both versions can work with same JSON files
   • CLI file (1_45c.py) remains completely untouched

🛡️  SAFETY FEATURES:
   • CLI uses original validation rules
   • No interference between CLI and GUI operations
   • Separate process isolation maintained

Ready to launch CLI terminal...
        """

        console_text.insert("1.0", startup_text)
        console_text.configure(state="disabled")

        # Button frame with console styling
        button_frame = ctk.CTkFrame(dialog, fg_color="#1a1a1a")
        button_frame.pack(fill="x", padx=10, pady=10)

        def launch_cli():
            """Actually launch the CLI with console feedback"""
            console_text.configure(state="normal")
            console_text.insert("end", "\n🚀 LAUNCHING CLI TERMINAL...\n")
            console_text.insert("end", "================================================================================\n")

            try:
                if os.name == 'nt':  # Windows
                    console_text.insert("end", "🖥️  Platform: Windows\n")
                    console_text.insert("end", "📂 Command: cmd /c start cmd /k python 1_45c.py\n")
                    subprocess.Popen(['cmd', '/c', 'start', 'cmd', '/k', 'python', '1_45c.py'])
                else:  # Unix/Linux/Mac
                    console_text.insert("end", "🖥️  Platform: Unix/Linux/Mac\n")
                    console_text.insert("end", "📂 Command: gnome-terminal -- python3 1_45c.py\n")
                    subprocess.Popen(['gnome-terminal', '--', 'python3', '1_45c.py'])

                console_text.insert("end", "✅ CLI terminal launched successfully!\n")
                console_text.insert("end", "🔄 You can now use both GUI and CLI simultaneously.\n")
                console_text.insert("end", "⬛ CLI window should appear in a separate terminal.\n\n")
                console_text.insert("end", "Press 'Close' to return to GUI...\n")

                self.status_label.configure(text="⬛ CLI terminal launched successfully")

            except Exception as e:
                console_text.insert("end", f"❌ ERROR: Failed to launch CLI terminal\n")
                console_text.insert("end", f"🔍 Details: {str(e)}\n")
                console_text.insert("end", "💡 Ensure Python is in your system PATH\n")
                console_text.insert("end", "💡 Ensure 1_45c.py exists in current directory\n")

                self.status_label.configure(text="❌ CLI launch failed")

            console_text.configure(state="disabled")
            console_text.see("end")

        # Console-styled buttons
        launch_btn = ctk.CTkButton(
            button_frame,
            text="🚀 LAUNCH CLI",
            command=launch_cli,
            font=ctk.CTkFont(family="Courier New", size=12, weight="bold"),
            fg_color="#006600",
            hover_color="#008800",
            text_color="#ffffff",
            height=40
        )
        launch_btn.pack(side="left", padx=5)

        close_btn = ctk.CTkButton(
            button_frame,
            text="⬛ CLOSE",
            command=dialog.destroy,
            font=ctk.CTkFont(family="Courier New", size=12, weight="bold"),
            fg_color="#333333",
            hover_color="#555555",
            text_color="#ffffff",
            height=40
        )
        close_btn.pack(side="right", padx=5)

    def show_cli_unavailable_dialog(self):
        """Show ASCII sad face when CLI is not available"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("⬛ CLI Not Available")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.configure(fg_color="#1a1a1a")  # Dark terminal background

        # Console-style frame
        console_frame = ctk.CTkFrame(dialog, fg_color="#000000", corner_radius=0)
        console_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # ASCII art and message
        console_text = ctk.CTkTextbox(
            console_frame,
            font=ctk.CTkFont(family="Courier New", size=11),
            fg_color="#000000",
            text_color="#ff4444",  # Red text for error
            corner_radius=0
        )
        console_text.pack(fill="both", expand=True, padx=5, pady=5)

        sad_face_message = """
⬛ SCUM Economy Chooser CLI Terminal
================================================================================

                            ╭─────────────────╮
                            │                 │
                            │    ☹️  ERROR    │
                            │                 │
                            ╰─────────────────╯

                                 .-""""""-.
                               .'          '.
                              /   O      O   \\
                             :           `    :
                             |                |
                             :    \\______/    :
                              \\              /
                               '.          .'
                                 '-.......-'

❌ CLI NOT AVAILABLE

🔍 ISSUE DETECTED:
   • File '1_45c.py' not found in current directory
   • Original CLI version is missing or moved

💡 POSSIBLE SOLUTIONS:
   • Ensure '1_45c.py' is in the same folder as this GUI
   • Download the original CLI file from the source
   • Check if the file was renamed or moved

⚠️  IMPORTANT:
   • The GUI version (v2.01) works independently
   • All features are available in Advanced Mode
   • CLI compatibility is maintained when file is present

🎮 CURRENT STATUS:
   • GUI Mode: ✅ Fully Functional
   • CLI Mode: ❌ File Missing
   • Dual Operation: ❌ CLI Required

Press 'OK' to continue with GUI-only mode...
        """

        console_text.insert("1.0", sad_face_message)
        console_text.configure(state="disabled")

        # Close button with console styling
        close_btn = ctk.CTkButton(
            dialog,
            text="⬛ OK - Continue with GUI",
            command=dialog.destroy,
            font=ctk.CTkFont(family="Courier New", size=12, weight="bold"),
            fg_color="#333333",
            hover_color="#555555",
            text_color="#ffffff",
            height=40
        )
        close_btn.pack(pady=10)

        # Update status
        self.status_label.configure(text="❌ CLI not available - 1_45c.py not found")

    def show_help(self):
        """Show help dialog"""
        help_window = ctk.CTkToplevel(self.root)
        help_window.title("Help - SCUM Economy Chooser Enhanced")
        help_window.geometry("800x700")
        help_window.transient(self.root)

        # Help content
        help_text = """
🎮 SCUM Economy Chooser - Enhanced GUI Help

📖 GETTING STARTED:
1. Load your economyoverride.json file using 'Load' or 'Scan'
2. Browse your data in the Data Tree tab
3. Select an editing tool from the left panel
4. Preview changes in the Changes tab
5. Apply changes with full undo/redo support

🔄 UNDO/REDO SYSTEM:
• Full operation history tracking
• Visual before/after comparisons
• Safe experimentation with rollback
• History viewer shows all operations

📋 CHANGES MANAGEMENT:
• All edits are staged before applying
• Preview changes with before/after comparison
• Apply All to commit or Clear All to discard
• Individual change review and approval

🐟 F.I.S.H. LOGIC FEATURES:
F.I.S.H. = Flexible Intelligence for Scenario Handling

🎯 Smart Categorization:
• Weapon systems (Military, Police, Improvised)
• Consumables (Food, Medical, Fish)
• Equipment (Tools, Crafted items)
• Ammunition and calibers

🔍 Priority-Based Analysis:
• Critical: Military weapons, high-value items
• High: Police equipment, medical supplies, ammo
• Normal: Food, tools, crafted items

📊 Scenario Detection:
• Identifies item distribution patterns
• Suggests balance improvements
• Detects potential issues

🌍 GLOBAL OPERATIONS:
• Global price changes with preview
• Economy field configuration
• Bulk operations across all traders

👤 MERCHANT OPERATIONS:
• Individual merchant editing
• Outpost-wide modifications
• Fine-tune specific items

🔬 ADVANCED TOOLS:
• Spread edit with advanced filtering
• Purchase settings management
• Category-based bulk operations

💡 TIPS:
• Always backup your files before editing
• Use F.I.S.H. Analysis for insights
• Preview changes before applying
• Check the Changes tab for pending modifications
• Use Undo/Redo for safe experimentation

⚠️ SAFETY:
• All changes are staged before applying
• Full undo/redo system with history
• Original files are never overwritten
• Timestamped saves preserve your work

For more detailed help, consult the original CLI documentation.
        """

        text_widget = ctk.CTkTextbox(help_window, font=ctk.CTkFont(family="Arial", size=11))
        text_widget.pack(fill="both", expand=True, padx=20, pady=20)
        text_widget.insert("1.0", help_text)
        text_widget.configure(state="disabled")

        close_btn = ctk.CTkButton(help_window, text="Close", command=help_window.destroy)
        close_btn.pack(pady=10)

    # Menu Action Methods - These will open dialog windows with apply buttons
    def open_global_price_changes(self):
        """Open global price changes dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        try:
            # Import the enhanced dialog with apply buttons
            from enhanced_dialogs import EnhancedGlobalPriceDialog
            EnhancedGlobalPriceDialog(self.root, self.current_data, self.update_status,
                                    self.refresh_displays, self.add_pending_change)
        except ImportError as e:
            messagebox.showerror("Error", f"Failed to load enhanced dialog: {str(e)}")
            # Fallback to basic functionality
            self.basic_global_price_changes()

    def open_economy_fields(self):
        """Open economy fields editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        try:
            from enhanced_dialogs import EnhancedEconomyFieldsDialog
            EnhancedEconomyFieldsDialog(self.root, self.current_data, self.update_status,
                                      self.refresh_displays, self.add_pending_change)
        except ImportError:
            # Fallback to basic functionality
            self.basic_economy_fields()

    def open_merchant_level(self):
        """Open merchant level editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        try:
            from enhanced_dialogs import EnhancedMerchantLevelDialog
            EnhancedMerchantLevelDialog(self.root, self.current_data, self.update_status,
                                      self.refresh_displays, self.add_pending_change)
        except ImportError:
            # Fallback to basic functionality
            self.basic_merchant_level()

    def open_outpost_level(self):
        """Open outpost level editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        try:
            from enhanced_dialogs import EnhancedOutpostLevelDialog
            EnhancedOutpostLevelDialog(self.root, self.current_data, self.update_status,
                                     self.refresh_displays, self.add_pending_change)
        except ImportError:
            # Fallback to basic functionality
            self.basic_outpost_level()

    def open_fine_tune(self):
        """Open fine tune editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        try:
            from enhanced_dialogs import EnhancedFineTuneDialog
            EnhancedFineTuneDialog(self.root, self.current_data, self.update_status,
                                 self.refresh_displays, self.add_pending_change)
        except ImportError:
            # Fallback to basic functionality
            self.basic_fine_tune()

    def open_spread_edit(self):
        """Open spread edit dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        try:
            from enhanced_dialogs import EnhancedSpreadEditDialog
            EnhancedSpreadEditDialog(self.root, self.current_data, self.update_status,
                                   self.refresh_displays, self.add_pending_change)
        except ImportError:
            # Fallback to basic functionality
            self.basic_spread_edit()

    def open_purchase_settings(self):
        """Open purchase settings dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        try:
            from enhanced_dialogs import EnhancedPurchaseSettingsDialog
            EnhancedPurchaseSettingsDialog(self.root, self.current_data, self.update_status,
                                         self.refresh_displays, self.add_pending_change)
        except ImportError:
            # Fallback to basic functionality
            self.basic_purchase_settings()

    def open_fish_analysis(self):
        """Open F.I.S.H. analysis dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        self.run_fish_analysis()
        # Switch to F.I.S.H. tab
        self.tabview.set("🐟 F.I.S.H.")

    def open_smart_categories(self):
        """Open smart categories dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        from enhanced_dialogs import SmartCategoriesDialog
        SmartCategoriesDialog(self.root, self.current_data, self.update_status,
                            self.refresh_displays, self.add_pending_change,
                            self.fish_engine)

    def open_scenario_rules(self):
        """Open scenario rules editor"""
        try:
            from enhanced_dialogs import ScenarioRulesDialog
            ScenarioRulesDialog(self.root, self.fish_engine, self.update_status)
        except ImportError:
            # Show built-in F.I.S.H. rules information
            self.show_fish_rules_info()

    def show_fish_rules_info(self):
        """Show information about built-in F.I.S.H. rules"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("F.I.S.H. Logic Rules")
        dialog.geometry("700x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # Header
        header_label = ctk.CTkLabel(
            dialog,
            text="🐟 F.I.S.H. Logic Rules",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        header_label.pack(pady=10)

        # Info text
        info_text = ctk.CTkTextbox(dialog)
        info_text.pack(fill="both", expand=True, padx=20, pady=10)

        rules_info = """
🐟 F.I.S.H. Logic - Flexible Item Sorting & Handling

F.I.S.H. Logic is the built-in AI-powered categorization system that automatically sorts items into smart categories based on their codes and properties.

📋 BUILT-IN CATEGORIZATION RULES:

🔫 WEAPONS:
• Primary Weapons: AK, M16, M4, AR, Sniper, Rifle, SMG, LMG
• Secondary Weapons: Pistol, Handgun, Revolver, Glock, M9, Deagle
• Melee Weapons: Knife, Sword, Axe, Hammer, Bat, Machete, Katana
• Improvised Weapons: Items with "Improvised" in the name
• Suppressors: WeaponSuppressor_, Suppressor, Silencer

🎖️ MILITARY & POLICE:
• Military: Items containing "Military", "Army", "Combat", "Tactical"
• Police: Items containing "Police", "Cop", "Law"

💥 AMMUNITION:
• Calibers: Cal_, _AP_CR endings
• Specific calibers: _556x45_, _762x39_, _9x19_, _45ACP_, _12Gauge_
• Ammo keywords: "ammo", "bullet", "round", "cartridge"

🍽️ FOOD & CONSUMABLES:
• Fish: Fish_ prefix (excluding fishing equipment)
• Canned Food: Can_ prefix, "Canned" keyword
• Meat: Meat_ prefix, "_Meat" suffix
• General Food: "food", "drink", "beverage", "snack"

💊 MEDICAL:
• Medical supplies: "bandage", "pill", "syringe", "medical", "medicine", "antibiotic"

🔧 TOOLS & EQUIPMENT:
• Tools: "tool", "hammer", "wrench", "screwdriver", "pliers"
• Crafted Items: Crafted_ prefix
• Improvised Equipment: "Improvised" (non-weapon)

🛡️ SAFETY FEATURES:
• Weapons are prioritized to prevent misclassification as food
• Default values (-1, null, none) are protected from changes
• Tradeable codes are locked to prevent corruption
• All changes follow CLI-compatible validation rules

🔧 CUSTOMIZATION:
• Normal Mode: Create custom buckets with your own filter rules
• Advanced Mode: Access full F.I.S.H. Logic with AI-powered detection
• Filter Types: contains, starts_with, not_contains, contains_any

💡 TIPS:
• F.I.S.H. Logic learns from your data structure
• Categories are dynamically generated based on item codes
• Use custom buckets for specific workflows
• Switch to Advanced Mode for full F.I.S.H. capabilities

🎯 CURRENT VERSION: XconomyChooser v2.01
Built-in rules are optimized for SCUM economy files and provide robust, safe categorization without requiring custom rule editing.
        """

        info_text.insert("1.0", rules_info)
        info_text.configure(state="disabled")

        # Close button
        close_btn = ctk.CTkButton(dialog, text="✅ Close", command=dialog.destroy)
        close_btn.pack(pady=10)

    # Fallback methods for basic functionality
    def basic_global_price_changes(self):
        """Basic global price changes without enhanced features"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Global Price Changes")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # Simple interface
        frame = ctk.CTkFrame(dialog)
        frame.pack(fill="both", expand=True, padx=20, pady=20)

        title = ctk.CTkLabel(frame, text="Global Price Changes", font=ctk.CTkFont(size=16, weight="bold"))
        title.pack(pady=10)

        # Price field selection
        field_var = ctk.StringVar(value="base-purchase-price")
        ctk.CTkRadioButton(frame, text="Purchase Price", variable=field_var, value="base-purchase-price").pack(pady=5)
        ctk.CTkRadioButton(frame, text="Sell Price", variable=field_var, value="base-sell-price").pack(pady=5)

        # Percentage input
        ctk.CTkLabel(frame, text="Percentage Change:").pack(pady=5)
        percent_entry = ctk.CTkEntry(frame, placeholder_text="Enter percentage")
        percent_entry.pack(pady=5)

        def apply_changes():
            try:
                percentage = float(percent_entry.get())
                if not -99 <= percentage <= 100:
                    messagebox.showerror("Error", "Percentage must be between -99 and 100")
                    return

                field = field_var.get()
                if messagebox.askyesno("Confirm", f"Apply {percentage:+.1f}% change to {field}?"):
                    self.save_state(f"Global {field} change: {percentage:+.1f}%")

                    count = 0
                    for trader_items in self.current_data.get("economy-override", {}).get("traders", {}).values():
                        for item in trader_items:
                            if isinstance(item, dict) and field in item and item[field] not in ["null", "-1", ""]:
                                try:
                                    current_price = float(item[field])
                                    new_price = max(1, round(current_price * (1 + percentage / 100)))
                                    item[field] = str(new_price)
                                    count += 1
                                except (ValueError, TypeError):
                                    continue

                    self.refresh_displays()
                    self.update_status(f"Applied {percentage:+.1f}% change to {count} items")
                    messagebox.showinfo("Success", f"Updated {count} items")
                    dialog.destroy()

            except ValueError:
                messagebox.showerror("Error", "Please enter a valid percentage")

        ctk.CTkButton(frame, text="Apply Changes", command=apply_changes).pack(pady=10)
        ctk.CTkButton(frame, text="Cancel", command=dialog.destroy).pack(pady=5)

    def basic_economy_fields(self):
        """Basic economy fields editor"""
        messagebox.showinfo("Economy Fields", "Basic Economy Fields Editor\n\nUse the enhanced version for full functionality.\nThis feature requires the enhanced dialogs module.")

    def basic_merchant_level(self):
        """Basic merchant level editor"""
        messagebox.showinfo("Merchant Level", "Basic Merchant Level Editor\n\nUse the enhanced version for full functionality.\nThis feature requires the enhanced dialogs module.")

    def basic_outpost_level(self):
        """Basic outpost level editor"""
        messagebox.showinfo("Outpost Level", "Basic Outpost Level Editor\n\nUse the enhanced version for full functionality.\nThis feature requires the enhanced dialogs module.")

    def basic_fine_tune(self):
        """Basic fine tune editor"""
        messagebox.showinfo("Fine Tune", "Basic Fine Tune Editor\n\nUse the enhanced version for full functionality.\nThis feature requires the enhanced dialogs module.")

    def basic_spread_edit(self):
        """Basic spread edit editor"""
        messagebox.showinfo("Spread Edit", "Basic Spread Edit Editor\n\nUse the enhanced version for full functionality.\nThis feature requires the enhanced dialogs module.")

    def basic_purchase_settings(self):
        """Basic purchase settings editor"""
        messagebox.showinfo("Purchase Settings", "Basic Purchase Settings Editor\n\nUse the enhanced version for full functionality.\nThis feature requires the enhanced dialogs module.")

    # Utility methods
    def update_status(self, message):
        """Update status bar message"""
        self.status_label.configure(text=message)

    # Save and Exit Methods
    def save_and_exit(self):
        """Save the current data and exit"""
        if not self.current_data:
            messagebox.showwarning("Warning", "No data to save")
            return

        # Check for pending changes
        if self.pending_changes:
            result = messagebox.askyesnocancel("Pending Changes",
                                             f"You have {len(self.pending_changes)} pending changes.\n\n"
                                             "Yes: Apply changes and save\n"
                                             "No: Save without applying changes\n"
                                             "Cancel: Don't save")
            if result is None:  # Cancel
                return
            elif result:  # Yes - apply changes
                self.apply_all_changes()

        # Generate timestamped filename (CLI-compatible format)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_filename = f"economyoverride_{timestamp}.json"

        try:
            with open(save_filename, 'w', encoding='utf-8') as f:
                json.dump(self.current_data, f, indent=2)

            # Log the save operation (CLI parity)
            self.economy_logger.log_file_operation("Changes saved to", save_filename)

            self.mark_changes_saved()  # Stop glow
            messagebox.showinfo("Success", f"File saved as {save_filename}")

            # Log session end and show log if enabled (CLI parity)
            self.economy_logger.log_session_end(save_filename)

            self.root.quit()
        except Exception as e:
            self.economy_logger.log_edit(f"ERROR: Failed to save file: {str(e)}")
            messagebox.showerror("Error", f"Failed to save file: {str(e)}")

    def exit_application(self):
        """Exit the application"""
        if self.pending_changes:
            if not messagebox.askyesno("Pending Changes",
                                     f"You have {len(self.pending_changes)} pending changes.\n"
                                     "Are you sure you want to exit without applying them?"):
                return

        if messagebox.askyesno("Confirm Exit", "Are you sure you want to exit?"):
            self.root.quit()

    def open_heat_map_window(self):
        """Open the heat map visualization window"""
        if self.heat_map_window and self.heat_map_window.winfo_exists():
            self.heat_map_window.lift()
            return

        self.heat_map_window = HeatMapWindow(self.root, self.edit_frequency_tracker)

    def track_item_edit(self, outpost, trader, item_code, edit_type="general"):
        """Track an item edit for heat map"""
        self.edit_frequency_tracker.track_edit(outpost, trader, item_code, edit_type)

class HeatMapWindow:
    """Pop-out heat map visualization window with 3 visualization styles"""

    def __init__(self, parent, frequency_tracker):
        self.parent = parent
        self.tracker = frequency_tracker
        self.current_style = "grid"  # grid, list, chart

        # Create window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("🔥 Edit Heat Map - XconomyChooser v2.01")
        self.window.geometry("1200x800")
        self.window.transient(parent)

        # Create interface
        self.create_interface()
        self.refresh_data()

    def create_interface(self):
        """Create the heat map interface"""
        # Header
        header_frame = ctk.CTkFrame(self.window)
        header_frame.pack(fill="x", padx=10, pady=5)

        title_label = ctk.CTkLabel(
            header_frame,
            text="🔥 Edit Frequency Heat Map",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(side="left", padx=10, pady=10)

        # Style selector
        style_frame = ctk.CTkFrame(header_frame)
        style_frame.pack(side="right", padx=10, pady=5)

        ctk.CTkLabel(style_frame, text="Visualization Style:").pack(side="left", padx=5)

        self.style_var = ctk.StringVar(value=self.current_style)
        style_menu = ctk.CTkOptionMenu(
            style_frame,
            variable=self.style_var,
            values=["grid", "list", "chart"],
            command=self.change_style
        )
        style_menu.pack(side="left", padx=5)

        # Statistics panel
        self.stats_frame = ctk.CTkFrame(self.window)
        self.stats_frame.pack(fill="x", padx=10, pady=5)

        # Main visualization area
        self.viz_frame = ctk.CTkFrame(self.window)
        self.viz_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Control buttons
        controls_frame = ctk.CTkFrame(self.window)
        controls_frame.pack(fill="x", padx=10, pady=5)

        refresh_btn = ctk.CTkButton(
            controls_frame,
            text="🔄 Refresh",
            command=self.refresh_data
        )
        refresh_btn.pack(side="left", padx=5)

        reset_btn = ctk.CTkButton(
            controls_frame,
            text="🗑️ Reset Tracking",
            command=self.reset_tracking,
            fg_color="#dc3545"
        )
        reset_btn.pack(side="left", padx=5)

        export_btn = ctk.CTkButton(
            controls_frame,
            text="📊 Export Data",
            command=self.export_data
        )
        export_btn.pack(side="left", padx=5)

        close_btn = ctk.CTkButton(
            controls_frame,
            text="❌ Close",
            command=self.window.destroy
        )
        close_btn.pack(side="right", padx=5)


# Main execution
if __name__ == "__main__":
    app = SCUMEconomyGUI()
    app.root.mainloop()
