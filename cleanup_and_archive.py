#!/usr/bin/env python3
"""
Cleanup and Archive Script for SCUM Economy Chooser Enhanced GUI
Identifies and archives files that are not part of the current build
"""

import os
import shutil
import zipfile
from datetime import datetime

class ProjectCleanup:
    def __init__(self):
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.archive_dir = "archived_files"
        os.makedirs(self.archive_dir, exist_ok=True)
        
        # Define what belongs to the current build
        self.current_build_files = {
            # Core application files
            "scum_economy_gui_enhanced.py",
            "1_45c.py",
            "enhanced_dialogs.py",
            "version_manager.py",
            
            # Configuration files
            "economyoverride.json",
            "custom_buckets.json",
            "gui_layout_devbuild.json",
            "version_info.json",
            
            # Current documentation
            "README_CURRENT_VERSION.md",
            "README_GUI.md",
            "PROJECT_STATUS_SUMMARY.md",
            "XCONOMYCHOOSER_V2_01_RELEASE_NOTES.md",
            
            # Essential test files
            "cli_parity_check.py",
            "wiring_fix_verification.py",
            "comprehensive_method_test.py",
            
            # Project files
            "AUGMENT XCONOMY.code-workspace"
        }
        
        # Directories to keep
        self.current_build_dirs = {
            "backups",
            "releases", 
            "logs",
            "documentation",
            "__pycache__"
        }
        
        # Files/patterns to archive
        self.archive_patterns = {
            # Old development files
            "development_files": [
                "1_45c_Augment.py",
                "scum_economy_gui_complete.py",
                "scum_economy_gui_devbuild_full.py", 
                "scum_economy_gui_improved.py"
            ],
            
            # Test and experimental files
            "test_files": [
                "complete_wiring_test.py",
                "comprehensive_load_save_undo_test.py",
                "database_integration_demo.py",
                "interactive_database_test.py",
                "math_validation_test.py",
                "simple_db_browser.py",
                "test_build_mode.py",
                "test_debug_run.py",
                "test_debug_system.py",
                "test_undo_redo.py",
                "wiring_test.py",
                "wiring_verification.py"
            ],
            
            # Old configuration files
            "old_config_files": [
                "20250609_154056_economy_config.json",
                "20250609_160929_economy_config.json",
                "test_economy.json",
                "scum_economy_test.db",
                "custom_buckets.json.backup_20250609_214534",
                "custom_buckets.json.backup_20250609_214730",
                "economyoverride.json.backup_20250609_214534",
                "economyoverride.json.backup_20250609_214730",
                "gui_layout_devbuild.json.backup_20250609_214534",
                "gui_layout_devbuild.json.backup_20250609_214730"
            ],
            
            # Analysis and planning documents
            "analysis_docs": [
                "COMING_SOON_PLACEHOLDERS_ANALYSIS.md",
                "CUSTOM_BUCKETS_SYSTEM_SUMMARY.md",
                "DATABASE_INTEGRATION_PLAN.md",
                "DATABASE_TOGGLE_IMPLEMENTATION_SUMMARY.md",
                "DUAL_MODE_IMPLEMENTATION_SUMMARY.md",
                "ENHANCED_GUI_SUMMARY.md",
                "FINAL_WIRING_VERIFICATION.md",
                "FUNCTION_COMPARISON_ANALYSIS.md",
                "INTEGRATION_ANALYSIS.md",
                "LOAD_SAVE_UNDO_TEST_RESULTS.md",
                "PLACEHOLDERS_ELIMINATED_SUMMARY.md",
                "RESOURCE_INTEGRATION_PLAN.md"
            ],
            
            # Reference materials
            "reference_files": [
                "FISH logix explained.txt",
                "Scum Admin Suite Gui.pdf",
                "Scum Admin Suite.txt",
                "xconomychooser_explained.txt"
            ]
        }
    
    def analyze_project(self):
        """Analyze current project structure"""
        print("🔍 ANALYZING PROJECT STRUCTURE")
        print("=" * 50)
        
        all_files = []
        all_dirs = []
        
        for item in os.listdir("."):
            if os.path.isfile(item):
                all_files.append(item)
            elif os.path.isdir(item):
                all_dirs.append(item)
        
        print(f"📁 Total files: {len(all_files)}")
        print(f"📂 Total directories: {len(all_dirs)}")
        
        # Categorize files
        current_files = []
        archive_files = []
        
        for file in all_files:
            if file in self.current_build_files:
                current_files.append(file)
            else:
                # Check if it matches any archive pattern
                should_archive = False
                for category, patterns in self.archive_patterns.items():
                    if file in patterns:
                        archive_files.append((file, category))
                        should_archive = True
                        break
                
                if not should_archive:
                    # Unknown file - let user decide
                    archive_files.append((file, "unknown"))
        
        print(f"\n✅ Current build files: {len(current_files)}")
        print(f"📦 Files to archive: {len(archive_files)}")
        
        return current_files, archive_files, all_dirs
    
    def create_archive_structure(self, archive_files):
        """Create organized archive structure"""
        print("\n📦 CREATING ARCHIVE STRUCTURE")
        print("-" * 30)
        
        # Group files by category
        categorized = {}
        for file, category in archive_files:
            if category not in categorized:
                categorized[category] = []
            categorized[category].append(file)
        
        # Create archive directories
        archive_base = os.path.join(self.archive_dir, f"Archived_Files_{self.timestamp}")
        os.makedirs(archive_base, exist_ok=True)
        
        for category, files in categorized.items():
            category_dir = os.path.join(archive_base, category)
            os.makedirs(category_dir, exist_ok=True)
            
            print(f"📁 {category}: {len(files)} files")
            
            for file in files:
                if os.path.exists(file):
                    shutil.copy2(file, category_dir)
                    print(f"  📄 {file}")
        
        return archive_base
    
    def archive_historic_directory(self):
        """Archive the entire historic directory"""
        print("\n🕰️ ARCHIVING HISTORIC DIRECTORY")
        print("-" * 30)
        
        historic_dir = "Xconomy resource and code ideas"
        if not os.path.exists(historic_dir):
            print("❌ Historic directory not found")
            return None
        
        # Create ZIP of entire historic directory
        zip_path = os.path.join(self.archive_dir, f"Historic_Development_Files_{self.timestamp}.zip")
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(historic_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    arc_path = os.path.relpath(file_path, ".")
                    zipf.write(file_path, arc_path)
        
        print(f"✅ Historic directory archived: {zip_path}")
        return zip_path
    
    def create_cleanup_summary(self, current_files, archive_files, archive_base, historic_zip):
        """Create cleanup summary document"""
        summary_content = f"""# Project Cleanup Summary - {self.timestamp}

## 🎯 Cleanup Operation Results

### ✅ Current Build Files (Preserved)
**Total: {len(current_files)} files**

#### Core Application:
- scum_economy_gui_enhanced.py (Main enhanced GUI)
- 1_45c.py (Original CLI - preserved)
- enhanced_dialogs.py (Dialog components)
- version_manager.py (Version management system)

#### Configuration:
- economyoverride.json (Economy data)
- custom_buckets.json (Custom bucket configurations)
- gui_layout_devbuild.json (GUI layout settings)
- version_info.json (Version tracking)

#### Documentation:
- README_CURRENT_VERSION.md (Current version guide)
- README_GUI.md (GUI documentation)
- PROJECT_STATUS_SUMMARY.md (Project status)
- XCONOMYCHOOSER_V2_01_RELEASE_NOTES.md (Release notes)

#### Testing:
- cli_parity_check.py (CLI parity verification)
- wiring_fix_verification.py (JSON structure testing)
- comprehensive_method_test.py (Complete functionality testing)

### 📦 Archived Files
**Total: {len(archive_files)} files**

"""
        
        # Group archived files by category
        categorized = {}
        for file, category in archive_files:
            if category not in categorized:
                categorized[category] = []
            categorized[category].append(file)
        
        for category, files in categorized.items():
            summary_content += f"\n#### {category.replace('_', ' ').title()} ({len(files)} files):\n"
            for file in sorted(files):
                summary_content += f"- {file}\n"
        
        summary_content += f"""
### 🕰️ Historic Development Archive
- **Archive:** Historic_Development_Files_{self.timestamp}.zip
- **Contents:** Complete "Xconomy resource and code ideas" directory
- **Purpose:** Preserve development history and reference materials

### 📁 Directory Structure After Cleanup

#### Active Project:
```
AUGMENT XCONOMY/
├── 📦 Core Files
│   ├── scum_economy_gui_enhanced.py
│   ├── 1_45c.py
│   ├── enhanced_dialogs.py
│   └── version_manager.py
├── ⚙️ Configuration
│   ├── economyoverride.json
│   ├── custom_buckets.json
│   ├── gui_layout_devbuild.json
│   └── version_info.json
├── 📚 Documentation
│   ├── README_CURRENT_VERSION.md
│   ├── README_GUI.md
│   ├── PROJECT_STATUS_SUMMARY.md
│   └── XCONOMYCHOOSER_V2_01_RELEASE_NOTES.md
├── 🧪 Testing
│   ├── cli_parity_check.py
│   ├── wiring_fix_verification.py
│   └── comprehensive_method_test.py
├── 🗂️ System Directories
│   ├── backups/ (Version backups)
│   ├── releases/ (Release packages)
│   ├── logs/ (Operation logs)
│   └── __pycache__/ (Python cache)
└── 📦 Archived
    └── archived_files/ (Cleanup archives)
```

## 🎯 Benefits of Cleanup

### ✅ Improved Organization
- Clear separation of active vs. archived files
- Reduced clutter in main directory
- Easier navigation and maintenance

### ✅ Preserved History
- All development files safely archived
- Historic versions preserved in ZIP format
- Complete project evolution documented

### ✅ Streamlined Development
- Focus on current build files only
- Faster file operations and searches
- Cleaner project structure

## 🔄 Recovery Instructions

### To Restore Archived Files:
1. Navigate to `archived_files/` directory
2. Extract desired archive
3. Copy files back to main directory

### To Access Historic Versions:
1. Extract `Historic_Development_Files_{self.timestamp}.zip`
2. Browse historic development files
3. Reference previous implementations

---
**Cleanup completed:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}  
**Archive location:** {archive_base}  
**Historic archive:** {historic_zip if historic_zip else 'Not created'}
"""
        
        summary_path = os.path.join(archive_base, "CLEANUP_SUMMARY.md")
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        return summary_path
    
    def run_cleanup(self):
        """Run the complete cleanup process"""
        print("🧹 SCUM ECONOMY CHOOSER - PROJECT CLEANUP")
        print("=" * 60)
        
        # Analyze project
        current_files, archive_files, all_dirs = self.analyze_project()
        
        # Show what will be archived
        print(f"\n📋 CLEANUP PLAN:")
        print(f"✅ Keep: {len(current_files)} current build files")
        print(f"📦 Archive: {len(archive_files)} old/test files")
        print(f"🕰️ Archive: Historic development directory")
        
        # Confirm with user
        response = input(f"\n❓ Proceed with cleanup? (y/N): ").strip().lower()
        if response != 'y':
            print("❌ Cleanup cancelled")
            return
        
        # Create archives
        archive_base = self.create_archive_structure(archive_files)
        historic_zip = self.archive_historic_directory()
        
        # Create summary
        summary_path = self.create_cleanup_summary(current_files, archive_files, archive_base, historic_zip)
        
        print(f"\n✅ CLEANUP COMPLETE!")
        print(f"📁 Archive created: {archive_base}")
        print(f"📄 Summary: {summary_path}")
        if historic_zip:
            print(f"🕰️ Historic archive: {historic_zip}")
        
        print(f"\n🎯 Project is now clean and organized!")
        print(f"📦 {len(archive_files)} files archived")
        print(f"✅ {len(current_files)} active files remain")

def main():
    cleanup = ProjectCleanup()
    cleanup.run_cleanup()

if __name__ == "__main__":
    main()
