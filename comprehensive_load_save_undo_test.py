#!/usr/bin/env python3
"""
Comprehensive Load/Save/Undo Testing Script
Tests all core functionality of the SCUM Economy Chooser
"""

import json
import os
import shutil
import time
from datetime import datetime

def create_test_economy_file():
    """Create a test economy file for testing"""
    test_data = {
        "tradeable-code": {
            "Weapon_AK47": {
                "base-purchase-price": 1000,
                "base-sell-price": 500,
                "max-purchase-price": 2000,
                "max-sell-price": 1000,
                "min-purchase-price": 100,
                "min-sell-price": 50
            },
            "2H_Baseball_Bat": {
                "base-purchase-price": 150,
                "base-sell-price": 75,
                "max-purchase-price": 300,
                "max-sell-price": 150,
                "min-purchase-price": 15,
                "min-sell-price": 7
            },
            "1H_ImprovisedKnife": {
                "base-purchase-price": 50,
                "base-sell-price": 25,
                "max-purchase-price": 100,
                "max-sell-price": 50,
                "min-purchase-price": 5,
                "min-sell-price": 2
            },
            "Crafted_Backpack": {
                "base-purchase-price": 200,
                "base-sell-price": 100,
                "max-purchase-price": 400,
                "max-sell-price": 200,
                "min-purchase-price": 20,
                "min-sell-price": 10
            },
            "Fish_Bass": {
                "base-purchase-price": 30,
                "base-sell-price": 15,
                "max-purchase-price": 60,
                "max-sell-price": 30,
                "min-purchase-price": 3,
                "min-sell-price": 1
            },
            "Cal_9mm": {
                "base-purchase-price": 5,
                "base-sell-price": 2,
                "max-purchase-price": 10,
                "max-sell-price": 5,
                "min-purchase-price": 1,
                "min-sell-price": 1
            }
        }
    }
    
    with open('test_economy_comprehensive.json', 'w') as f:
        json.dump(test_data, f, indent=2)
    
    print("✅ Created test economy file: test_economy_comprehensive.json")
    return test_data

def backup_original_files():
    """Backup original files before testing"""
    backups = []
    
    files_to_backup = [
        'economyoverride.json',
        'custom_buckets.json',
        'gui_layout_devbuild.json'
    ]
    
    for file in files_to_backup:
        if os.path.exists(file):
            backup_name = f"{file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(file, backup_name)
            backups.append((file, backup_name))
            print(f"📁 Backed up {file} → {backup_name}")
    
    return backups

def test_file_operations():
    """Test basic file load/save operations"""
    print("\n🧪 Testing File Operations...")
    
    # Test 1: Create and load test file
    test_data = create_test_economy_file()
    
    # Test 2: Verify file exists and is readable
    if os.path.exists('test_economy_comprehensive.json'):
        with open('test_economy_comprehensive.json', 'r') as f:
            loaded_data = json.load(f)
        
        if loaded_data == test_data:
            print("✅ File creation and loading: PASSED")
        else:
            print("❌ File creation and loading: FAILED")
            return False
    else:
        print("❌ Test file creation: FAILED")
        return False
    
    # Test 3: Modify and save
    loaded_data['tradeable-code']['Weapon_AK47']['base-purchase-price'] = 1500
    
    with open('test_economy_comprehensive.json', 'w') as f:
        json.dump(loaded_data, f, indent=2)
    
    # Test 4: Verify modification
    with open('test_economy_comprehensive.json', 'r') as f:
        modified_data = json.load(f)
    
    if modified_data['tradeable-code']['Weapon_AK47']['base-purchase-price'] == 1500:
        print("✅ File modification and save: PASSED")
    else:
        print("❌ File modification and save: FAILED")
        return False
    
    return True

def test_categorization_logic():
    """Test F.I.S.H. categorization logic"""
    print("\n🐟 Testing F.I.S.H. Categorization Logic...")
    
    test_items = [
        ("Weapon_AK47", "Should be categorized as Firearms"),
        ("2H_Baseball_Bat", "Should be categorized as Two-Handed Weapons"),
        ("1H_ImprovisedKnife", "Should be categorized as Improvised Weapons"),
        ("Crafted_Backpack", "Should be categorized as Crafted Items"),
        ("Fish_Bass", "Should be categorized as Fish"),
        ("Cal_9mm", "Should be categorized as Ammunition"),
        ("Weapon_Improvised_Handgun", "Should be categorized as Improvised Weapons"),
        ("Magazine_AK47", "Should be categorized as Magazines"),
        ("WeaponScope_M82A1", "Should be categorized as Weapon Scopes")
    ]
    
    # Import the categorization logic
    try:
        from scum_economy_gui_enhanced import FISHLogicEngine
        fish_engine = FISHLogicEngine()
        
        for item_code, expected in test_items:
            result = fish_engine.categorize_item(item_code)
            print(f"🔍 {item_code}: {result.get('category', 'Unknown')} - {expected}")
        
        print("✅ F.I.S.H. Logic categorization: TESTED")
        return True
        
    except Exception as e:
        print(f"❌ F.I.S.H. Logic test failed: {e}")
        return False

def test_undo_redo_logic():
    """Test undo/redo functionality"""
    print("\n↩️ Testing Undo/Redo Logic...")
    
    try:
        from scum_economy_gui_enhanced import UndoRedoManager
        undo_manager = UndoRedoManager()
        
        # Test 1: Add some changes
        change1 = {
            'type': 'price_change',
            'item': 'Weapon_AK47',
            'field': 'base-purchase-price',
            'old_value': 1000,
            'new_value': 1500,
            'description': 'Test change 1'
        }
        
        change2 = {
            'type': 'price_change',
            'item': '2H_Baseball_Bat',
            'field': 'base-purchase-price',
            'old_value': 150,
            'new_value': 200,
            'description': 'Test change 2'
        }
        
        # Use save_state instead of add_change
        test_data1 = {"test": "data1"}
        test_data2 = {"test": "data2"}

        undo_manager.save_state(test_data1, "Test state 1")
        undo_manager.save_state(test_data2, "Test state 2")
        
        print(f"📝 Added 2 changes, history size: {len(undo_manager.history)}")
        
        # Test 2: Undo operations
        if undo_manager.can_undo():
            undone = undo_manager.undo()
            print(f"↩️ Undone: {undone.get('description', 'Unknown')}")
        
        if undo_manager.can_undo():
            undone = undo_manager.undo()
            print(f"↩️ Undone: {undone.get('description', 'Unknown')}")
        
        # Test 3: Redo operations
        if undo_manager.can_redo():
            redone = undo_manager.redo()
            print(f"↪️ Redone: {redone.get('description', 'Unknown')}")
        
        print("✅ Undo/Redo logic: PASSED")
        return True
        
    except Exception as e:
        print(f"❌ Undo/Redo test failed: {e}")
        return False

def test_custom_buckets():
    """Test custom bucket functionality"""
    print("\n🪣 Testing Custom Buckets...")
    
    try:
        # Create test custom bucket
        test_bucket = {
            "name": "Test Weapons",
            "description": "Test bucket for weapons",
            "filters": [
                {"type": "starts_with", "value": "Weapon_"},
                {"type": "not_contains", "value": "Parts"}
            ],
            "items": ["Weapon_AK47", "Weapon_M16"]
        }
        
        # Save to file
        custom_buckets = {"test_weapons": test_bucket}
        
        with open('test_custom_buckets.json', 'w') as f:
            json.dump(custom_buckets, f, indent=2)
        
        # Load and verify
        with open('test_custom_buckets.json', 'r') as f:
            loaded_buckets = json.load(f)
        
        if loaded_buckets == custom_buckets:
            print("✅ Custom bucket save/load: PASSED")
            return True
        else:
            print("❌ Custom bucket save/load: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Custom bucket test failed: {e}")
        return False

def cleanup_test_files():
    """Clean up test files"""
    test_files = [
        'test_economy_comprehensive.json',
        'test_custom_buckets.json'
    ]
    
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️ Cleaned up: {file}")

def main():
    """Run comprehensive tests"""
    print("🧪 COMPREHENSIVE LOAD/SAVE/UNDO TESTING")
    print("=" * 50)
    
    # Backup original files
    backups = backup_original_files()
    
    try:
        # Run tests
        tests = [
            ("File Operations", test_file_operations),
            ("F.I.S.H. Categorization", test_categorization_logic),
            ("Undo/Redo Logic", test_undo_redo_logic),
            ("Custom Buckets", test_custom_buckets)
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name} crashed: {e}")
                results.append((test_name, False))
        
        # Summary
        print("\n📊 TEST RESULTS SUMMARY")
        print("=" * 30)
        
        passed = 0
        for test_name, result in results:
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
        
        if passed == len(results):
            print("🎉 ALL TESTS PASSED!")
        else:
            print("⚠️ Some tests failed - check logs above")
    
    finally:
        # Cleanup
        cleanup_test_files()
        print("\n🧹 Test cleanup completed")

if __name__ == "__main__":
    main()
