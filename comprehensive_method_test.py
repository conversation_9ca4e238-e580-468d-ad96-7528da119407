#!/usr/bin/env python3
"""
Comprehensive Method Test for SCUM Economy Chooser Enhanced GUI
Tests all functionality including buckets, categories, F.I.S.H. logic, and mode restrictions
"""

import json
import os
import sys
import time
from datetime import datetime

def test_json_loading():
    """Test if JSON files can be loaded"""
    print("🧪 Testing JSON Loading...")
    
    json_files = [f for f in os.listdir('.') if f.endswith('.json') and 'economy' in f.lower()]
    print(f"Found {len(json_files)} economy JSON files:")
    
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Check structure
            if 'economy-override' in data:
                traders = data['economy-override'].get('traders', {})
                total_items = sum(len(items) for items in traders.values() if isinstance(items, list))
                print(f"  ✅ {json_file}: {len(traders)} traders, {total_items} items")
            else:
                print(f"  ⚠️ {json_file}: No economy-override structure")
                
        except Exception as e:
            print(f"  ❌ {json_file}: Error - {e}")
    
    return json_files

def test_bucket_system():
    """Test bucket system functionality"""
    print("\n🪣 Testing Bucket System...")
    
    # Test bucket file loading
    bucket_file = "custom_buckets.json"
    if os.path.exists(bucket_file):
        try:
            with open(bucket_file, 'r', encoding='utf-8') as f:
                buckets = json.load(f)
            print(f"  ✅ Loaded {len(buckets)} custom buckets")
            
            for bucket_key, bucket_data in buckets.items():
                name = bucket_data.get('name', 'Unknown')
                filters = bucket_data.get('filters', [])
                print(f"    🪣 {name}: {len(filters)} filters")
                
        except Exception as e:
            print(f"  ❌ Error loading buckets: {e}")
    else:
        print("  ⚠️ No custom_buckets.json found")

def test_fish_logic_rules():
    """Test F.I.S.H. Logic rules and categorization"""
    print("\n🐟 Testing F.I.S.H. Logic Rules...")
    
    # Import the F.I.S.H. engine
    try:
        sys.path.append('.')
        from scum_economy_gui_enhanced import FISHLogicEngine
        
        fish_engine = FISHLogicEngine()
        print(f"  ✅ F.I.S.H. Engine loaded with {len(fish_engine.rules)} rules")
        
        # Test categorization with sample items
        test_items = [
            "Weapon_AK47",
            "2H_Baseball_Bat", 
            "1H_ImprovisedKnife",
            "Fish_Bass",
            "Weapon_Improvised_Handgun",
            "Cal_7_62x39mm",
            "Military_Shirt",
            "WeaponScope_ACOG"
        ]
        
        print("  📊 Testing categorization:")
        for item in test_items:
            result = fish_engine.categorize_item(item)
            category = result['category']
            priority = result['priority']
            print(f"    {item} → {category} ({priority})")
            
    except Exception as e:
        print(f"  ❌ Error testing F.I.S.H. Logic: {e}")

def test_bucket_filtering():
    """Test bucket filtering logic"""
    print("\n🔍 Testing Bucket Filtering...")
    
    # Test filter types
    test_filters = [
        {"type": "starts_with", "value": "Weapon_"},
        {"type": "contains", "value": "fish"},
        {"type": "not_contains", "value": "parts"},
        {"type": "contains_any", "value": ["food", "can_", "meat"]}
    ]
    
    test_items = [
        "Weapon_AK47",
        "Fish_Bass", 
        "Weapon_Parts_Barrel",
        "Can_Food_Beans",
        "Tool_Hammer"
    ]
    
    print("  🧪 Testing filter logic:")
    for filter_rule in test_filters:
        filter_type = filter_rule["type"]
        filter_value = filter_rule["value"]
        print(f"    Filter: {filter_type} = {filter_value}")
        
        for item in test_items:
            matches = apply_test_filter(item, filter_rule)
            status = "✅" if matches else "❌"
            print(f"      {status} {item}")

def apply_test_filter(item_code, filter_rule):
    """Apply a single filter rule for testing"""
    filter_type = filter_rule["type"]
    filter_value = filter_rule["value"]
    item_code_lower = item_code.lower()
    
    if filter_type == "starts_with":
        return item_code.startswith(filter_value)
    elif filter_type == "contains":
        return filter_value.lower() in item_code_lower
    elif filter_type == "not_contains":
        return filter_value.lower() not in item_code_lower
    elif filter_type == "contains_any":
        return any(val.lower() in item_code_lower for val in filter_value)
    
    return False

def test_mode_restrictions():
    """Test mode-specific feature restrictions"""
    print("\n🎯 Testing Mode Restrictions...")
    
    # This would need to be tested in the actual GUI
    print("  📝 Mode restriction tests (requires GUI interaction):")
    print("    - Normal Mode: Should hide F.I.S.H. tools")
    print("    - Normal Mode: Should hide professional tools") 
    print("    - Normal Mode: Should show bucket management")
    print("    - Advanced Mode: Should show all features")
    print("    - Advanced Mode: Should allow bucket rule editing")

def test_category_definitions():
    """Test predefined category definitions"""
    print("\n📂 Testing Category Definitions...")

    try:
        # Test the updated category definitions from the GUI (Essential Categories)
        category_definitions = {
            "fish": {
                "name": "🐟 Fish Items",
                "filter": lambda code: code.startswith('Fish_') and not any(x in code.lower() for x in ['fishing', 'rod']),
                "description": "All fish items for consumption"
            },
            "weapons": {
                "name": "🔫 Weapons",
                "filter": lambda code: code.startswith('Weapon_') and not any(x in code.lower() for x in ['parts', 'attachment']),
                "description": "Combat weapons"
            },
            "improvised": {
                "name": "🔧 Improvised Items",
                "filter": lambda code: 'improvised' in code.lower() or any(x in code for x in ['1H_Improvised', '2H_Improvised', 'Weapon_Improvised']),
                "description": "Player-crafted improvised weapons and tools"
            },
            "crafted": {
                "name": "🛠️ Crafted Items",
                "filter": lambda code: any(x in code.lower() for x in ['crafted', 'handmade', 'makeshift']) or code.startswith('Craft_'),
                "description": "Items that can be crafted by players"
            },
            "two_handed": {
                "name": "⚔️ Two-Handed Weapons",
                "filter": lambda code: code.startswith('2H_') or '2h_' in code.lower(),
                "description": "Two-handed weapons and tools"
            },
            "medical": {
                "name": "💊 Medical Items",
                "filter": lambda code: any(x in code.lower() for x in ['bandage', 'pill', 'syringe', 'medical']),
                "description": "Medical supplies and healing items"
            },
            "military": {
                "name": "🎖️ Military Gear",
                "filter": lambda code: any(x in code for x in ['Military', 'Army', 'Combat']) and not code.startswith('Weapon_'),
                "description": "Military equipment (non-weapon)"
            }
        }
        
        test_items = [
            "Fish_Bass",
            "Fish_Fishing_Rod",  # Should be excluded from fish
            "Weapon_AK47",
            "Weapon_Parts_Barrel",  # Should be excluded from weapons
            "1H_ImprovisedKnife",
            "Weapon_Improvised_Handgun",
            "2H_Baseball_Bat",
            "Craft_Bandage",
            "Military_Shirt",
            "Handmade_Bow"
        ]
        
        print("  🧪 Testing category filters:")
        for category_key, category_data in category_definitions.items():
            print(f"    📂 {category_data['name']}:")
            for item in test_items:
                matches = category_data['filter'](item)
                status = "✅" if matches else "❌"
                print(f"      {status} {item}")
                
    except Exception as e:
        print(f"  ❌ Error testing categories: {e}")

def main():
    """Run all tests"""
    print("🔧 COMPREHENSIVE METHOD TEST - SCUM Economy Chooser Enhanced GUI")
    print("=" * 70)
    print(f"Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Run all tests
    json_files = test_json_loading()
    test_bucket_system()
    test_fish_logic_rules()
    test_bucket_filtering()
    test_mode_restrictions()
    test_category_definitions()
    
    print("\n" + "=" * 70)
    print("🎯 MANUAL GUI TESTS REQUIRED:")
    print("1. ✋ Load a JSON file and verify data appears in tree")
    print("2. 🪣 Click bucket buttons and verify items are found")
    print("3. ➕ Create new custom bucket with filters")
    print("4. ⚙️ Manage buckets - edit/delete in Advanced mode")
    print("5. 🔄 Switch between Normal/Advanced modes")
    print("6. 🐟 Run F.I.S.H. analysis in Advanced mode")
    print("7. 📊 Check if predefined searches are locked in Normal mode")
    print("8. 🖱️ Test drag-and-drop between bucket windows (if implemented)")
    
    print(f"\nTest completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
