#!/usr/bin/env python3
"""
Database Integration Demo for SCUM Economy Chooser
Demonstrates SQLite integration with credential management
"""

import sqlite3
import json
import os
from datetime import datetime
from dataclasses import dataclass
from typing import Optional, List, Dict, Any

@dataclass
class DatabaseConfig:
    """Database configuration with credential management"""
    db_type: str = "sqlite"
    sqlite_path: str = "./demo_economy.db"
    host: str = "localhost"
    port: int = 3306
    database: str = "scum_economy"
    username: str = ""
    password: str = ""
    
    @classmethod
    def from_env(cls):
        """Load configuration from environment variables"""
        return cls(
            db_type=os.getenv('DB_TYPE', 'sqlite'),
            sqlite_path=os.getenv('SQLITE_DB_PATH', './demo_economy.db'),
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', '3306')),
            database=os.getenv('DB_NAME', 'scum_economy'),
            username=os.getenv('DB_USER', ''),
            password=os.getenv('DB_PASSWORD', '')
        )

class EconomyDatabaseManager:
    """Database manager for SCUM Economy data"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.connection = None
        self.setup_database()
    
    def setup_database(self):
        """Initialize database connection and create tables"""
        if self.config.db_type == "sqlite":
            self.connection = sqlite3.connect(self.config.sqlite_path)
            self.connection.row_factory = sqlite3.Row  # Enable dict-like access
        else:
            raise NotImplementedError(f"Database type {self.config.db_type} not implemented yet")
        
        self.create_tables()
        print(f"✅ Database initialized: {self.config.sqlite_path}")
    
    def create_tables(self):
        """Create database tables"""
        cursor = self.connection.cursor()
        
        # Economy items table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS economy_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tradeable_code TEXT UNIQUE NOT NULL,
                base_purchase_price INTEGER NOT NULL,
                base_sell_price INTEGER NOT NULL,
                max_purchase_price INTEGER NOT NULL,
                max_sell_price INTEGER NOT NULL,
                min_purchase_price INTEGER NOT NULL,
                min_sell_price INTEGER NOT NULL,
                category TEXT,
                subcategory TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Change history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS change_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                tradeable_code TEXT NOT NULL,
                field_name TEXT NOT NULL,
                old_value INTEGER,
                new_value INTEGER,
                change_type TEXT,
                description TEXT,
                user_id TEXT,
                session_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (tradeable_code) REFERENCES economy_items(tradeable_code)
            )
        ''')
        
        # Custom buckets table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS custom_buckets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bucket_name TEXT UNIQUE NOT NULL,
                description TEXT,
                filters TEXT,  -- JSON string
                user_id TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        self.connection.commit()
        print("✅ Database tables created")
    
    def insert_economy_item(self, tradeable_code: str, base_purchase_price: int, 
                           base_sell_price: int, max_purchase_price: int,
                           max_sell_price: int, min_purchase_price: int,
                           min_sell_price: int, category: str = None):
        """Insert or update an economy item"""
        cursor = self.connection.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO economy_items 
            (tradeable_code, base_purchase_price, base_sell_price, 
             max_purchase_price, max_sell_price, min_purchase_price, 
             min_sell_price, category, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (tradeable_code, base_purchase_price, base_sell_price,
              max_purchase_price, max_sell_price, min_purchase_price,
              min_sell_price, category))
        
        self.connection.commit()
        return cursor.lastrowid
    
    def get_economy_item(self, tradeable_code: str) -> Optional[Dict]:
        """Get a specific economy item"""
        cursor = self.connection.cursor()
        cursor.execute('SELECT * FROM economy_items WHERE tradeable_code = ?', (tradeable_code,))
        row = cursor.fetchone()
        return dict(row) if row else None
    
    def get_all_economy_items(self) -> List[Dict]:
        """Get all economy items"""
        cursor = self.connection.cursor()
        cursor.execute('SELECT * FROM economy_items ORDER BY tradeable_code')
        return [dict(row) for row in cursor.fetchall()]
    
    def log_change(self, tradeable_code: str, field_name: str, old_value: int, 
                   new_value: int, change_type: str = "manual", 
                   description: str = "", user_id: str = "system",
                   session_id: str = "demo"):
        """Log a change to the change history"""
        cursor = self.connection.cursor()
        
        cursor.execute('''
            INSERT INTO change_history 
            (tradeable_code, field_name, old_value, new_value, change_type, 
             description, user_id, session_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (tradeable_code, field_name, old_value, new_value, 
              change_type, description, user_id, session_id))
        
        self.connection.commit()
        return cursor.lastrowid
    
    def get_change_history(self, tradeable_code: str = None) -> List[Dict]:
        """Get change history for an item or all items"""
        cursor = self.connection.cursor()
        
        if tradeable_code:
            cursor.execute('''
                SELECT * FROM change_history 
                WHERE tradeable_code = ? 
                ORDER BY created_at DESC
            ''', (tradeable_code,))
        else:
            cursor.execute('SELECT * FROM change_history ORDER BY created_at DESC')
        
        return [dict(row) for row in cursor.fetchall()]
    
    def save_custom_bucket(self, bucket_name: str, description: str, 
                          filters: Dict, user_id: str = "system"):
        """Save a custom bucket"""
        cursor = self.connection.cursor()
        
        cursor.execute('''
            INSERT OR REPLACE INTO custom_buckets 
            (bucket_name, description, filters, user_id, updated_at)
            VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
        ''', (bucket_name, description, json.dumps(filters), user_id))
        
        self.connection.commit()
        return cursor.lastrowid
    
    def get_custom_buckets(self, user_id: str = None) -> List[Dict]:
        """Get custom buckets"""
        cursor = self.connection.cursor()
        
        if user_id:
            cursor.execute('SELECT * FROM custom_buckets WHERE user_id = ?', (user_id,))
        else:
            cursor.execute('SELECT * FROM custom_buckets')
        
        buckets = []
        for row in cursor.fetchall():
            bucket = dict(row)
            bucket['filters'] = json.loads(bucket['filters'])
            buckets.append(bucket)
        
        return buckets
    
    def migrate_from_json(self, json_file_path: str):
        """Migrate data from JSON file to database"""
        if not os.path.exists(json_file_path):
            print(f"❌ JSON file not found: {json_file_path}")
            return False
        
        with open(json_file_path, 'r') as f:
            data = json.load(f)
        
        items_migrated = 0
        for code, item_data in data.get('tradeable-code', {}).items():
            try:
                self.insert_economy_item(
                    tradeable_code=code,
                    base_purchase_price=item_data.get('base-purchase-price', 0),
                    base_sell_price=item_data.get('base-sell-price', 0),
                    max_purchase_price=item_data.get('max-purchase-price', 0),
                    max_sell_price=item_data.get('max-sell-price', 0),
                    min_purchase_price=item_data.get('min-purchase-price', 0),
                    min_sell_price=item_data.get('min-sell-price', 0)
                )
                items_migrated += 1
            except Exception as e:
                print(f"❌ Failed to migrate {code}: {e}")
        
        print(f"✅ Migrated {items_migrated} items from {json_file_path}")
        return True
    
    def export_to_json(self, output_file: str):
        """Export database data to JSON format"""
        items = self.get_all_economy_items()
        
        json_data = {
            "tradeable-code": {
                item['tradeable_code']: {
                    "base-purchase-price": item['base_purchase_price'],
                    "base-sell-price": item['base_sell_price'],
                    "max-purchase-price": item['max_purchase_price'],
                    "max-sell-price": item['max_sell_price'],
                    "min-purchase-price": item['min_purchase_price'],
                    "min-sell-price": item['min_sell_price']
                }
                for item in items
            }
        }
        
        with open(output_file, 'w') as f:
            json.dump(json_data, f, indent=2)
        
        print(f"✅ Exported {len(items)} items to {output_file}")
    
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            print("✅ Database connection closed")

def demo_database_operations():
    """Demonstrate database operations"""
    print("🗄️ DATABASE INTEGRATION DEMO")
    print("=" * 40)
    
    # Load configuration
    config = DatabaseConfig.from_env()
    print(f"📋 Using database: {config.db_type} at {config.sqlite_path}")
    
    # Initialize database manager
    db_manager = EconomyDatabaseManager(config)
    
    try:
        # Demo 1: Insert sample data
        print("\n📝 Demo 1: Inserting sample data...")
        sample_items = [
            ("Weapon_AK47", 1000, 500, 2000, 1000, 100, 50),
            ("2H_Baseball_Bat", 150, 75, 300, 150, 15, 7),
            ("1H_ImprovisedKnife", 50, 25, 100, 50, 5, 2),
            ("Crafted_Backpack", 200, 100, 400, 200, 20, 10),
            ("Fish_Bass", 30, 15, 60, 30, 3, 1)
        ]
        
        for item in sample_items:
            db_manager.insert_economy_item(*item)
        
        print(f"✅ Inserted {len(sample_items)} sample items")
        
        # Demo 2: Query data
        print("\n🔍 Demo 2: Querying data...")
        ak47 = db_manager.get_economy_item("Weapon_AK47")
        if ak47:
            print(f"🔫 AK47: Purchase ${ak47['base_purchase_price']}, Sell ${ak47['base_sell_price']}")
        
        # Demo 3: Log changes
        print("\n📊 Demo 3: Logging changes...")
        db_manager.log_change(
            tradeable_code="Weapon_AK47",
            field_name="base-purchase-price",
            old_value=1000,
            new_value=1200,
            change_type="manual",
            description="Price increase for balance"
        )
        
        # Demo 4: Custom buckets
        print("\n🪣 Demo 4: Custom buckets...")
        weapon_bucket = {
            "filters": [
                {"type": "starts_with", "value": "Weapon_"},
                {"type": "not_contains", "value": "Parts"}
            ]
        }
        
        db_manager.save_custom_bucket(
            bucket_name="Combat Weapons",
            description="Primary combat weapons",
            filters=weapon_bucket
        )
        
        buckets = db_manager.get_custom_buckets()
        print(f"✅ Saved custom bucket: {buckets[0]['bucket_name']}")
        
        # Demo 5: Export to JSON
        print("\n📤 Demo 5: Export to JSON...")
        db_manager.export_to_json("demo_export.json")
        
        # Demo 6: Show statistics
        print("\n📊 Demo 6: Database statistics...")
        all_items = db_manager.get_all_economy_items()
        changes = db_manager.get_change_history()
        buckets = db_manager.get_custom_buckets()
        
        print(f"📦 Total items: {len(all_items)}")
        print(f"📝 Total changes: {len(changes)}")
        print(f"🪣 Total buckets: {len(buckets)}")
        
        print("\n🎉 Database demo completed successfully!")
        
    finally:
        db_manager.close()
        
        # Cleanup demo files
        if os.path.exists("demo_economy.db"):
            os.remove("demo_economy.db")
            print("🗑️ Cleaned up demo database")
        
        if os.path.exists("demo_export.json"):
            os.remove("demo_export.json")
            print("🗑️ Cleaned up demo export file")

if __name__ == "__main__":
    demo_database_operations()
