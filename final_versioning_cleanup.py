#!/usr/bin/env python3
"""
Final Versioning Cleanup - Remove old unversioned files, keep only properly versioned ones
"""

import os
import shutil
from datetime import datetime

def final_cleanup():
    """Remove old unversioned files and keep only properly versioned ones"""
    
    print("🧹 FINAL VERSIONING CLEANUP")
    print("=" * 50)
    
    # Files to remove (old unversioned versions)
    files_to_remove = [
        "scum_economy_gui_enhanced.py",  # Keep XconomyChooser_Enhanced_GUI_v2.03.py
        "enhanced_dialogs.py",           # Keep XconomyChooser_Dialogs_v2.03.py  
        "version_manager.py",            # Keep XconomyChooser_VersionManager_v2.03.py
        "cleanup_and_archive.py",       # Keep XconomyChooser_Cleanup_v2.03.py
        "aggressive_cleanup.py",        # Keep XconomyChooser_AggressiveCleanup_v2.03.py
        "retroactive_versioning_cleanup.py"  # Keep XconomyChooser_RetroVersioning_v2.03.py
    ]
    
    # Keep 1_45c.py as it's the original CLI and properly named
    
    # Files to keep (properly versioned)
    versioned_files = [
        "XconomyChooser_Enhanced_GUI_v2.03.py",
        "XconomyChooser_CLI_v1.45c.py", 
        "XconomyChooser_Dialogs_v2.03.py",
        "XconomyChooser_VersionManager_v2.03.py",
        "XconomyChooser_Cleanup_v2.03.py",
        "XconomyChooser_AggressiveCleanup_v2.03.py",
        "XconomyChooser_RetroVersioning_v2.03.py",
        "1_45c.py"  # Original CLI - keep both versions for compatibility
    ]
    
    # Configuration and documentation files to keep
    keep_files = [
        "economyoverride.json",
        "custom_buckets.json", 
        "gui_layout_devbuild.json",
        "version_info.json",
        "README_CURRENT_VERSION.md",
        "README_GUI.md",
        "PROJECT_STATUS_SUMMARY.md",
        "XCONOMYCHOOSER_V2_01_RELEASE_NOTES.md",
        "cli_parity_check.py",
        "wiring_fix_verification.py",
        "comprehensive_method_test.py",
        "AUGMENT XCONOMY.code-workspace",
        "final_versioning_cleanup.py"
    ]
    
    print("📋 CLEANUP PLAN:")
    print(f"🗑️  Remove {len(files_to_remove)} old unversioned files")
    print(f"✅ Keep {len(versioned_files)} properly versioned files")
    print(f"✅ Keep {len(keep_files)} configuration/documentation files")
    
    print(f"\n🗑️  FILES TO REMOVE:")
    for file in files_to_remove:
        if os.path.exists(file):
            print(f"  ❌ {file}")
    
    print(f"\n✅ VERSIONED FILES TO KEEP:")
    for file in versioned_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
    
    # Confirm removal
    response = input(f"\n⚠️  Proceed with removing old unversioned files? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ Cleanup cancelled")
        return
    
    # Remove old unversioned files
    removed_count = 0
    for file in files_to_remove:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"🗑️  Removed: {file}")
                removed_count += 1
            except Exception as e:
                print(f"❌ Failed to remove {file}: {e}")
    
    print(f"\n✅ FINAL CLEANUP COMPLETE!")
    print(f"🗑️  Removed {removed_count} old unversioned files")
    
    # Show final clean structure
    print(f"\n📁 FINAL CLEAN WORKSPACE:")
    remaining_files = [f for f in os.listdir(".") if os.path.isfile(f) and not f.startswith('.')]
    remaining_dirs = [d for d in os.listdir(".") if os.path.isdir(d) and not d.startswith('.')]
    
    print(f"\n📄 Core Application Files:")
    core_files = [f for f in remaining_files if f.startswith("XconomyChooser_") or f == "1_45c.py"]
    for file in sorted(core_files):
        print(f"  ✅ {file}")
    
    print(f"\n📄 Configuration Files:")
    config_files = [f for f in remaining_files if f.endswith('.json')]
    for file in sorted(config_files):
        print(f"  ✅ {file}")
    
    print(f"\n📄 Documentation Files:")
    doc_files = [f for f in remaining_files if f.endswith('.md')]
    for file in sorted(doc_files):
        print(f"  ✅ {file}")
    
    print(f"\n📄 Test Files:")
    test_files = [f for f in remaining_files if 'test' in f.lower() or 'check' in f.lower() or 'verification' in f.lower()]
    for file in sorted(test_files):
        print(f"  ✅ {file}")
    
    print(f"\n📂 System Directories:")
    for directory in sorted(remaining_dirs):
        print(f"  ✅ {directory}/")
    
    print(f"\n🎯 WORKSPACE IS NOW PROPERLY VERSIONED AND CLEAN!")
    print(f"📊 Total files: {len(remaining_files)}")
    print(f"📊 Total directories: {len(remaining_dirs)}")

def create_final_readme():
    """Create final README with proper versioning information"""
    readme_content = f"""# SCUM Economy Chooser Enhanced GUI v2.03 - Final Clean Version

## 🎯 Clean Workspace with Proper Versioning

**Version:** 2.03  
**Release Date:** {datetime.now().strftime("%Y-%m-%d")}  
**Status:** Production Ready - Properly Versioned & Clean  

## 📁 Core Application Files (Properly Versioned)

### Main Applications:
- `XconomyChooser_Enhanced_GUI_v2.03.py` - **Main enhanced GUI application**
- `XconomyChooser_CLI_v1.45c.py` - **Properly versioned CLI**
- `1_45c.py` - **Original CLI (preserved for compatibility)**

### Support Components:
- `XconomyChooser_Dialogs_v2.03.py` - Enhanced dialog components
- `XconomyChooser_VersionManager_v2.03.py` - Version management system
- `XconomyChooser_Cleanup_v2.03.py` - Cleanup and archiving tools
- `XconomyChooser_AggressiveCleanup_v2.03.py` - Advanced cleanup
- `XconomyChooser_RetroVersioning_v2.03.py` - Retroactive versioning

## 🚀 Quick Start

### Run Enhanced GUI (Recommended):
```bash
python XconomyChooser_Enhanced_GUI_v2.03.py
```

### Run Original CLI:
```bash
python 1_45c.py
```

### Run Versioned CLI:
```bash
python XconomyChooser_CLI_v1.45c.py
```

## ✅ Features Summary

### 🎨 Enhanced GUI Features:
- **Colored icons and buttons** throughout interface
- **Multiple custom bucket windows** for workflow management
- **Essential categories** with smart filtering
- **F.I.S.H. Logic** categorization system
- **Complete CLI parity** in Normal mode
- **Professional tools** in Advanced mode

### 🔧 System Features:
- **Automatic version management** with historic backups
- **Release packaging** with proper versioning
- **Cleanup and archiving** systems
- **Change tracking** and undo/redo
- **Professional validation** and safety features

## 📊 Version History

- **v1.45c** - Original CLI foundation
- **v1.71k** - Early GUI development  
- **v1.72** - Advanced features and batch operations
- **v2.00** - Enhanced GUI with F.I.S.H. Logic
- **v2.01** - CLI parity achievement
- **v2.02** - Wiring fixes and improvements
- **v2.03** - Colored interface, enhanced buckets, proper versioning

## 🎯 Workspace Status

### ✅ Properly Versioned:
- All core files have proper version numbers
- Historic backups updated with versioned names
- Release packages include versioned files
- Clean workspace with no duplicate files

### ✅ Production Ready:
- Complete feature set with CLI parity
- Professional appearance with colored interface
- Comprehensive backup and version management
- Clean, maintainable codebase

---
**SCUM Economy Chooser Enhanced GUI v2.03**  
*Final clean version with proper versioning*  
*Ready for production use and distribution*
"""
    
    with open("README_FINAL_CLEAN_VERSION.md", 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("📄 Created: README_FINAL_CLEAN_VERSION.md")

def main():
    final_cleanup()
    create_final_readme()

if __name__ == "__main__":
    main()
