#!/usr/bin/env python3
"""
Enhanced Dialogs for SCUM Economy Chooser
Dialogs with proper apply buttons and visual before/after changes
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, messagebox
import json
import copy
from collections import defaultdict

class EnhancedGlobalPriceDialog:
    def __init__(self, parent, data, status_callback, refresh_callback, add_change_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback
        self.add_change_callback = add_change_callback
        self.preview_data = None
        
        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("🌍 Global Price Changes")
        self.window.geometry("800x700")
        self.window.transient(parent)
        self.window.grab_set()
        
        self.create_interface()
    
    def create_interface(self):
        """Create the global price changes interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="🌍 Global Price Changes",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)
        
        # Description
        desc = ctk.CTkLabel(
            main_frame,
            text="Adjust prices globally across all merchants and outposts",
            font=ctk.CTkFont(size=12)
        )
        desc.pack(pady=5)
        
        # Settings frame
        settings_frame = ctk.CTkFrame(main_frame)
        settings_frame.pack(fill="x", padx=10, pady=10)
        
        # Price field selection
        field_frame = ctk.CTkFrame(settings_frame)
        field_frame.pack(fill="x", padx=10, pady=5)
        
        field_label = ctk.CTkLabel(field_frame, text="Select Price Field:")
        field_label.pack(pady=5)
        
        self.price_field_var = ctk.StringVar(value="base-purchase-price")
        field_radio1 = ctk.CTkRadioButton(
            field_frame,
            text="💰 Base Purchase Price",
            variable=self.price_field_var,
            value="base-purchase-price"
        )
        field_radio1.pack(pady=2)
        
        field_radio2 = ctk.CTkRadioButton(
            field_frame,
            text="💵 Base Sell Price",
            variable=self.price_field_var,
            value="base-sell-price"
        )
        field_radio2.pack(pady=2)
        
        # Percentage input
        percent_frame = ctk.CTkFrame(settings_frame)
        percent_frame.pack(fill="x", padx=10, pady=5)
        
        percent_label = ctk.CTkLabel(percent_frame, text="Percentage Change (-99 to +100):")
        percent_label.pack(pady=5)
        
        self.percent_entry = ctk.CTkEntry(
            percent_frame,
            placeholder_text="Enter percentage (e.g., -10, +25)"
        )
        self.percent_entry.pack(pady=5, padx=20, fill="x")
        
        # Preview section
        preview_frame = ctk.CTkFrame(main_frame)
        preview_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        preview_label = ctk.CTkLabel(preview_frame, text="📊 Before/After Preview:")
        preview_label.pack(pady=5)
        
        # Tabview for before/after
        self.preview_tabview = ctk.CTkTabview(preview_frame)
        self.preview_tabview.pack(fill="both", expand=True, padx=10, pady=5)
        
        self.preview_tabview.add("📋 Summary")
        self.preview_tabview.add("👁️ Before")
        self.preview_tabview.add("✨ After")
        
        # Summary tab
        self.summary_text = ctk.CTkTextbox(self.preview_tabview.tab("📋 Summary"), height=150)
        self.summary_text.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Before tab
        self.before_text = ctk.CTkTextbox(self.preview_tabview.tab("👁️ Before"), height=150)
        self.before_text.pack(fill="both", expand=True, padx=5, pady=5)
        
        # After tab
        self.after_text = ctk.CTkTextbox(self.preview_tabview.tab("✨ After"), height=150)
        self.after_text.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)
        
        preview_btn = ctk.CTkButton(
            button_frame,
            text="👁️ Generate Preview",
            command=self.generate_preview
        )
        preview_btn.pack(side="left", padx=5)
        
        self.add_change_btn = ctk.CTkButton(
            button_frame,
            text="📋 Add to Changes",
            command=self.add_to_changes,
            state="disabled"
        )
        self.add_change_btn.pack(side="left", padx=5)
        
        apply_btn = ctk.CTkButton(
            button_frame,
            text="✅ Apply Immediately",
            command=self.apply_immediately
        )
        apply_btn.pack(side="left", padx=5)
        
        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Cancel",
            command=self.window.destroy
        )
        cancel_btn.pack(side="right", padx=5)
    
    def generate_preview(self):
        """Generate preview of changes"""
        try:
            percentage_str = self.percent_entry.get().strip()
            if not percentage_str:
                messagebox.showwarning("Warning", "Please enter a percentage")
                return
                
            # Handle + prefix
            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]
                
            percentage = float(percentage_str)
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return
            
            field = self.price_field_var.get()
            
            # Create preview data
            self.preview_data = copy.deepcopy(self.data)
            
            # Track changes
            changes = []
            total_items = 0
            changed_items = 0
            
            # Apply changes to preview data
            for trader_name, trader_items in self.preview_data.get("economy-override", {}).get("traders", {}).items():
                for item in trader_items:
                    if isinstance(item, dict) and field in item and item[field] not in ["null", "-1", ""]:
                        try:
                            current_price = float(item[field])
                            new_price = max(1, round(current_price * (1 + percentage / 100)))
                            
                            # Track change
                            if len(changes) < 10:  # Limit preview samples
                                code = item.get('tradeable-code', 'Unknown')
                                changes.append({
                                    'trader': trader_name,
                                    'item': code,
                                    'before': current_price,
                                    'after': new_price
                                })
                            
                            item[field] = str(new_price)
                            changed_items += 1
                        except (ValueError, TypeError):
                            pass
                        total_items += 1
            
            # Update summary
            summary_text = f"Global Price Change Summary\n\n"
            summary_text += f"Field: {field}\n"
            summary_text += f"Percentage: {percentage:+.1f}%\n"
            summary_text += f"Total items affected: {changed_items}\n"
            summary_text += f"Total items processed: {total_items}\n\n"
            summary_text += "Sample changes:\n"
            
            for change in changes:
                summary_text += f"• {change['trader']}: {change['item']} - ${change['before']} → ${change['after']}\n"
            
            if changed_items > len(changes):
                summary_text += f"... and {changed_items - len(changes)} more items\n"
            
            self.summary_text.delete("1.0", "end")
            self.summary_text.insert("1.0", summary_text)
            
            # Update before/after tabs
            self.update_before_after_tabs(changes)
            
            # Enable add to changes button
            self.add_change_btn.configure(state="normal")
            
            self.status_callback(f"Preview generated: {changed_items} items would be changed")
            
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")
    
    def update_before_after_tabs(self, changes):
        """Update before and after tabs with sample data"""
        # Before tab
        before_text = "Before Changes (Sample):\n\n"
        for change in changes:
            before_text += f"{change['trader']}: {change['item']} - ${change['before']}\n"
        
        self.before_text.delete("1.0", "end")
        self.before_text.insert("1.0", before_text)
        
        # After tab
        after_text = "After Changes (Sample):\n\n"
        for change in changes:
            after_text += f"{change['trader']}: {change['item']} - ${change['after']}\n"
        
        self.after_text.delete("1.0", "end")
        self.after_text.insert("1.0", after_text)
    
    def add_to_changes(self):
        """Add this change to the pending changes list"""
        if not self.preview_data:
            messagebox.showwarning("Warning", "Please generate a preview first")
            return
        
        percentage_str = self.percent_entry.get().strip()
        if percentage_str.startswith('+'):
            percentage_str = percentage_str[1:]
        percentage = float(percentage_str)
        field = self.price_field_var.get()
        
        description = f"Global {field} change: {percentage:+.1f}%"
        
        def apply_function():
            # Apply the preview data to the actual data
            self.data.clear()
            self.data.update(self.preview_data)
        
        self.add_change_callback(
            "global_price_change",
            description,
            f"Field: {field}, Percentage: {percentage:+.1f}%",
            "Changes applied to all applicable items",
            apply_function
        )
        
        self.status_callback(f"Added global price change to pending changes")
        messagebox.showinfo("Added", "Global price change added to pending changes")
        self.window.destroy()
    
    def apply_immediately(self):
        """Apply changes immediately without staging"""
        if not self.preview_data:
            messagebox.showwarning("Warning", "Please generate a preview first")
            return
        
        percentage_str = self.percent_entry.get().strip()
        if percentage_str.startswith('+'):
            percentage_str = percentage_str[1:]
        percentage = float(percentage_str)
        field = self.price_field_var.get()
        
        if messagebox.askyesno("Confirm", f"Apply {percentage:+.1f}% change to {field} immediately?"):
            # Apply the preview data to the actual data
            self.data.clear()
            self.data.update(self.preview_data)
            
            self.refresh_callback()
            self.status_callback(f"Applied global {field} change: {percentage:+.1f}%")
            messagebox.showinfo("Success", "Global price changes applied successfully")
            self.window.destroy()


# Complete implementations for 100% CLI parity
class EnhancedEconomyFieldsDialog:
    def __init__(self, parent, data, status_callback, refresh_callback, add_change_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback
        self.add_change_callback = add_change_callback
        self.changes = {}

        # Economy fields with defaults (from CLI)
        self.economy_fields = {
            "economy-reset-time-hours": "-1.0",
            "prices-randomization-time-hours": "-1.0",
            "tradeable-rotation-time-ingame-hours-min": "48.0",
            "tradeable-rotation-time-ingame-hours-max": "96.0",
            "tradeable-rotation-time-of-day-min": "8.0",
            "tradeable-rotation-time-of-day-max": "16.0",
            "fully-restock-tradeable-hours": "2.0",
            "trader-funds-change-rate-per-hour-multiplier": "1.0",
            "prices-subject-to-player-count": "1",
            "gold-price-subject-to-global-multiplier": "1",
            "economy-logging": "1",
            "traders-unlimited-funds": "0",
            "traders-unlimited-stock": "0",
            "only-after-player-sale-tradeable-availability-enabled": "1",
            "tradeable-rotation-enabled": "1",
            "enable-fame-point-requirement": "1"
        }

        self.create_dialog(parent)

    def create_dialog(self, parent):
        self.window = ctk.CTkToplevel(parent)
        self.window.title("⚙️ Economy Fields Editor")
        self.window.geometry("900x700")
        self.window.transient(parent)
        self.window.grab_set()

        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(main_frame, text="⚙️ Economy Fields Editor",
                           font=ctk.CTkFont(size=20, weight="bold"))
        title.pack(pady=10)

        # Description
        desc = ctk.CTkLabel(main_frame, text="Edit economy configuration settings with apply buttons",
                          font=ctk.CTkFont(size=12))
        desc.pack(pady=5)

        # Scrollable frame for fields
        scroll_frame = ctk.CTkScrollableFrame(main_frame, height=400)
        scroll_frame.pack(fill="both", expand=True, padx=10, pady=10)

        self.field_widgets = {}

        # Create field editors
        for i, (field_name, default_value) in enumerate(self.economy_fields.items()):
            field_frame = ctk.CTkFrame(scroll_frame)
            field_frame.pack(fill="x", padx=5, pady=2)

            # Field label
            label = ctk.CTkLabel(field_frame, text=field_name, width=300, anchor="w")
            label.pack(side="left", padx=5, pady=5)

            # Current value from data
            current_value = self.data.get("economy-override", {}).get(field_name, default_value)

            # Entry widget
            entry = ctk.CTkEntry(field_frame, width=150)
            entry.insert(0, str(current_value))
            entry.pack(side="left", padx=5, pady=5)

            # Reset button
            reset_btn = ctk.CTkButton(field_frame, text="Reset", width=60,
                                    command=lambda f=field_name, e=entry, d=default_value: self.reset_field(f, e, d))
            reset_btn.pack(side="right", padx=5, pady=5)

            self.field_widgets[field_name] = entry

        # Buttons frame
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        preview_btn = ctk.CTkButton(button_frame, text="👁️ Preview Changes", command=self.preview_changes)
        preview_btn.pack(side="left", padx=5)

        self.add_changes_btn = ctk.CTkButton(button_frame, text="📋 Add to Changes",
                                           command=self.add_to_changes, state="disabled")
        self.add_changes_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(button_frame, text="✅ Apply Immediately", command=self.apply_immediately)
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(button_frame, text="❌ Cancel", command=self.window.destroy)
        cancel_btn.pack(side="right", padx=5)

    def reset_field(self, field_name, entry, default_value):
        entry.delete(0, "end")
        entry.insert(0, default_value)

    def preview_changes(self):
        self.changes = {}
        for field_name, entry in self.field_widgets.items():
            new_value = entry.get().strip()
            current_value = self.data.get("economy-override", {}).get(field_name, self.economy_fields[field_name])
            if new_value != current_value:
                self.changes[field_name] = {"old": current_value, "new": new_value}

        if not self.changes:
            messagebox.showinfo("No Changes", "No changes detected")
            return

        # Show preview
        preview_text = "Economy Fields Changes Preview:\n\n"
        for field, change in self.changes.items():
            preview_text += f"• {field}:\n  Before: {change['old']}\n  After: {change['new']}\n\n"

        messagebox.showinfo("Preview Changes", preview_text)
        self.add_changes_btn.configure(state="normal")

    def add_to_changes(self):
        if not self.changes:
            messagebox.showwarning("No Changes", "Please preview changes first")
            return

        description = f"Economy fields: {len(self.changes)} fields modified"

        def apply_function():
            for field_name, change in self.changes.items():
                if "economy-override" not in self.data:
                    self.data["economy-override"] = {}
                self.data["economy-override"][field_name] = change["new"]

        self.add_change_callback("economy_fields", description,
                               f"{len(self.changes)} fields changed",
                               "Applied to economy-override section", apply_function)

        self.status_callback(f"Added economy fields changes to pending changes")
        messagebox.showinfo("Added", "Economy fields changes added to pending changes")
        self.window.destroy()

    def apply_immediately(self):
        if not self.changes:
            self.preview_changes()
            if not self.changes:
                return

        if messagebox.askyesno("Confirm", f"Apply {len(self.changes)} economy field changes immediately?"):
            for field_name, change in self.changes.items():
                if "economy-override" not in self.data:
                    self.data["economy-override"] = {}
                self.data["economy-override"][field_name] = change["new"]

            self.refresh_callback()
            self.status_callback(f"Applied {len(self.changes)} economy field changes")
            messagebox.showinfo("Success", f"Applied {len(self.changes)} economy field changes")
            self.window.destroy()

class EnhancedMerchantLevelDialog:
    def __init__(self, parent, data, status_callback, refresh_callback, add_change_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback
        self.add_change_callback = add_change_callback
        self.create_dialog(parent)

    def create_dialog(self, parent):
        self.window = ctk.CTkToplevel(parent)
        self.window.title("👤 Merchant Level Editor")
        self.window.geometry("800x600")
        self.window.transient(parent)
        self.window.grab_set()

        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(main_frame, text="👤 Edit Individual Merchants",
                           font=ctk.CTkFont(size=20, weight="bold"))
        title.pack(pady=10)

        # Get available merchants
        traders = self.data.get("economy-override", {}).get("traders", {})
        merchant_list = list(traders.keys())

        if not merchant_list:
            ctk.CTkLabel(main_frame, text="No merchants found in data").pack(pady=20)
            ctk.CTkButton(main_frame, text="Close", command=self.window.destroy).pack(pady=10)
            return

        # Merchant selection
        select_frame = ctk.CTkFrame(main_frame)
        select_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(select_frame, text="Select Merchant:").pack(pady=5)
        self.merchant_var = ctk.StringVar(value=merchant_list[0])
        merchant_menu = ctk.CTkOptionMenu(select_frame, variable=self.merchant_var,
                                        values=merchant_list, command=self.on_merchant_select)
        merchant_menu.pack(pady=5)

        # Price field selection
        field_frame = ctk.CTkFrame(main_frame)
        field_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(field_frame, text="Price Field:").pack(pady=5)
        self.field_var = ctk.StringVar(value="base-purchase-price")
        ctk.CTkRadioButton(field_frame, text="Purchase Price", variable=self.field_var,
                         value="base-purchase-price").pack(pady=2)
        ctk.CTkRadioButton(field_frame, text="Sell Price", variable=self.field_var,
                         value="base-sell-price").pack(pady=2)

        # Percentage input
        percent_frame = ctk.CTkFrame(main_frame)
        percent_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(percent_frame, text="Percentage Change:").pack(pady=5)
        self.percent_entry = ctk.CTkEntry(percent_frame, placeholder_text="e.g., -10, +25")
        self.percent_entry.pack(pady=5)

        # Merchant info display
        self.info_text = ctk.CTkTextbox(main_frame, height=150)
        self.info_text.pack(fill="both", expand=True, padx=10, pady=10)

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        preview_btn = ctk.CTkButton(button_frame, text="👁️ Preview", command=self.preview_changes)
        preview_btn.pack(side="left", padx=5)

        self.add_changes_btn = ctk.CTkButton(button_frame, text="📋 Add to Changes",
                                           command=self.add_to_changes, state="disabled")
        self.add_changes_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(button_frame, text="✅ Apply", command=self.apply_immediately)
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(button_frame, text="❌ Cancel", command=self.window.destroy)
        cancel_btn.pack(side="right", padx=5)

        # Load initial merchant info
        self.on_merchant_select(merchant_list[0])

    def on_merchant_select(self, merchant_name):
        traders = self.data.get("economy-override", {}).get("traders", {})
        merchant_items = traders.get(merchant_name, [])

        info_text = f"Merchant: {merchant_name}\n"
        info_text += f"Total Items: {len(merchant_items)}\n\n"
        info_text += "Sample Items:\n"

        for i, item in enumerate(merchant_items[:10]):
            if isinstance(item, dict):
                code = item.get("tradeable-code", "Unknown")
                price = item.get("base-purchase-price", "N/A")
                info_text += f"  • {code} - ${price}\n"

        if len(merchant_items) > 10:
            info_text += f"  ... and {len(merchant_items) - 10} more items"

        self.info_text.delete("1.0", "end")
        self.info_text.insert("1.0", info_text)

    def preview_changes(self):
        try:
            percentage_str = self.percent_entry.get().strip()
            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]
            percentage = float(percentage_str)

            merchant_name = self.merchant_var.get()
            field = self.field_var.get()

            traders = self.data.get("economy-override", {}).get("traders", {})
            merchant_items = traders.get(merchant_name, [])

            changes = []
            for item in merchant_items:
                if isinstance(item, dict) and field in item and item[field] not in ["null", "-1", ""]:
                    try:
                        current_price = float(item[field])
                        new_price = max(1, round(current_price * (1 + percentage / 100)))
                        changes.append({
                            'code': item.get('tradeable-code', 'Unknown'),
                            'old_price': current_price,
                            'new_price': new_price
                        })
                    except (ValueError, TypeError):
                        continue

            preview_text = f"Merchant Level Changes Preview:\n\n"
            preview_text += f"Merchant: {merchant_name}\n"
            preview_text += f"Field: {field}\n"
            preview_text += f"Percentage: {percentage:+.1f}%\n"
            preview_text += f"Items affected: {len(changes)}\n\n"
            preview_text += "Sample changes:\n"

            for change in changes[:10]:
                preview_text += f"• {change['code']}: ${change['old_price']} → ${change['new_price']}\n"

            if len(changes) > 10:
                preview_text += f"... and {len(changes) - 10} more items"

            messagebox.showinfo("Preview Changes", preview_text)
            self.add_changes_btn.configure(state="normal")

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage")

    def add_to_changes(self):
        merchant_name = self.merchant_var.get()
        percentage_str = self.percent_entry.get().strip()
        field = self.field_var.get()

        description = f"Merchant {merchant_name}: {field} {percentage_str}%"

        def apply_function():
            self.apply_merchant_changes()

        self.add_change_callback("merchant_level", description,
                               f"Merchant: {merchant_name}",
                               f"Field: {field}, Change: {percentage_str}%", apply_function)

        self.status_callback(f"Added merchant level changes to pending changes")
        messagebox.showinfo("Added", "Merchant level changes added to pending changes")
        self.window.destroy()

    def apply_immediately(self):
        if messagebox.askyesno("Confirm", "Apply merchant level changes immediately?"):
            self.apply_merchant_changes()
            self.refresh_callback()
            self.status_callback("Applied merchant level changes")
            messagebox.showinfo("Success", "Merchant level changes applied")
            self.window.destroy()

    def apply_merchant_changes(self):
        try:
            percentage_str = self.percent_entry.get().strip()
            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]
            percentage = float(percentage_str)

            merchant_name = self.merchant_var.get()
            field = self.field_var.get()

            traders = self.data.get("economy-override", {}).get("traders", {})
            merchant_items = traders.get(merchant_name, [])

            for item in merchant_items:
                if isinstance(item, dict) and field in item and item[field] not in ["null", "-1", ""]:
                    try:
                        current_price = float(item[field])
                        new_price = max(1, round(current_price * (1 + percentage / 100)))
                        item[field] = str(new_price)
                    except (ValueError, TypeError):
                        continue

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage")

class EnhancedOutpostLevelDialog:
    def __init__(self, parent, data, status_callback, refresh_callback, add_change_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback
        self.add_change_callback = add_change_callback
        self.create_dialog(parent)

    def create_dialog(self, parent):
        self.window = ctk.CTkToplevel(parent)
        self.window.title("🏢 Outpost Level Editor")
        self.window.geometry("700x500")
        self.window.transient(parent)
        self.window.grab_set()

        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(main_frame, text="🏢 Edit Entire Outposts",
                           font=ctk.CTkFont(size=20, weight="bold"))
        title.pack(pady=10)

        # Get outposts from traders
        traders = self.data.get("economy-override", {}).get("traders", {})
        outposts = {}
        for trader_name in traders.keys():
            outpost = trader_name.split("_")[0] if "_" in trader_name else "Unknown"
            if outpost not in outposts:
                outposts[outpost] = []
            outposts[outpost].append(trader_name)

        if not outposts:
            ctk.CTkLabel(main_frame, text="No outposts found in data").pack(pady=20)
            ctk.CTkButton(main_frame, text="Close", command=self.window.destroy).pack(pady=10)
            return

        # Outpost selection
        select_frame = ctk.CTkFrame(main_frame)
        select_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(select_frame, text="Select Outpost:").pack(pady=5)
        outpost_list = list(outposts.keys())
        self.outpost_var = ctk.StringVar(value=outpost_list[0])
        outpost_menu = ctk.CTkOptionMenu(select_frame, variable=self.outpost_var,
                                       values=outpost_list, command=self.on_outpost_select)
        outpost_menu.pack(pady=5)

        # Price field selection
        field_frame = ctk.CTkFrame(main_frame)
        field_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(field_frame, text="Price Field:").pack(pady=5)
        self.field_var = ctk.StringVar(value="base-purchase-price")
        ctk.CTkRadioButton(field_frame, text="Purchase Price", variable=self.field_var,
                         value="base-purchase-price").pack(pady=2)
        ctk.CTkRadioButton(field_frame, text="Sell Price", variable=self.field_var,
                         value="base-sell-price").pack(pady=2)

        # Percentage input
        percent_frame = ctk.CTkFrame(main_frame)
        percent_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(percent_frame, text="Percentage Change:").pack(pady=5)
        self.percent_entry = ctk.CTkEntry(percent_frame, placeholder_text="e.g., -10, +25")
        self.percent_entry.pack(pady=5)

        # Outpost info display
        self.info_text = ctk.CTkTextbox(main_frame, height=150)
        self.info_text.pack(fill="both", expand=True, padx=10, pady=10)

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        preview_btn = ctk.CTkButton(button_frame, text="👁️ Preview", command=self.preview_changes)
        preview_btn.pack(side="left", padx=5)

        self.add_changes_btn = ctk.CTkButton(button_frame, text="📋 Add to Changes",
                                           command=self.add_to_changes, state="disabled")
        self.add_changes_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(button_frame, text="✅ Apply", command=self.apply_immediately)
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(button_frame, text="❌ Cancel", command=self.window.destroy)
        cancel_btn.pack(side="right", padx=5)

        # Store outposts data
        self.outposts = outposts

        # Load initial outpost info
        self.on_outpost_select(outpost_list[0])

    def on_outpost_select(self, outpost_name):
        traders = self.data.get("economy-override", {}).get("traders", {})
        outpost_traders = self.outposts.get(outpost_name, [])

        total_items = 0
        for trader_name in outpost_traders:
            trader_items = traders.get(trader_name, [])
            total_items += len(trader_items)

        info_text = f"Outpost: {outpost_name}\n"
        info_text += f"Traders: {len(outpost_traders)}\n"
        info_text += f"Total Items: {total_items}\n\n"
        info_text += "Traders in this outpost:\n"

        for trader_name in outpost_traders:
            trader_items = traders.get(trader_name, [])
            info_text += f"  • {trader_name} ({len(trader_items)} items)\n"

        self.info_text.delete("1.0", "end")
        self.info_text.insert("1.0", info_text)

    def preview_changes(self):
        try:
            percentage_str = self.percent_entry.get().strip()
            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]
            percentage = float(percentage_str)

            outpost_name = self.outpost_var.get()
            field = self.field_var.get()

            traders = self.data.get("economy-override", {}).get("traders", {})
            outpost_traders = self.outposts.get(outpost_name, [])

            total_changes = 0
            for trader_name in outpost_traders:
                trader_items = traders.get(trader_name, [])
                for item in trader_items:
                    if isinstance(item, dict) and field in item and item[field] not in ["null", "-1", ""]:
                        try:
                            float(item[field])
                            total_changes += 1
                        except (ValueError, TypeError):
                            continue

            preview_text = f"Outpost Level Changes Preview:\n\n"
            preview_text += f"Outpost: {outpost_name}\n"
            preview_text += f"Field: {field}\n"
            preview_text += f"Percentage: {percentage:+.1f}%\n"
            preview_text += f"Traders affected: {len(outpost_traders)}\n"
            preview_text += f"Items affected: {total_changes}\n\n"
            preview_text += "This will apply the percentage change to ALL items\n"
            preview_text += f"in ALL traders within the {outpost_name} outpost."

            messagebox.showinfo("Preview Changes", preview_text)
            self.add_changes_btn.configure(state="normal")

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage")

    def add_to_changes(self):
        outpost_name = self.outpost_var.get()
        percentage_str = self.percent_entry.get().strip()
        field = self.field_var.get()

        description = f"Outpost {outpost_name}: {field} {percentage_str}%"

        def apply_function():
            self.apply_outpost_changes()

        self.add_change_callback("outpost_level", description,
                               f"Outpost: {outpost_name}",
                               f"Field: {field}, Change: {percentage_str}%", apply_function)

        self.status_callback(f"Added outpost level changes to pending changes")
        messagebox.showinfo("Added", "Outpost level changes added to pending changes")
        self.window.destroy()

    def apply_immediately(self):
        if messagebox.askyesno("Confirm", "Apply outpost level changes immediately?"):
            self.apply_outpost_changes()
            self.refresh_callback()
            self.status_callback("Applied outpost level changes")
            messagebox.showinfo("Success", "Outpost level changes applied")
            self.window.destroy()

    def apply_outpost_changes(self):
        try:
            percentage_str = self.percent_entry.get().strip()
            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]
            percentage = float(percentage_str)

            outpost_name = self.outpost_var.get()
            field = self.field_var.get()

            traders = self.data.get("economy-override", {}).get("traders", {})
            outpost_traders = self.outposts.get(outpost_name, [])

            for trader_name in outpost_traders:
                trader_items = traders.get(trader_name, [])
                for item in trader_items:
                    if isinstance(item, dict) and field in item and item[field] not in ["null", "-1", ""]:
                        try:
                            current_price = float(item[field])
                            new_price = max(1, round(current_price * (1 + percentage / 100)))
                            item[field] = str(new_price)
                        except (ValueError, TypeError):
                            continue

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage")

class EnhancedFineTuneDialog:
    def __init__(self, parent, data, status_callback, refresh_callback, add_change_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback
        self.add_change_callback = add_change_callback
        self.create_dialog(parent)

    def create_dialog(self, parent):
        self.window = ctk.CTkToplevel(parent)
        self.window.title("🔧 Fine Tune Items")
        self.window.geometry("900x700")
        self.window.transient(parent)
        self.window.grab_set()

        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(main_frame, text="🔧 Fine Tune Individual Items",
                           font=ctk.CTkFont(size=20, weight="bold"))
        title.pack(pady=10)

        # Search frame
        search_frame = ctk.CTkFrame(main_frame)
        search_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(search_frame, text="Search for Item:").pack(pady=5)
        self.search_entry = ctk.CTkEntry(search_frame, placeholder_text="Enter item code...")
        self.search_entry.pack(side="left", fill="x", expand=True, padx=5)

        search_btn = ctk.CTkButton(search_frame, text="🔍 Search", command=self.search_items)
        search_btn.pack(side="right", padx=5)

        # Results frame
        results_frame = ctk.CTkFrame(main_frame)
        results_frame.pack(fill="both", expand=True, padx=10, pady=10)

        ctk.CTkLabel(results_frame, text="Search Results:").pack(pady=5)

        # Results listbox
        self.results_listbox = tk.Listbox(results_frame, height=10)
        self.results_listbox.pack(fill="both", expand=True, padx=5, pady=5)
        self.results_listbox.bind("<<ListboxSelect>>", self.on_item_select)

        # Item editor frame
        editor_frame = ctk.CTkFrame(main_frame)
        editor_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(editor_frame, text="Item Editor:").pack(pady=5)

        # Field editors will be created dynamically
        self.editor_widgets = {}
        self.editor_container = ctk.CTkScrollableFrame(editor_frame, height=200)
        self.editor_container.pack(fill="both", expand=True, padx=5, pady=5)

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        self.update_btn = ctk.CTkButton(button_frame, text="💾 Update Item",
                                      command=self.update_item, state="disabled")
        self.update_btn.pack(side="left", padx=5)

        self.add_changes_btn = ctk.CTkButton(button_frame, text="📋 Add to Changes",
                                           command=self.add_to_changes, state="disabled")
        self.add_changes_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(button_frame, text="❌ Cancel", command=self.window.destroy)
        cancel_btn.pack(side="right", padx=5)

        self.current_item = None
        self.current_trader = None
        self.current_index = None

    def search_items(self):
        search_term = self.search_entry.get().strip().lower()
        if not search_term:
            messagebox.showwarning("Warning", "Please enter a search term")
            return

        self.results_listbox.delete(0, tk.END)

        traders = self.data.get("economy-override", {}).get("traders", {})
        matches = []

        for trader_name, trader_items in traders.items():
            for idx, item in enumerate(trader_items):
                if isinstance(item, dict):
                    code = item.get("tradeable-code", "").lower()
                    if search_term in code:
                        matches.append((trader_name, idx, item))

        if not matches:
            self.results_listbox.insert(tk.END, "No items found")
            return

        for trader_name, idx, item in matches:
            code = item.get("tradeable-code", "Unknown")
            price = item.get("base-purchase-price", "N/A")
            display_text = f"{trader_name}: {code} (${price})"
            self.results_listbox.insert(tk.END, display_text)

        self.search_matches = matches

    def on_item_select(self, event):
        selection = self.results_listbox.curselection()
        if not selection or not hasattr(self, 'search_matches'):
            return

        idx = selection[0]
        if idx >= len(self.search_matches):
            return

        trader_name, item_idx, item = self.search_matches[idx]
        self.current_trader = trader_name
        self.current_index = item_idx
        self.current_item = item

        self.create_item_editor(item)
        self.update_btn.configure(state="normal")

    def create_item_editor(self, item):
        # Clear existing widgets
        for widget in self.editor_container.winfo_children():
            widget.destroy()

        self.editor_widgets = {}

        # Create editors for each field
        for field_name, field_value in item.items():
            field_frame = ctk.CTkFrame(self.editor_container)
            field_frame.pack(fill="x", padx=5, pady=2)

            # Field label
            label = ctk.CTkLabel(field_frame, text=field_name, width=200, anchor="w")
            label.pack(side="left", padx=5, pady=5)

            # Field editor
            if field_name == "can-be-purchased":
                # Boolean field
                var = ctk.StringVar(value=str(field_value))
                combo = ctk.CTkComboBox(field_frame, variable=var,
                                      values=["true", "false", "default"], width=150)
                combo.pack(side="left", padx=5, pady=5)
                self.editor_widgets[field_name] = var
            else:
                # Text field
                entry = ctk.CTkEntry(field_frame, width=200)
                entry.insert(0, str(field_value))
                entry.pack(side="left", padx=5, pady=5)
                self.editor_widgets[field_name] = entry

    def update_item(self):
        if not self.current_item:
            return

        # Update item with new values
        for field_name, widget in self.editor_widgets.items():
            if isinstance(widget, ctk.StringVar):
                new_value = widget.get()
            else:
                new_value = widget.get()

            self.current_item[field_name] = new_value

        self.add_changes_btn.configure(state="normal")
        messagebox.showinfo("Updated", "Item updated in memory. Use 'Add to Changes' to stage the change.")

    def add_to_changes(self):
        if not self.current_item:
            return

        code = self.current_item.get("tradeable-code", "Unknown")
        description = f"Fine tune: {code} in {self.current_trader}"

        def apply_function():
            # Item is already updated in memory
            pass

        self.add_change_callback("fine_tune", description,
                               f"Item: {code}",
                               f"Trader: {self.current_trader}", apply_function)

        self.status_callback(f"Added fine tune changes to pending changes")
        messagebox.showinfo("Added", "Fine tune changes added to pending changes")
        self.window.destroy()

class EnhancedSpreadEditDialog:
    def __init__(self, parent, data, status_callback, refresh_callback, add_change_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback
        self.add_change_callback = add_change_callback
        self.create_dialog(parent)

    def create_dialog(self, parent):
        self.window = ctk.CTkToplevel(parent)
        self.window.title("📊 Spread Edit Items")
        self.window.geometry("800x600")
        self.window.transient(parent)
        self.window.grab_set()

        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(main_frame, text="📊 Spread Edit Across Traders",
                           font=ctk.CTkFont(size=20, weight="bold"))
        title.pack(pady=10)

        # Filter frame
        filter_frame = ctk.CTkFrame(main_frame)
        filter_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(filter_frame, text="Item Filter:").pack(pady=5)
        self.filter_entry = ctk.CTkEntry(filter_frame, placeholder_text="Enter item code filter...")
        self.filter_entry.pack(fill="x", padx=5, pady=5)

        # Category selection
        category_frame = ctk.CTkFrame(main_frame)
        category_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(category_frame, text="Category Filter (CLI Compatible):").pack(pady=5)
        self.category_var = ctk.StringVar(value="All")
        categories = ["All", "Cal_", "Magazine", "Weapon_", "Crafted", "_AP_CR"]
        category_menu = ctk.CTkOptionMenu(category_frame, variable=self.category_var, values=categories)
        category_menu.pack(pady=5)

        # Price field selection
        field_frame = ctk.CTkFrame(main_frame)
        field_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(field_frame, text="Price Field:").pack(pady=5)
        self.field_var = ctk.StringVar(value="base-purchase-price")
        ctk.CTkRadioButton(field_frame, text="Purchase Price", variable=self.field_var,
                         value="base-purchase-price").pack(pady=2)
        ctk.CTkRadioButton(field_frame, text="Sell Price", variable=self.field_var,
                         value="base-sell-price").pack(pady=2)

        # Operation type
        operation_frame = ctk.CTkFrame(main_frame)
        operation_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(operation_frame, text="Operation:").pack(pady=5)
        self.operation_var = ctk.StringVar(value="percentage")
        ctk.CTkRadioButton(operation_frame, text="Percentage Change", variable=self.operation_var,
                         value="percentage").pack(pady=2)
        ctk.CTkRadioButton(operation_frame, text="Set Fixed Value", variable=self.operation_var,
                         value="fixed").pack(pady=2)

        # Value input
        value_frame = ctk.CTkFrame(main_frame)
        value_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(value_frame, text="Value:").pack(pady=5)
        self.value_entry = ctk.CTkEntry(value_frame, placeholder_text="Enter percentage or fixed value...")
        self.value_entry.pack(fill="x", padx=5, pady=5)

        # Preview area
        self.preview_text = ctk.CTkTextbox(main_frame, height=150)
        self.preview_text.pack(fill="both", expand=True, padx=10, pady=10)

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        filter_btn = ctk.CTkButton(button_frame, text="🔍 Filter Items", command=self.filter_items)
        filter_btn.pack(side="left", padx=5)

        preview_btn = ctk.CTkButton(button_frame, text="👁️ Preview", command=self.preview_changes)
        preview_btn.pack(side="left", padx=5)

        self.add_changes_btn = ctk.CTkButton(button_frame, text="📋 Add to Changes",
                                           command=self.add_to_changes, state="disabled")
        self.add_changes_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(button_frame, text="✅ Apply", command=self.apply_immediately)
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(button_frame, text="❌ Cancel", command=self.window.destroy)
        cancel_btn.pack(side="right", padx=5)

        self.filtered_items = []

    def filter_items(self):
        filter_text = self.filter_entry.get().strip().lower()
        category = self.category_var.get()

        traders = self.data.get("economy-override", {}).get("traders", {})
        self.filtered_items = []

        for trader_name, trader_items in traders.items():
            for idx, item in enumerate(trader_items):
                if isinstance(item, dict):
                    code = item.get("tradeable-code", "").lower()

                    # Apply filters
                    if filter_text and filter_text not in code:
                        continue

                    if category != "All":
                        if category == "Cal_" and not code.startswith("cal_"):
                            continue
                        elif category == "Magazine" and "magazine" not in code:
                            continue
                        elif category == "Weapon_" and not (code.startswith("weapon_") and "parts" not in code):
                            continue
                        elif category == "Crafted" and not code.startswith("crafted"):
                            continue
                        elif category == "_AP_CR" and not code.endswith("_ap_cr"):
                            continue

                    self.filtered_items.append((trader_name, idx, item))

        # Display filtered results
        preview_text = f"Filtered Items ({len(self.filtered_items)} found):\n\n"
        for i, (trader_name, idx, item) in enumerate(self.filtered_items[:20]):
            code = item.get("tradeable-code", "Unknown")
            price = item.get(self.field_var.get(), "N/A")
            preview_text += f"{i+1}. {trader_name}: {code} (${price})\n"

        if len(self.filtered_items) > 20:
            preview_text += f"\n... and {len(self.filtered_items) - 20} more items"

        self.preview_text.delete("1.0", "end")
        self.preview_text.insert("1.0", preview_text)

    def preview_changes(self):
        if not self.filtered_items:
            messagebox.showwarning("Warning", "Please filter items first")
            return

        try:
            value_str = self.value_entry.get().strip()
            if value_str.startswith('+'):
                value_str = value_str[1:]
            value = float(value_str)

            operation = self.operation_var.get()
            field = self.field_var.get()

            changes = []
            for trader_name, idx, item in self.filtered_items:
                if field in item and item[field] not in ["null", "-1", ""]:
                    try:
                        current_price = float(item[field])
                        if operation == "percentage":
                            new_price = max(1, round(current_price * (1 + value / 100)))
                        else:  # fixed
                            new_price = max(1, int(value))

                        changes.append({
                            'trader': trader_name,
                            'code': item.get('tradeable-code', 'Unknown'),
                            'old_price': current_price,
                            'new_price': new_price
                        })
                    except (ValueError, TypeError):
                        continue

            preview_text = f"Spread Edit Changes Preview:\n\n"
            preview_text += f"Operation: {operation}\n"
            preview_text += f"Value: {value}\n"
            preview_text += f"Field: {field}\n"
            preview_text += f"Items affected: {len(changes)}\n\n"
            preview_text += "Sample changes:\n"

            for change in changes[:15]:
                preview_text += f"• {change['trader']}: {change['code']} - ${change['old_price']} → ${change['new_price']}\n"

            if len(changes) > 15:
                preview_text += f"\n... and {len(changes) - 15} more items"

            self.preview_text.delete("1.0", "end")
            self.preview_text.insert("1.0", preview_text)
            self.add_changes_btn.configure(state="normal")

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number")

    def add_to_changes(self):
        value_str = self.value_entry.get().strip()
        operation = self.operation_var.get()
        field = self.field_var.get()

        description = f"Spread edit: {operation} {value_str} on {field}"

        def apply_function():
            self.apply_spread_changes()

        self.add_change_callback("spread_edit", description,
                               f"Items: {len(self.filtered_items)}",
                               f"Operation: {operation}, Value: {value_str}", apply_function)

        self.status_callback(f"Added spread edit changes to pending changes")
        messagebox.showinfo("Added", "Spread edit changes added to pending changes")
        self.window.destroy()

    def apply_immediately(self):
        if messagebox.askyesno("Confirm", "Apply spread edit changes immediately?"):
            self.apply_spread_changes()
            self.refresh_callback()
            self.status_callback("Applied spread edit changes")
            messagebox.showinfo("Success", "Spread edit changes applied")
            self.window.destroy()

    def apply_spread_changes(self):
        try:
            value_str = self.value_entry.get().strip()
            if value_str.startswith('+'):
                value_str = value_str[1:]
            value = float(value_str)

            operation = self.operation_var.get()
            field = self.field_var.get()

            for trader_name, idx, item in self.filtered_items:
                if field in item and item[field] not in ["null", "-1", ""]:
                    try:
                        current_price = float(item[field])
                        if operation == "percentage":
                            new_price = max(1, round(current_price * (1 + value / 100)))
                        else:  # fixed
                            new_price = max(1, int(value))

                        item[field] = str(new_price)
                    except (ValueError, TypeError):
                        continue

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number")

class EnhancedPurchaseSettingsDialog:
    def __init__(self, parent, data, status_callback, refresh_callback, add_change_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback
        self.add_change_callback = add_change_callback
        self.create_dialog(parent)

    def create_dialog(self, parent):
        self.window = ctk.CTkToplevel(parent)
        self.window.title("🛒 Purchase Settings")
        self.window.geometry("700x500")
        self.window.transient(parent)
        self.window.grab_set()

        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(main_frame, text="🛒 Manage Purchase Settings",
                           font=ctk.CTkFont(size=20, weight="bold"))
        title.pack(pady=10)

        # Description
        desc = ctk.CTkLabel(main_frame, text="Edit 'can-be-purchased' field across traders (CLI compatible)",
                          font=ctk.CTkFont(size=12))
        desc.pack(pady=5)

        # Trader selection
        trader_frame = ctk.CTkFrame(main_frame)
        trader_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(trader_frame, text="Select Trader Type:").pack(pady=5)

        # CLI-compatible trader types
        trader_types = ["Armory", "BoatShop", "Hospital", "Mechanic", "Saloon", "Trader", "Barber"]
        self.trader_var = ctk.StringVar(value="All")
        trader_values = ["All"] + trader_types
        trader_menu = ctk.CTkOptionMenu(trader_frame, variable=self.trader_var, values=trader_values)
        trader_menu.pack(pady=5)

        # Purchase setting
        setting_frame = ctk.CTkFrame(main_frame)
        setting_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(setting_frame, text="Purchase Setting:").pack(pady=5)
        self.setting_var = ctk.StringVar(value="true")
        ctk.CTkRadioButton(setting_frame, text="Enable Purchase (true)", variable=self.setting_var,
                         value="true").pack(pady=2)
        ctk.CTkRadioButton(setting_frame, text="Disable Purchase (false)", variable=self.setting_var,
                         value="false").pack(pady=2)
        ctk.CTkRadioButton(setting_frame, text="Default Setting", variable=self.setting_var,
                         value="default").pack(pady=2)

        # Item filter
        filter_frame = ctk.CTkFrame(main_frame)
        filter_frame.pack(fill="x", padx=10, pady=10)

        ctk.CTkLabel(filter_frame, text="Item Filter (optional):").pack(pady=5)
        self.filter_entry = ctk.CTkEntry(filter_frame, placeholder_text="Enter item code filter...")
        self.filter_entry.pack(fill="x", padx=5, pady=5)

        # Preview area
        self.preview_text = ctk.CTkTextbox(main_frame, height=150)
        self.preview_text.pack(fill="both", expand=True, padx=10, pady=10)

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        preview_btn = ctk.CTkButton(button_frame, text="👁️ Preview", command=self.preview_changes)
        preview_btn.pack(side="left", padx=5)

        self.add_changes_btn = ctk.CTkButton(button_frame, text="📋 Add to Changes",
                                           command=self.add_to_changes, state="disabled")
        self.add_changes_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(button_frame, text="✅ Apply", command=self.apply_immediately)
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(button_frame, text="❌ Cancel", command=self.window.destroy)
        cancel_btn.pack(side="right", padx=5)

        self.affected_items = []

    def preview_changes(self):
        trader_type = self.trader_var.get()
        setting = self.setting_var.get()
        filter_text = self.filter_entry.get().strip().lower()

        traders = self.data.get("economy-override", {}).get("traders", {})
        self.affected_items = []

        for trader_name, trader_items in traders.items():
            # Check if trader matches type
            if trader_type != "All":
                if trader_type not in trader_name:
                    continue

            for idx, item in enumerate(trader_items):
                if isinstance(item, dict):
                    code = item.get("tradeable-code", "").lower()

                    # Apply filter
                    if filter_text and filter_text not in code:
                        continue

                    self.affected_items.append((trader_name, idx, item))

        preview_text = f"Purchase Settings Changes Preview:\n\n"
        preview_text += f"Trader Type: {trader_type}\n"
        preview_text += f"Setting: can-be-purchased = {setting}\n"
        preview_text += f"Items affected: {len(self.affected_items)}\n\n"

        if filter_text:
            preview_text += f"Filter: {filter_text}\n\n"

        preview_text += "Sample affected items:\n"
        for i, (trader_name, idx, item) in enumerate(self.affected_items[:15]):
            code = item.get("tradeable-code", "Unknown")
            current_setting = item.get("can-be-purchased", "default")
            preview_text += f"• {trader_name}: {code} ({current_setting} → {setting})\n"

        if len(self.affected_items) > 15:
            preview_text += f"\n... and {len(self.affected_items) - 15} more items"

        self.preview_text.delete("1.0", "end")
        self.preview_text.insert("1.0", preview_text)
        self.add_changes_btn.configure(state="normal")

    def add_to_changes(self):
        trader_type = self.trader_var.get()
        setting = self.setting_var.get()

        description = f"Purchase settings: {trader_type} traders set to {setting}"

        def apply_function():
            self.apply_purchase_changes()

        self.add_change_callback("purchase_settings", description,
                               f"Trader: {trader_type}",
                               f"Setting: {setting}, Items: {len(self.affected_items)}", apply_function)

        self.status_callback(f"Added purchase settings changes to pending changes")
        messagebox.showinfo("Added", "Purchase settings changes added to pending changes")
        self.window.destroy()

    def apply_immediately(self):
        if messagebox.askyesno("Confirm", "Apply purchase settings changes immediately?"):
            self.apply_purchase_changes()
            self.refresh_callback()
            self.status_callback("Applied purchase settings changes")
            messagebox.showinfo("Success", "Purchase settings changes applied")
            self.window.destroy()

    def apply_purchase_changes(self):
        setting = self.setting_var.get()

        for trader_name, idx, item in self.affected_items:
            item["can-be-purchased"] = setting

class SmartCategoriesDialog:
    def __init__(self, parent, data, status_callback, refresh_callback, add_change_callback, fish_engine):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback
        self.add_change_callback = add_change_callback
        self.fish_engine = fish_engine

        # Run F.I.S.H. analysis and show results
        analysis = self.fish_engine.analyze_economy(self.data)

        # Create results window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("📂 Smart Categories (F.I.S.H. Logic)")
        self.window.geometry("800x600")
        self.window.transient(parent)

        # Display analysis results
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        title = ctk.CTkLabel(main_frame, text="📂 F.I.S.H. Smart Categories Analysis",
                           font=ctk.CTkFont(size=18, weight="bold"))
        title.pack(pady=10)

        # Results display
        results_text = ctk.CTkTextbox(main_frame)
        results_text.pack(fill="both", expand=True, padx=10, pady=10)

        # Build results text
        report = f"🐟 F.I.S.H. Smart Categories Analysis\n\n"
        report += f"Total Items: {analysis['total_items']}\n"
        report += f"Categories Found: {len(analysis['categories'])}\n\n"

        for category, items in sorted(analysis['categories'].items(), key=lambda x: len(x[1]), reverse=True):
            report += f"📂 {category} ({len(items)} items):\n"
            for item_data in items[:5]:
                code = item_data['code']
                trader = item_data['trader']
                report += f"  • {code} ({trader})\n"
            if len(items) > 5:
                report += f"  ... and {len(items) - 5} more items\n"
            report += "\n"

        results_text.insert("1.0", report)
        results_text.configure(state="disabled")

        # Close button
        close_btn = ctk.CTkButton(main_frame, text="Close", command=self.window.destroy)
        close_btn.pack(pady=10)

class ScenarioRulesDialog:
    def __init__(self, parent, fish_engine, status_callback):
        self.fish_engine = fish_engine
        self.status_callback = status_callback

        # Create rules editor window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("📋 F.I.S.H. Scenario Rules")
        self.window.geometry("700x500")
        self.window.transient(parent)

        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        title = ctk.CTkLabel(main_frame, text="📋 F.I.S.H. Scenario Rules Editor",
                           font=ctk.CTkFont(size=18, weight="bold"))
        title.pack(pady=10)

        # Rules display
        rules_text = ctk.CTkTextbox(main_frame)
        rules_text.pack(fill="both", expand=True, padx=10, pady=10)

        # Display current rules
        rules_info = "🐟 F.I.S.H. Logic Rules Configuration\n\n"
        rules_info += "Current Active Rules:\n\n"

        for category, rule in self.fish_engine.rules.items():
            rules_info += f"📂 {category}:\n"
            rules_info += f"  Priority: {rule['priority']}\n"
            rules_info += f"  Tags: {rule.get('tags', [])}\n"
            rules_info += f"  Audience: {rule.get('audience', 'general')}\n"
            rules_info += f"  Description: {rule['description']}\n\n"

        rules_info += "\nHierarchy Rules:\n"
        for rule_type, rules in self.fish_engine.hierarchy_rules.items():
            rules_info += f"\n{rule_type}:\n"
            for rule in rules:
                rules_info += f"  • {rule}\n"

        rules_text.insert("1.0", rules_info)
        rules_text.configure(state="disabled")

        # Close button
        close_btn = ctk.CTkButton(main_frame, text="Close", command=self.window.destroy)
        close_btn.pack(pady=10)
