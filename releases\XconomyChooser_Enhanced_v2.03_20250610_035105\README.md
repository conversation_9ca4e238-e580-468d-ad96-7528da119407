# SCUM Economy Chooser Enhanced GUI v2.03

## 🎮 What's New in Version 2.03

### ✅ Complete CLI Parity
- All CLI functions available in Normal mode
- Enhanced user-friendly interface
- Professional tools in Advanced mode

### 🐟 F.I.S.H. Logic System
- Flexible Intelligence for Scenario Handling
- Smart item categorization
- Advanced filtering and analysis

### 🪣 Custom Buckets
- Create custom item groups
- Drag-and-drop functionality
- Save and manage bucket configurations

### 🔧 Enhanced Features
- Visual change tracking
- Undo/Redo system
- Database integration ready
- Improved error handling

## 📁 Package Contents

### Core Files
- `XconomyChooser_Enhanced_GUI.py` - Main enhanced GUI application
- `XconomyChooser_CLI.py` - Original CLI version (1_45c.py)
- `enhanced_dialogs.py` - Enhanced dialog components

### Configuration
- `config/economyoverride.json` - Sample economy file
- `config/custom_buckets.json` - Custom bucket configurations
- `config/gui_layout_devbuild.json` - GUI layout settings

### Documentation
- `documentation/` - Complete documentation and guides

## 🚀 Quick Start

1. **Run Enhanced GUI**: `python XconomyChooser_Enhanced_GUI.py`
2. **Run CLI Version**: `python XconomyChooser_CLI.py`
3. **Load Economy File**: Use File > Load to open your economyoverride.json
4. **Start Editing**: Choose tools from the left panel

## 🎯 Mode Selection

### Normal Mode (Recommended)
- All CLI functions available
- Essential categories
- Custom buckets
- User-friendly interface

### Advanced Mode (Expert Users)
- F.I.S.H. Logic tools
- Professional analytics
- Advanced bucket rules
- Full feature access

## 🛡️ Safety Features

- Automatic backups before changes
- CLI-compatible validation
- Undo/Redo system
- Change preview and tracking

## 📋 System Requirements

- Python 3.8+
- CustomTkinter library
- Windows/Linux/Mac compatible

## 🔧 Installation

```bash
pip install customtkinter
python XconomyChooser_Enhanced_GUI.py
```

## 📞 Support

For issues, feature requests, or questions, refer to the documentation
or check the project repository.

---
**SCUM Economy Chooser Enhanced GUI v2.03**  
*Complete CLI parity with enhanced user experience*
