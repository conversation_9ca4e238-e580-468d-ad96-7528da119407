#!/usr/bin/env python3
"""
SCUM Economy Chooser - Advanced GUI v1.45c
Complete GUI implementation with CLI integration
Author: V1nceTD (Enhanced by Augment Agent)
"""

import customtkinter as ctk
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os
import sys
import subprocess
import threading
import time
from datetime import datetime
import logging
import math
import glob
from collections import defaultdict, Counter

# Set appearance
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

class SCUMEconomyGUI:
    def __init__(self):
        # Create main window
        self.root = ctk.CTk()
        self.root.title("🎮 SCUM Economy Chooser - Advanced GUI v1.45c")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 700)
        
        # Initialize variables
        self.current_data = None
        self.current_filename = None
        self.current_file_path = None
        self.categories = []
        self.smart_categories = {}
        
        # Create GUI components
        self.create_main_interface()
        
        # Auto-scan for JSON files on startup
        self.auto_scan_json_files()
    
    def create_main_interface(self):
        """Create the main GUI interface"""
        # Create main container with grid layout
        self.main_container = ctk.CTkFrame(self.root)
        self.main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Configure grid weights
        self.main_container.grid_rowconfigure(2, weight=1)
        self.main_container.grid_columnconfigure(1, weight=1)
        
        # Create sections
        self.create_header()
        self.create_file_section()
        self.create_main_content()
        self.create_status_bar()
    
    def create_header(self):
        """Create the application header"""
        header_frame = ctk.CTkFrame(self.main_container)
        header_frame.grid(row=0, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        
        # Title
        title_label = ctk.CTkLabel(
            header_frame,
            text="🎮 SCUM Economy Chooser - Advanced GUI",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=10)
        
        # Version and author info
        info_frame = ctk.CTkFrame(header_frame)
        info_frame.pack(pady=5)
        
        version_label = ctk.CTkLabel(
            info_frame,
            text="Build v1.45c - Advanced GUI Edition by V1nceTD",
            font=ctk.CTkFont(size=12)
        )
        version_label.pack(side="left", padx=10)
        
        # Quick action buttons
        button_frame = ctk.CTkFrame(info_frame)
        button_frame.pack(side="right", padx=10)
        
        cli_button = ctk.CTkButton(
            button_frame,
            text="🖥️ CLI",
            command=self.launch_cli_terminal,
            width=80,
            height=30
        )
        cli_button.pack(side="left", padx=2)
        
        help_button = ctk.CTkButton(
            button_frame,
            text="❓ Help",
            command=self.show_help,
            width=80,
            height=30
        )
        help_button.pack(side="left", padx=2)
    
    def create_file_section(self):
        """Create file management section"""
        file_frame = ctk.CTkFrame(self.main_container)
        file_frame.grid(row=1, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        
        # File info and controls
        file_info_frame = ctk.CTkFrame(file_frame)
        file_info_frame.pack(fill="x", padx=10, pady=10)
        
        # Current file display
        self.current_file_label = ctk.CTkLabel(
            file_info_frame,
            text="📁 No file loaded",
            font=ctk.CTkFont(size=14),
            text_color="orange"
        )
        self.current_file_label.pack(side="left", padx=10)
        
        # File action buttons
        file_buttons_frame = ctk.CTkFrame(file_info_frame)
        file_buttons_frame.pack(side="right", padx=10)
        
        load_btn = ctk.CTkButton(
            file_buttons_frame,
            text="📂 Load JSON",
            command=self.load_json_file,
            width=120
        )
        load_btn.pack(side="left", padx=3)
        
        scan_btn = ctk.CTkButton(
            file_buttons_frame,
            text="🔍 Scan Dir",
            command=self.scan_directory,
            width=100
        )
        scan_btn.pack(side="left", padx=3)
        
        reload_btn = ctk.CTkButton(
            file_buttons_frame,
            text="🔄 Reload",
            command=self.reload_file,
            width=80
        )
        reload_btn.pack(side="left", padx=3)
    
    def create_main_content(self):
        """Create the main content area with tools and preview"""
        # Left panel - Tools
        self.left_panel = ctk.CTkFrame(self.main_container)
        self.left_panel.grid(row=2, column=0, sticky="nsew", padx=(5, 2), pady=5)
        
        # Right panel - Preview and details
        self.right_panel = ctk.CTkFrame(self.main_container)
        self.right_panel.grid(row=2, column=1, sticky="nsew", padx=(2, 5), pady=5)
        
        # Configure column weights
        self.main_container.grid_columnconfigure(0, weight=1)
        self.main_container.grid_columnconfigure(1, weight=2)
        
        self.create_tools_panel()
        self.create_preview_panel()
    
    def create_tools_panel(self):
        """Create the tools panel with all editing options"""
        # Tools title
        tools_title = ctk.CTkLabel(
            self.left_panel,
            text="⚙️ Economy Editor Tools",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        tools_title.pack(pady=10)
        
        # Scrollable frame for tools
        tools_scroll = ctk.CTkScrollableFrame(self.left_panel)
        tools_scroll.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Tool categories
        self.create_global_tools(tools_scroll)
        self.create_merchant_tools(tools_scroll)
        self.create_advanced_tools(tools_scroll)
    
    def create_global_tools(self, parent):
        """Create global editing tools"""
        global_frame = ctk.CTkFrame(parent)
        global_frame.pack(fill="x", pady=5)
        
        global_title = ctk.CTkLabel(
            global_frame,
            text="🌍 Global Operations",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        global_title.pack(pady=5)
        
        # Global price changes
        global_price_btn = ctk.CTkButton(
            global_frame,
            text="💰 Global Price Changes",
            command=self.open_global_price_changes,
            height=40
        )
        global_price_btn.pack(fill="x", padx=10, pady=2)
        
        # Economy fields
        economy_fields_btn = ctk.CTkButton(
            global_frame,
            text="⚙️ Economy Fields",
            command=self.open_economy_fields,
            height=40
        )
        economy_fields_btn.pack(fill="x", padx=10, pady=2)
        
        # Category management
        category_btn = ctk.CTkButton(
            global_frame,
            text="📂 Category Management",
            command=self.open_category_management,
            height=40
        )
        category_btn.pack(fill="x", padx=10, pady=2)
    
    def create_merchant_tools(self, parent):
        """Create merchant-specific tools"""
        merchant_frame = ctk.CTkFrame(parent)
        merchant_frame.pack(fill="x", pady=5)
        
        merchant_title = ctk.CTkLabel(
            merchant_frame,
            text="👤 Merchant Operations",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        merchant_title.pack(pady=5)
        
        # Individual merchant editing
        merchant_btn = ctk.CTkButton(
            merchant_frame,
            text="👤 Edit Merchant Level",
            command=self.open_merchant_level,
            height=40
        )
        merchant_btn.pack(fill="x", padx=10, pady=2)
        
        # Outpost level editing
        outpost_btn = ctk.CTkButton(
            merchant_frame,
            text="🏢 Edit Outpost Level",
            command=self.open_outpost_level,
            height=40
        )
        outpost_btn.pack(fill="x", padx=10, pady=2)
        
        # Fine tuning
        fine_tune_btn = ctk.CTkButton(
            merchant_frame,
            text="🔧 Fine Tune Items",
            command=self.open_fine_tune,
            height=40
        )
        fine_tune_btn.pack(fill="x", padx=10, pady=2)
    
    def create_advanced_tools(self, parent):
        """Create advanced tools"""
        advanced_frame = ctk.CTkFrame(parent)
        advanced_frame.pack(fill="x", pady=5)
        
        advanced_title = ctk.CTkLabel(
            advanced_frame,
            text="🔬 Advanced Tools",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        advanced_title.pack(pady=5)
        
        # Spread editing
        spread_btn = ctk.CTkButton(
            advanced_frame,
            text="📊 Spread Edit Items",
            command=self.open_spread_edit,
            height=40
        )
        spread_btn.pack(fill="x", padx=10, pady=2)
        
        # Purchase settings
        purchase_btn = ctk.CTkButton(
            advanced_frame,
            text="🛒 Purchase Settings",
            command=self.open_purchase_settings,
            height=40
        )
        purchase_btn.pack(fill="x", padx=10, pady=2)
    
    def create_preview_panel(self):
        """Create the preview and details panel"""
        # Preview title
        preview_title = ctk.CTkLabel(
            self.right_panel,
            text="📊 Data Preview & Analysis",
            font=ctk.CTkFont(size=18, weight="bold")
        )
        preview_title.pack(pady=10)
        
        # Tabview for different views
        self.tabview = ctk.CTkTabview(self.right_panel)
        self.tabview.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Add tabs
        self.tabview.add("📈 Overview")
        self.tabview.add("🌳 Data Tree")
        self.tabview.add("📋 JSON Preview")
        self.tabview.add("📊 Statistics")
        
        self.create_overview_tab()
        self.create_tree_tab()
        self.create_json_tab()
        self.create_stats_tab()
    
    def create_overview_tab(self):
        """Create overview tab"""
        overview_frame = self.tabview.tab("📈 Overview")
        
        self.overview_text = ctk.CTkTextbox(overview_frame)
        self.overview_text.pack(fill="both", expand=True, padx=10, pady=10)
        
        # Initial content
        self.overview_text.insert("1.0", """
🎮 SCUM Economy Chooser - Advanced GUI

Welcome to the advanced economy editor for SCUM servers!

📁 Getting Started:
1. Load your economyoverride.json file
2. Choose an editing tool from the left panel
3. Make your changes and save

🌍 Global Operations:
- Adjust all prices by percentage
- Modify economy configuration
- Manage item categories

👤 Merchant Operations:
- Edit individual merchants
- Modify entire outposts
- Fine-tune specific items

🔬 Advanced Tools:
- Spread changes across traders
- Manage purchase settings
- Detailed analysis tools

💡 Tips:
- Always backup your files before editing
- Use the CLI version for complex operations
- Preview changes before applying
- Check the statistics tab for data insights
        """)
        self.overview_text.configure(state="disabled")
    
    def create_tree_tab(self):
        """Create data tree tab"""
        tree_frame = self.tabview.tab("🌳 Data Tree")
        
        # Search frame
        search_frame = ctk.CTkFrame(tree_frame)
        search_frame.pack(fill="x", padx=10, pady=5)
        
        search_label = ctk.CTkLabel(search_frame, text="🔍 Search:")
        search_label.pack(side="left", padx=5)
        
        self.search_entry = ctk.CTkEntry(search_frame, placeholder_text="Enter item code...")
        self.search_entry.pack(side="left", fill="x", expand=True, padx=5)
        
        search_btn = ctk.CTkButton(search_frame, text="Go", command=self.search_items, width=60)
        search_btn.pack(side="right", padx=5)
        
        # Tree view
        tree_container = ctk.CTkFrame(tree_frame)
        tree_container.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Create treeview with tkinter (CustomTkinter doesn't have treeview yet)
        self.tree = ttk.Treeview(tree_container, height=20)
        self.tree.heading("#0", text="Outpost > Trader > Item")
        self.tree.pack(fill="both", expand=True, padx=5, pady=5)
        
        # Bind selection event
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)
    
    def create_json_tab(self):
        """Create JSON preview tab"""
        json_frame = self.tabview.tab("📋 JSON Preview")
        
        self.json_text = ctk.CTkTextbox(json_frame, font=ctk.CTkFont(family="Courier"))
        self.json_text.pack(fill="both", expand=True, padx=10, pady=10)
    
    def create_stats_tab(self):
        """Create statistics tab"""
        stats_frame = self.tabview.tab("📊 Statistics")
        
        self.stats_text = ctk.CTkTextbox(stats_frame)
        self.stats_text.pack(fill="both", expand=True, padx=10, pady=10)
    
    def create_status_bar(self):
        """Create status bar at bottom"""
        status_frame = ctk.CTkFrame(self.main_container)
        status_frame.grid(row=3, column=0, columnspan=2, sticky="ew", padx=5, pady=5)
        
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="Ready - Load a JSON file to begin editing",
            font=ctk.CTkFont(size=11)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        # Save buttons
        save_frame = ctk.CTkFrame(status_frame)
        save_frame.pack(side="right", padx=10, pady=5)
        
        save_button = ctk.CTkButton(
            save_frame,
            text="💾 Save & Exit",
            command=self.save_and_exit,
            width=120
        )
        save_button.pack(side="left", padx=5)
        
        exit_button = ctk.CTkButton(
            save_frame,
            text="❌ Exit",
            command=self.exit_application,
            width=80
        )
        exit_button.pack(side="left", padx=5)

    # File Management Methods
    def auto_scan_json_files(self):
        """Auto-scan for JSON files in current directory"""
        try:
            json_files = [f for f in os.listdir('.') if f.endswith('.json')]
            if json_files:
                self.status_label.configure(text=f"Found {len(json_files)} JSON file(s) in directory")
            else:
                self.status_label.configure(text="No JSON files found in current directory")
        except Exception as e:
            self.status_label.configure(text="Error scanning directory")

    def scan_directory(self):
        """Scan directory for JSON files and show selection dialog"""
        try:
            json_files = [f for f in os.listdir('.') if f.endswith('.json')]
            if not json_files:
                messagebox.showinfo("No Files", "No JSON files found in current directory")
                return

            # Create selection dialog
            self.show_file_selection_dialog(json_files)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to scan directory: {str(e)}")

    def show_file_selection_dialog(self, json_files):
        """Show dialog to select from available JSON files"""
        dialog = ctk.CTkToplevel(self.root)
        dialog.title("Select JSON File")
        dialog.geometry("600x500")
        dialog.transient(self.root)
        dialog.grab_set()

        # Title
        title = ctk.CTkLabel(dialog, text="📁 Select Economy Override JSON File",
                           font=ctk.CTkFont(size=16, weight="bold"))
        title.pack(pady=10)

        # File list frame
        listbox_frame = ctk.CTkFrame(dialog)
        listbox_frame.pack(fill="both", expand=True, padx=20, pady=10)

        # Create listbox with scrollbar
        file_listbox = tk.Listbox(listbox_frame, height=20, font=("Arial", 10))
        scrollbar = tk.Scrollbar(listbox_frame, orient="vertical", command=file_listbox.yview)
        file_listbox.configure(yscrollcommand=scrollbar.set)

        file_listbox.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        scrollbar.pack(side="right", fill="y", pady=5)

        # Populate listbox
        for file in json_files:
            file_listbox.insert(tk.END, file)

        # Buttons
        button_frame = ctk.CTkFrame(dialog)
        button_frame.pack(fill="x", padx=20, pady=10)

        def load_selected():
            selection = file_listbox.curselection()
            if selection:
                filename = json_files[selection[0]]
                self.load_specific_file(filename)
                dialog.destroy()
            else:
                messagebox.showwarning("No Selection", "Please select a file")

        load_btn = ctk.CTkButton(button_frame, text="📂 Load Selected", command=load_selected)
        load_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(button_frame, text="❌ Cancel", command=dialog.destroy)
        cancel_btn.pack(side="right", padx=5)

    def load_specific_file(self, filename):
        """Load a specific JSON file"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                self.current_data = json.load(f)
            self.current_filename = filename
            self.current_file_path = os.path.abspath(filename)
            self.current_file_label.configure(text=f"✅ Loaded: {filename}", text_color="green")
            self.status_label.configure(text=f"Successfully loaded {filename}")

            # Update displays
            self.populate_tree()
            self.update_json_preview()
            self.update_statistics()
            self.build_categories()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load {filename}: {str(e)}")
            self.status_label.configure(text="Error loading file")

    def load_json_file(self):
        """Load a JSON file using file dialog"""
        file_path = filedialog.askopenfilename(
            title="Select Economy Override JSON File",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    self.current_data = json.load(f)
                self.current_filename = os.path.basename(file_path)
                self.current_file_path = file_path
                self.current_file_label.configure(text=f"✅ Loaded: {self.current_filename}", text_color="green")
                self.status_label.configure(text=f"Successfully loaded {self.current_filename}")

                # Update displays
                self.populate_tree()
                self.update_json_preview()
                self.update_statistics()
                self.build_categories()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to load JSON file: {str(e)}")
                self.status_label.configure(text="Error loading file")

    def reload_file(self):
        """Reload the current file"""
        if self.current_file_path and os.path.exists(self.current_file_path):
            try:
                with open(self.current_file_path, 'r', encoding='utf-8') as f:
                    self.current_data = json.load(f)
                self.status_label.configure(text=f"Reloaded {self.current_filename}")

                # Update displays
                self.populate_tree()
                self.update_json_preview()
                self.update_statistics()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to reload file: {str(e)}")
        elif self.current_filename and os.path.exists(self.current_filename):
            try:
                with open(self.current_filename, 'r', encoding='utf-8') as f:
                    self.current_data = json.load(f)
                self.status_label.configure(text=f"Reloaded {self.current_filename}")

                # Update displays
                self.populate_tree()
                self.update_json_preview()
                self.update_statistics()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to reload file: {str(e)}")
        else:
            messagebox.showwarning("Warning", "No file to reload. Please load a file first.")

    # Display Update Methods
    def populate_tree(self):
        """Populate the tree view with data"""
        if not self.current_data:
            return

        # Clear existing items
        for item in self.tree.get_children():
            self.tree.delete(item)

        try:
            traders = self.current_data.get("economy-override", {}).get("traders", {})

            # Group by outpost
            outposts = defaultdict(list)
            for trader_key in traders.keys():
                outpost = trader_key.split("_")[0] if "_" in trader_key else "Unknown"
                outposts[outpost].append(trader_key)

            # Build tree
            for outpost, trader_list in sorted(outposts.items()):
                outpost_id = self.tree.insert("", "end", text=f"🏢 {outpost}", open=True)

                for trader_key in sorted(trader_list):
                    trader_items = traders[trader_key]
                    trader_id = self.tree.insert(outpost_id, "end", text=f"👤 {trader_key}")

                    # Add items (limit to first 50 for performance)
                    for idx, item in enumerate(trader_items[:50]):
                        if isinstance(item, dict):
                            code = item.get("tradeable-code", f"Item_{idx}")
                            price = item.get("base-purchase-price", "N/A")
                            item_text = f"📦 {code} (${price})"
                            self.tree.insert(trader_id, "end", text=item_text,
                                           values=(trader_key, idx))

                    if len(trader_items) > 50:
                        self.tree.insert(trader_id, "end", text=f"... and {len(trader_items) - 50} more items")

        except Exception as e:
            self.status_label.configure(text=f"Error populating tree: {str(e)}")

    def update_json_preview(self):
        """Update the JSON preview tab"""
        if not self.current_data:
            self.json_text.delete("1.0", "end")
            self.json_text.insert("1.0", "No data loaded")
            return

        try:
            # Show a subset of the data for performance
            preview_data = {
                "economy-override": {
                    "traders": {k: v[:3] for k, v in list(self.current_data.get("economy-override", {}).get("traders", {}).items())[:5]}
                }
            }

            json_str = json.dumps(preview_data, indent=2)
            self.json_text.delete("1.0", "end")
            self.json_text.insert("1.0", f"📋 JSON Preview (First 5 traders, 3 items each):\n\n{json_str}")

        except Exception as e:
            self.json_text.delete("1.0", "end")
            self.json_text.insert("1.0", f"Error displaying JSON: {str(e)}")

    def update_statistics(self):
        """Update the statistics tab"""
        if not self.current_data:
            self.stats_text.delete("1.0", "end")
            self.stats_text.insert("1.0", "No data loaded")
            return

        try:
            traders = self.current_data.get("economy-override", {}).get("traders", {})

            # Calculate statistics
            total_traders = len(traders)
            total_items = sum(len(items) for items in traders.values())

            # Outpost breakdown
            outpost_counts = defaultdict(int)
            for trader_key in traders.keys():
                outpost = trader_key.split("_")[0] if "_" in trader_key else "Unknown"
                outpost_counts[outpost] += 1

            # Item categories
            category_counts = defaultdict(int)
            price_ranges = {"0-100": 0, "100-1000": 0, "1000+": 0, "No Price": 0}

            for trader_items in traders.values():
                for item in trader_items:
                    if isinstance(item, dict):
                        code = item.get("tradeable-code", "")
                        if code:
                            category = code.split("_")[0] if "_" in code else "Unknown"
                            category_counts[category] += 1

                        # Price analysis
                        price_str = item.get("base-purchase-price", "")
                        try:
                            if price_str and price_str not in ["-1", "null"]:
                                price = float(price_str)
                                if price < 100:
                                    price_ranges["0-100"] += 1
                                elif price < 1000:
                                    price_ranges["100-1000"] += 1
                                else:
                                    price_ranges["1000+"] += 1
                            else:
                                price_ranges["No Price"] += 1
                        except (ValueError, TypeError):
                            price_ranges["No Price"] += 1

            # Build statistics text
            stats_text = f"""📊 Economy Statistics

📈 Overview:
• Total Traders: {total_traders}
• Total Items: {total_items}
• Average Items per Trader: {total_items / total_traders if total_traders > 0 else 0:.1f}

🏢 Outpost Breakdown:
"""
            for outpost, count in sorted(outpost_counts.items()):
                stats_text += f"• {outpost}: {count} traders\n"

            stats_text += f"\n📦 Top Item Categories:\n"
            for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
                stats_text += f"• {category}: {count} items\n"

            stats_text += f"\n💰 Price Distribution:\n"
            for range_name, count in price_ranges.items():
                stats_text += f"• {range_name}: {count} items\n"

            self.stats_text.delete("1.0", "end")
            self.stats_text.insert("1.0", stats_text)

        except Exception as e:
            self.stats_text.delete("1.0", "end")
            self.stats_text.insert("1.0", f"Error calculating statistics: {str(e)}")

    def build_categories(self):
        """Build smart categories from loaded data"""
        if not self.current_data:
            return

        try:
            traders = self.current_data.get("economy-override", {}).get("traders", {})
            category_counts = defaultdict(int)

            # Extract categories from tradeable codes
            for trader_items in traders.values():
                for item in trader_items:
                    if isinstance(item, dict):
                        code = item.get("tradeable-code", "")
                        if code:
                            # Extract prefix as category
                            category = code.split("_")[0] if "_" in code else code
                            category_counts[category] += 1

            # Store categories sorted by count
            self.categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)

        except Exception as e:
            self.status_label.configure(text=f"Error building categories: {str(e)}")

    # Tree interaction methods
    def on_tree_select(self, event):
        """Handle tree selection"""
        selection = self.tree.selection()
        if not selection:
            return

        item = self.tree.item(selection[0])
        values = item.get('values', [])

        if len(values) >= 2:  # This is an item
            trader_key, item_idx = values[0], int(values[1])
            try:
                item_data = self.current_data["economy-override"]["traders"][trader_key][item_idx]

                # Update JSON preview with selected item
                json_str = json.dumps(item_data, indent=2)
                self.json_text.delete("1.0", "end")
                self.json_text.insert("1.0", f"📦 Selected Item:\n\n{json_str}")

            except (KeyError, IndexError) as e:
                self.status_label.configure(text=f"Error loading item: {str(e)}")

    def search_items(self):
        """Search for items in the data"""
        search_term = self.search_entry.get().strip().lower()
        if not search_term or not self.current_data:
            return

        matches = []
        traders = self.current_data.get("economy-override", {}).get("traders", {})

        for trader_key, trader_items in traders.items():
            for idx, item in enumerate(trader_items):
                if isinstance(item, dict):
                    code = item.get("tradeable-code", "").lower()
                    if search_term in code:
                        matches.append((trader_key, idx, item))

        if matches:
            # Show first match in JSON preview
            first_match = matches[0][2]
            json_str = json.dumps(first_match, indent=2)
            self.json_text.delete("1.0", "end")
            self.json_text.insert("1.0", f"🔍 Search Results ({len(matches)} found):\n\nFirst match:\n{json_str}")

            messagebox.showinfo("Search Results", f"Found {len(matches)} items matching '{search_term}'")
        else:
            messagebox.showinfo("Search Results", f"No items found matching '{search_term}'")

    # CLI Integration
    def launch_cli_terminal(self):
        """Launch the CLI version in a separate terminal"""
        try:
            if os.name == 'nt':  # Windows
                subprocess.Popen(['cmd', '/c', 'start', 'cmd', '/k', 'python', '1_45c.py'])
            else:  # Unix/Linux/Mac
                subprocess.Popen(['gnome-terminal', '--', 'python3', '1_45c.py'])
            self.status_label.configure(text="CLI version launched in separate terminal")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch CLI: {str(e)}")

    def show_help(self):
        """Show help dialog"""
        help_window = ctk.CTkToplevel(self.root)
        help_window.title("Help - SCUM Economy Chooser")
        help_window.geometry("700x600")
        help_window.transient(self.root)

        # Help content
        help_text = """
🎮 SCUM Economy Chooser - Advanced GUI Help

📖 GETTING STARTED:
1. Load your economyoverride.json file using 'Load JSON' or 'Scan Dir'
2. Browse your data in the Data Tree tab
3. Select an editing tool from the left panel
4. Make your changes and save when done

🌍 GLOBAL OPERATIONS:

💰 Global Price Changes
- Adjust all merchant prices by percentage
- Choose between purchase and sell prices
- Preview changes before applying
- Supports -99% to +100% range

⚙️ Economy Fields
- Modify core economy settings
- Reset times, rotation settings, etc.
- Trader funds and stock settings
- Fame point requirements

📂 Category Management
- Edit items by category (Weapon, Ammo, etc.)
- Batch operations on item groups
- Smart category detection

👤 MERCHANT OPERATIONS:

👤 Edit Merchant Level
- Target specific merchants
- Adjust individual trader prices
- Select by outpost and merchant

🏢 Edit Outpost Level
- Apply changes to entire outposts
- Bulk edit all merchants in an outpost
- Outpost-wide price adjustments

🔧 Fine Tune Items
- Item-level precision editing
- Search and modify specific items
- Individual field editing

🔬 ADVANCED TOOLS:

📊 Spread Edit Items
- Apply changes across multiple traders
- Bulk operations with filters
- Cross-trader synchronization

🛒 Purchase Settings
- Manage item availability
- Control what can be purchased
- Fame point requirements

📊 DATA ANALYSIS:

📈 Overview Tab
- Welcome information and tips
- Quick start guide

🌳 Data Tree Tab
- Browse your economy structure
- Search for specific items
- Navigate outposts and traders

📋 JSON Preview Tab
- View raw JSON data
- Selected item details
- Data structure inspection

📊 Statistics Tab
- Economy overview statistics
- Item distribution analysis
- Price range breakdowns

💡 TIPS:
- Always backup your files before editing
- Use the CLI version for advanced operations
- Preview changes when possible
- Check the statistics tab for data insights
- Search function helps find specific items quickly

⚠️ SAFETY:
- The application preserves your original file structure
- All changes are applied to a copy in memory
- Use 'Save & Exit' to write changes to a new file
- Original files are never overwritten

🔗 CLI INTEGRATION:
- Click 'CLI' button to launch the original command-line version
- Both versions work with the same file format
- Use CLI for complex batch operations

For more detailed help, consult the original CLI documentation.
        """

        text_widget = ctk.CTkTextbox(help_window, font=ctk.CTkFont(family="Arial", size=11))
        text_widget.pack(fill="both", expand=True, padx=20, pady=20)
        text_widget.insert("1.0", help_text)
        text_widget.configure(state="disabled")

        close_btn = ctk.CTkButton(help_window, text="Close", command=help_window.destroy)
        close_btn.pack(pady=10)

    # Menu Action Methods - These will open dialog windows
    def open_global_price_changes(self):
        """Open global price changes dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        GlobalPriceDialog(self.root, self.current_data, self.update_status, self.refresh_displays)

    def open_economy_fields(self):
        """Open economy fields editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        EconomyFieldsDialog(self.root, self.current_data, self.update_status, self.refresh_displays)

    def open_merchant_level(self):
        """Open merchant level editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        MerchantLevelDialog(self.root, self.current_data, self.update_status, self.refresh_displays)

    def open_outpost_level(self):
        """Open outpost level editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        OutpostLevelDialog(self.root, self.current_data, self.update_status, self.refresh_displays)

    def open_fine_tune(self):
        """Open fine tune editor"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        FineTuneDialog(self.root, self.current_data, self.update_status, self.refresh_displays)

    def open_spread_edit(self):
        """Open spread edit dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        SpreadEditDialog(self.root, self.current_data, self.update_status, self.refresh_displays)

    def open_purchase_settings(self):
        """Open purchase settings dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        PurchaseSettingsDialog(self.root, self.current_data, self.update_status, self.refresh_displays)

    def open_category_management(self):
        """Open category management dialog"""
        if not self.current_data:
            messagebox.showwarning("Warning", "Please load a JSON file first")
            return

        CategoryManagementDialog(self.root, self.current_data, self.update_status, self.refresh_displays, self.categories)

    # Utility methods
    def update_status(self, message):
        """Update status bar message"""
        self.status_label.configure(text=message)

    def refresh_displays(self):
        """Refresh all display elements"""
        self.populate_tree()
        self.update_json_preview()
        self.update_statistics()
        self.build_categories()

    # Save and Exit Methods
    def save_and_exit(self):
        """Save the current data and exit"""
        if not self.current_data:
            messagebox.showwarning("Warning", "No data to save")
            return

        # Generate timestamped filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_filename = f"{timestamp}_economy_config.json"

        try:
            with open(save_filename, 'w', encoding='utf-8') as f:
                json.dump(self.current_data, f, indent=2)

            messagebox.showinfo("Success", f"File saved as {save_filename}")
            self.root.quit()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save file: {str(e)}")

    def exit_application(self):
        """Exit the application"""
        if messagebox.askyesno("Confirm Exit", "Are you sure you want to exit without saving?"):
            self.root.quit()


# Dialog Classes
class GlobalPriceDialog:
    def __init__(self, parent, data, status_callback, refresh_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("🌍 Global Price Changes")
        self.window.geometry("700x600")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the global price changes interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="🌍 Global Price Changes",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Description
        desc = ctk.CTkLabel(
            main_frame,
            text="Adjust prices globally across all merchants and outposts",
            font=ctk.CTkFont(size=12)
        )
        desc.pack(pady=5)

        # Price field selection
        field_frame = ctk.CTkFrame(main_frame)
        field_frame.pack(fill="x", padx=10, pady=10)

        field_label = ctk.CTkLabel(field_frame, text="Select Price Field:")
        field_label.pack(pady=5)

        self.price_field_var = ctk.StringVar(value="base-purchase-price")
        field_radio1 = ctk.CTkRadioButton(
            field_frame,
            text="💰 Base Purchase Price",
            variable=self.price_field_var,
            value="base-purchase-price"
        )
        field_radio1.pack(pady=2)

        field_radio2 = ctk.CTkRadioButton(
            field_frame,
            text="💵 Base Sell Price",
            variable=self.price_field_var,
            value="base-sell-price"
        )
        field_radio2.pack(pady=2)

        # Percentage input
        percent_frame = ctk.CTkFrame(main_frame)
        percent_frame.pack(fill="x", padx=10, pady=10)

        percent_label = ctk.CTkLabel(percent_frame, text="Percentage Change (-99 to +100):")
        percent_label.pack(pady=5)

        self.percent_entry = ctk.CTkEntry(
            percent_frame,
            placeholder_text="Enter percentage (e.g., -10, +25)"
        )
        self.percent_entry.pack(pady=5, padx=20, fill="x")

        # Preview section
        preview_frame = ctk.CTkFrame(main_frame)
        preview_frame.pack(fill="both", expand=True, padx=10, pady=10)

        preview_label = ctk.CTkLabel(preview_frame, text="📊 Preview Changes:")
        preview_label.pack(pady=5)

        self.preview_text = ctk.CTkTextbox(preview_frame, height=200)
        self.preview_text.pack(fill="both", expand=True, padx=10, pady=5)

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        preview_btn = ctk.CTkButton(
            button_frame,
            text="👁️ Preview Changes",
            command=self.preview_changes
        )
        preview_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="✅ Apply Changes",
            command=self.apply_changes
        )
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Cancel",
            command=self.window.destroy
        )
        cancel_btn.pack(side="right", padx=5)

    def preview_changes(self):
        """Preview the changes without applying them"""
        try:
            percentage_str = self.percent_entry.get().strip()
            if not percentage_str:
                messagebox.showwarning("Warning", "Please enter a percentage")
                return

            # Handle + prefix
            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]

            percentage = float(percentage_str)
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return

            field = self.price_field_var.get()
            preview_text = f"Preview for {field} change of {percentage:+.1f}%:\n\n"

            count = 0
            total_items = 0
            sample_changes = []

            # Count total and collect sample changes
            for trader_name, trader_items in self.data.get("economy-override", {}).get("traders", {}).items():
                for item in trader_items:
                    if isinstance(item, dict) and field in item and item[field] not in ["null", "-1", ""]:
                        try:
                            current_price = float(item[field])
                            new_price = max(1, round(current_price * (1 + percentage / 100)))
                            total_items += 1

                            if count < 10:  # Collect first 10 for preview
                                code = item.get('tradeable-code', 'Unknown')
                                sample_changes.append(f"{trader_name}: {code} - ${current_price} → ${new_price}")
                                count += 1
                        except (ValueError, TypeError):
                            continue

            # Build preview text
            for change in sample_changes:
                preview_text += change + "\n"

            if total_items > 10:
                preview_text += f"\n... and {total_items - 10} more items\n"

            preview_text += f"\nTotal items to be changed: {total_items}"

            self.preview_text.delete("1.0", "end")
            self.preview_text.insert("1.0", preview_text)

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")

    def apply_changes(self):
        """Apply the global price changes"""
        try:
            percentage_str = self.percent_entry.get().strip()
            if not percentage_str:
                messagebox.showwarning("Warning", "Please enter a percentage")
                return

            # Handle + prefix
            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]

            percentage = float(percentage_str)
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return

            field = self.price_field_var.get()

            if messagebox.askyesno("Confirm", f"Apply {percentage:+.1f}% change to {field} for all items?"):
                count = 0
                for trader_items in self.data.get("economy-override", {}).get("traders", {}).values():
                    for item in trader_items:
                        if isinstance(item, dict) and field in item and item[field] not in ["null", "-1", ""]:
                            try:
                                current_price = float(item[field])
                                new_price = max(1, round(current_price * (1 + percentage / 100)))
                                item[field] = str(new_price)
                                count += 1
                            except (ValueError, TypeError):
                                continue

                self.status_callback(f"Applied {percentage:+.1f}% change to {count} items")
                self.refresh_callback()
                messagebox.showinfo("Success", f"Successfully updated {count} items")
                self.window.destroy()

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")


class EconomyFieldsDialog:
    def __init__(self, parent, data, status_callback, refresh_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("⚙️ Edit Economy Fields")
        self.window.geometry("800x700")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the economy fields interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="⚙️ Economy Fields Editor",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Description
        desc = ctk.CTkLabel(
            main_frame,
            text="Modify core economy configuration settings",
            font=ctk.CTkFont(size=12)
        )
        desc.pack(pady=5)

        # Scrollable frame for fields
        scroll_frame = ctk.CTkScrollableFrame(main_frame)
        scroll_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Economy fields with default values and descriptions
        self.economy_fields = {
            "economy-reset-time-hours": ("-1.0", "Economy reset interval in hours"),
            "prices-randomization-time-hours": ("-1.0", "Price randomization interval"),
            "tradeable-rotation-time-ingame-hours-min": ("48.0", "Min rotation time"),
            "tradeable-rotation-time-ingame-hours-max": ("96.0", "Max rotation time"),
            "tradeable-rotation-time-of-day-min": ("8.0", "Min rotation time of day"),
            "tradeable-rotation-time-of-day-max": ("16.0", "Max rotation time of day"),
            "fully-restock-tradeable-hours": ("2.0", "Full restock interval"),
            "trader-funds-change-rate-per-hour-multiplier": ("1.0", "Trader funds change rate"),
            "prices-subject-to-player-count": ("1", "Prices affected by player count"),
            "gold-price-subject-to-global-multiplier": ("1", "Gold price global multiplier"),
            "economy-logging": ("1", "Enable economy logging"),
            "traders-unlimited-funds": ("0", "Unlimited trader funds"),
            "traders-unlimited-stock": ("0", "Unlimited trader stock"),
            "only-after-player-sale-tradeable-availability-enabled": ("1", "Player sale requirement"),
            "tradeable-rotation-enabled": ("1", "Enable item rotation"),
            "enable-fame-point-requirement": ("1", "Enable fame point requirements")
        }

        self.field_entries = {}

        # Create entry fields for each economy field
        for field_name, (default_value, description) in self.economy_fields.items():
            field_frame = ctk.CTkFrame(scroll_frame)
            field_frame.pack(fill="x", padx=5, pady=3)

            # Get current value from data or use default
            current_value = self.data.get("economy-override", {}).get(field_name, default_value)

            # Field name label
            name_label = ctk.CTkLabel(
                field_frame,
                text=field_name.replace("-", " ").title(),
                width=300,
                anchor="w",
                font=ctk.CTkFont(weight="bold")
            )
            name_label.pack(side="top", anchor="w", padx=10, pady=(5, 0))

            # Description label
            desc_label = ctk.CTkLabel(
                field_frame,
                text=description,
                width=300,
                anchor="w",
                font=ctk.CTkFont(size=10),
                text_color="gray"
            )
            desc_label.pack(side="top", anchor="w", padx=10, pady=(0, 2))

            # Entry field
            entry = ctk.CTkEntry(field_frame, width=150)
            entry.pack(side="top", anchor="w", padx=10, pady=(0, 5))
            entry.insert(0, str(current_value))

            self.field_entries[field_name] = entry

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        save_btn = ctk.CTkButton(
            button_frame,
            text="💾 Save Changes",
            command=self.save_changes
        )
        save_btn.pack(side="left", padx=5)

        reset_btn = ctk.CTkButton(
            button_frame,
            text="🔄 Reset to Defaults",
            command=self.reset_to_defaults
        )
        reset_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Cancel",
            command=self.window.destroy
        )
        cancel_btn.pack(side="right", padx=5)

    def save_changes(self):
        """Save the economy field changes"""
        try:
            # Ensure economy-override section exists
            if "economy-override" not in self.data:
                self.data["economy-override"] = {}

            changes_made = 0
            for field_name, entry in self.field_entries.items():
                new_value = entry.get().strip()
                if new_value:
                    old_value = self.data["economy-override"].get(field_name, "")
                    if old_value != new_value:
                        self.data["economy-override"][field_name] = new_value
                        changes_made += 1

            self.status_callback(f"Updated {changes_made} economy fields")
            self.refresh_callback()
            messagebox.showinfo("Success", f"Successfully updated {changes_made} fields")
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save changes: {str(e)}")

    def reset_to_defaults(self):
        """Reset all fields to default values"""
        if messagebox.askyesno("Confirm Reset", "Reset all fields to default values?"):
            for field_name, entry in self.field_entries.items():
                entry.delete(0, "end")
                default_value = self.economy_fields[field_name][0]
                entry.insert(0, default_value)


# Fully implemented dialog classes
class MerchantLevelDialog:
    def __init__(self, parent, data, status_callback, refresh_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("👤 Edit Merchant Level")
        self.window.geometry("900x700")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the merchant level interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="👤 Edit Specific Merchants",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Selection frame
        selection_frame = ctk.CTkFrame(main_frame)
        selection_frame.pack(fill="x", padx=10, pady=10)

        # Outpost selection
        outpost_frame = ctk.CTkFrame(selection_frame)
        outpost_frame.pack(side="left", fill="both", expand=True, padx=5)

        outpost_label = ctk.CTkLabel(outpost_frame, text="🏢 Select Outpost:")
        outpost_label.pack(pady=5)

        self.outpost_var = ctk.StringVar(value="A_0")
        outpost_menu = ctk.CTkOptionMenu(
            outpost_frame,
            variable=self.outpost_var,
            values=self.get_outposts(),
            command=self.update_merchant_list
        )
        outpost_menu.pack(pady=5, padx=10, fill="x")

        # Price field selection
        field_frame = ctk.CTkFrame(selection_frame)
        field_frame.pack(side="right", fill="both", expand=True, padx=5)

        field_label = ctk.CTkLabel(field_frame, text="💰 Price Field:")
        field_label.pack(pady=5)

        self.price_field_var = ctk.StringVar(value="base-purchase-price")
        field_radio1 = ctk.CTkRadioButton(
            field_frame,
            text="Purchase Price",
            variable=self.price_field_var,
            value="base-purchase-price"
        )
        field_radio1.pack(pady=2)

        field_radio2 = ctk.CTkRadioButton(
            field_frame,
            text="Sell Price",
            variable=self.price_field_var,
            value="base-sell-price"
        )
        field_radio2.pack(pady=2)

        # Merchant selection
        merchant_frame = ctk.CTkFrame(main_frame)
        merchant_frame.pack(fill="both", expand=True, padx=10, pady=10)

        merchant_label = ctk.CTkLabel(merchant_frame, text="👤 Select Merchant:")
        merchant_label.pack(pady=5)

        # Create frame for listbox with scrollbar
        listbox_frame = ctk.CTkFrame(merchant_frame)
        listbox_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Merchant listbox with scrollbar
        self.merchant_listbox = tk.Listbox(listbox_frame, height=12, font=("Arial", 10))
        scrollbar = tk.Scrollbar(listbox_frame, orient="vertical", command=self.merchant_listbox.yview)
        self.merchant_listbox.configure(yscrollcommand=scrollbar.set)

        self.merchant_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        self.merchant_listbox.bind('<<ListboxSelect>>', self.on_merchant_select)

        # Adjustment controls
        adjust_frame = ctk.CTkFrame(main_frame)
        adjust_frame.pack(fill="x", padx=10, pady=10)

        # Percentage input
        percent_label = ctk.CTkLabel(adjust_frame, text="📊 Percentage Change (-99 to +100):")
        percent_label.pack(pady=5)

        self.percent_entry = ctk.CTkEntry(
            adjust_frame,
            placeholder_text="Enter percentage (e.g., -10, +25)"
        )
        self.percent_entry.pack(pady=5, padx=20, fill="x")

        # Preview area
        preview_frame = ctk.CTkFrame(adjust_frame)
        preview_frame.pack(fill="x", pady=10)

        preview_label = ctk.CTkLabel(preview_frame, text="📋 Merchant Preview:")
        preview_label.pack(pady=5)

        self.merchant_preview = ctk.CTkTextbox(preview_frame, height=100)
        self.merchant_preview.pack(fill="x", padx=10, pady=5)

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        preview_btn = ctk.CTkButton(
            button_frame,
            text="👁️ Preview Changes",
            command=self.preview_merchant_changes
        )
        preview_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="✅ Apply to Selected Merchant",
            command=self.apply_to_merchant
        )
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Close",
            command=self.window.destroy
        )
        cancel_btn.pack(side="right", padx=5)

        # Initialize merchant list
        self.update_merchant_list()

    def get_outposts(self):
        """Get list of available outposts"""
        outposts = set()
        traders = self.data.get("economy-override", {}).get("traders", {})
        for trader_key in traders.keys():
            outpost = trader_key.split("_")[0] if "_" in trader_key else "Unknown"
            outposts.add(outpost)
        return sorted(list(outposts)) if outposts else ["A_0", "B_4", "C_2", "Z_3"]

    def update_merchant_list(self, *args):
        """Update the merchant list based on selected outpost"""
        outpost = self.outpost_var.get()
        self.merchant_listbox.delete(0, tk.END)

        traders = self.data.get("economy-override", {}).get("traders", {})
        merchants = [
            trader for trader in traders.keys()
            if trader.startswith(outpost)
        ]

        for merchant in sorted(merchants):
            item_count = len(traders.get(merchant, []))
            self.merchant_listbox.insert(tk.END, f"{merchant} ({item_count} items)")

    def on_merchant_select(self, event):
        """Handle merchant selection"""
        selection = self.merchant_listbox.curselection()
        if selection:
            merchant_text = self.merchant_listbox.get(selection[0])
            merchant_name = merchant_text.split(" (")[0]  # Remove item count
            self.show_merchant_details(merchant_name)

    def show_merchant_details(self, merchant_name):
        """Show details of selected merchant"""
        traders = self.data.get("economy-override", {}).get("traders", {})
        merchant_items = traders.get(merchant_name, [])

        field = self.price_field_var.get()
        details = f"Merchant: {merchant_name}\nItems: {len(merchant_items)}\n\n"

        # Show first few items with current prices
        for i, item in enumerate(merchant_items[:10]):
            if isinstance(item, dict):
                code = item.get("tradeable-code", f"Item_{i}")
                price = item.get(field, "N/A")
                details += f"• {code}: ${price}\n"

        if len(merchant_items) > 10:
            details += f"... and {len(merchant_items) - 10} more items"

        self.merchant_preview.delete("1.0", "end")
        self.merchant_preview.insert("1.0", details)

    def preview_merchant_changes(self):
        """Preview changes for selected merchant"""
        selection = self.merchant_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a merchant")
            return

        try:
            percentage_str = self.percent_entry.get().strip()
            if not percentage_str:
                messagebox.showwarning("Warning", "Please enter a percentage")
                return

            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]

            percentage = float(percentage_str)
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return

            merchant_text = self.merchant_listbox.get(selection[0])
            merchant_name = merchant_text.split(" (")[0]
            field = self.price_field_var.get()

            traders = self.data.get("economy-override", {}).get("traders", {})
            merchant_items = traders.get(merchant_name, [])

            preview_text = f"Preview for {merchant_name} - {field} change of {percentage:+.1f}%:\n\n"
            count = 0

            for item in merchant_items:
                if isinstance(item, dict) and field in item and item[field] not in ["null", "-1", ""]:
                    try:
                        current_price = float(item[field])
                        new_price = max(1, round(current_price * (1 + percentage / 100)))
                        code = item.get('tradeable-code', 'Unknown')
                        preview_text += f"• {code}: ${current_price} → ${new_price}\n"
                        count += 1
                    except (ValueError, TypeError):
                        continue

            preview_text += f"\nTotal items to be changed: {count}"

            self.merchant_preview.delete("1.0", "end")
            self.merchant_preview.insert("1.0", preview_text)

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")

    def apply_to_merchant(self):
        """Apply percentage change to selected merchant"""
        selection = self.merchant_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a merchant")
            return

        try:
            percentage_str = self.percent_entry.get().strip()
            if not percentage_str:
                messagebox.showwarning("Warning", "Please enter a percentage")
                return

            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]

            percentage = float(percentage_str)
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return

            merchant_text = self.merchant_listbox.get(selection[0])
            merchant_name = merchant_text.split(" (")[0]
            field = self.price_field_var.get()

            if messagebox.askyesno("Confirm", f"Apply {percentage:+.1f}% change to {field} for {merchant_name}?"):
                count = 0
                traders = self.data.get("economy-override", {}).get("traders", {})
                trader_items = traders.get(merchant_name, [])

                for item in trader_items:
                    if isinstance(item, dict) and field in item and item[field] not in ["null", "-1", ""]:
                        try:
                            current_price = float(item[field])
                            new_price = max(1, round(current_price * (1 + percentage / 100)))
                            item[field] = str(new_price)
                            count += 1
                        except (ValueError, TypeError):
                            continue

                self.status_callback(f"Updated {count} items for {merchant_name}")
                self.refresh_callback()
                messagebox.showinfo("Success", f"Successfully updated {count} items for {merchant_name}")

                # Refresh the merchant details
                self.show_merchant_details(merchant_name)

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")
        except KeyError:
            messagebox.showerror("Error", "Merchant data not found")


class OutpostLevelDialog:
    def __init__(self, parent, data, status_callback, refresh_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("🏢 Edit Outpost Level")
        self.window.geometry("700x600")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the outpost level interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="🏢 Edit Outpost Level Prices",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Description
        desc = ctk.CTkLabel(
            main_frame,
            text="Apply percentage changes to all merchants in an outpost",
            font=ctk.CTkFont(size=12)
        )
        desc.pack(pady=5)

        # Outpost selection
        outpost_frame = ctk.CTkFrame(main_frame)
        outpost_frame.pack(fill="x", padx=10, pady=10)

        outpost_label = ctk.CTkLabel(outpost_frame, text="🏢 Select Outpost:")
        outpost_label.pack(pady=5)

        self.outpost_var = ctk.StringVar(value="A_0")
        outpost_menu = ctk.CTkOptionMenu(
            outpost_frame,
            variable=self.outpost_var,
            values=self.get_outposts(),
            command=self.update_outpost_preview
        )
        outpost_menu.pack(pady=5, padx=20, fill="x")

        # Price field selection
        field_frame = ctk.CTkFrame(main_frame)
        field_frame.pack(fill="x", padx=10, pady=10)

        field_label = ctk.CTkLabel(field_frame, text="💰 Select Price Field:")
        field_label.pack(pady=5)

        self.price_field_var = ctk.StringVar(value="base-purchase-price")
        field_radio1 = ctk.CTkRadioButton(
            field_frame,
            text="💰 Base Purchase Price",
            variable=self.price_field_var,
            value="base-purchase-price"
        )
        field_radio1.pack(pady=2)

        field_radio2 = ctk.CTkRadioButton(
            field_frame,
            text="💵 Base Sell Price",
            variable=self.price_field_var,
            value="base-sell-price"
        )
        field_radio2.pack(pady=2)

        # Percentage input
        percent_frame = ctk.CTkFrame(main_frame)
        percent_frame.pack(fill="x", padx=10, pady=10)

        percent_label = ctk.CTkLabel(percent_frame, text="📊 Percentage Change (-99 to +100):")
        percent_label.pack(pady=5)

        self.percent_entry = ctk.CTkEntry(
            percent_frame,
            placeholder_text="Enter percentage (e.g., -10, +25)"
        )
        self.percent_entry.pack(pady=5, padx=20, fill="x")

        # Preview section
        preview_frame = ctk.CTkFrame(main_frame)
        preview_frame.pack(fill="both", expand=True, padx=10, pady=10)

        preview_label = ctk.CTkLabel(preview_frame, text="📋 Outpost Preview:")
        preview_label.pack(pady=5)

        self.outpost_preview = ctk.CTkTextbox(preview_frame, height=200)
        self.outpost_preview.pack(fill="both", expand=True, padx=10, pady=5)

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        preview_btn = ctk.CTkButton(
            button_frame,
            text="👁️ Preview Changes",
            command=self.preview_outpost_changes
        )
        preview_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="✅ Apply to All Merchants in Outpost",
            command=self.apply_to_outpost
        )
        apply_btn.pack(side="left", padx=5)

        cancel_btn = ctk.CTkButton(
            button_frame,
            text="❌ Close",
            command=self.window.destroy
        )
        cancel_btn.pack(side="right", padx=5)

        # Initialize outpost preview
        self.update_outpost_preview()

    def get_outposts(self):
        """Get list of available outposts"""
        outposts = set()
        traders = self.data.get("economy-override", {}).get("traders", {})
        for trader_key in traders.keys():
            outpost = trader_key.split("_")[0] if "_" in trader_key else "Unknown"
            outposts.add(outpost)
        return sorted(list(outposts)) if outposts else ["A_0", "B_4", "C_2", "Z_3"]

    def update_outpost_preview(self, *args):
        """Update the outpost preview"""
        outpost = self.outpost_var.get()
        traders = self.data.get("economy-override", {}).get("traders", {})

        # Get merchants in this outpost
        outpost_merchants = [
            trader for trader in traders.keys()
            if trader.startswith(outpost)
        ]

        preview_text = f"Outpost: {outpost}\n"
        preview_text += f"Merchants: {len(outpost_merchants)}\n\n"

        total_items = 0
        for merchant in outpost_merchants:
            item_count = len(traders.get(merchant, []))
            total_items += item_count
            preview_text += f"• {merchant}: {item_count} items\n"

        preview_text += f"\nTotal items in outpost: {total_items}"

        self.outpost_preview.delete("1.0", "end")
        self.outpost_preview.insert("1.0", preview_text)

    def preview_outpost_changes(self):
        """Preview changes for selected outpost"""
        try:
            percentage_str = self.percent_entry.get().strip()
            if not percentage_str:
                messagebox.showwarning("Warning", "Please enter a percentage")
                return

            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]

            percentage = float(percentage_str)
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return

            outpost = self.outpost_var.get()
            field = self.price_field_var.get()
            traders = self.data.get("economy-override", {}).get("traders", {})

            preview_text = f"Preview for {outpost} - {field} change of {percentage:+.1f}%:\n\n"

            total_count = 0
            merchant_count = 0

            for trader_name, trader_items in traders.items():
                if trader_name.startswith(outpost):
                    merchant_count += 1
                    merchant_changes = 0

                    for item in trader_items:
                        if isinstance(item, dict) and field in item and item[field] not in ["null", "-1", ""]:
                            try:
                                current_price = float(item[field])
                                new_price = max(1, round(current_price * (1 + percentage / 100)))
                                merchant_changes += 1
                                total_count += 1
                            except (ValueError, TypeError):
                                continue

                    preview_text += f"• {trader_name}: {merchant_changes} items\n"

            preview_text += f"\nTotal merchants: {merchant_count}"
            preview_text += f"\nTotal items to be changed: {total_count}"

            self.outpost_preview.delete("1.0", "end")
            self.outpost_preview.insert("1.0", preview_text)

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")

    def apply_to_outpost(self):
        """Apply percentage change to all merchants in selected outpost"""
        try:
            percentage_str = self.percent_entry.get().strip()
            if not percentage_str:
                messagebox.showwarning("Warning", "Please enter a percentage")
                return

            if percentage_str.startswith('+'):
                percentage_str = percentage_str[1:]

            percentage = float(percentage_str)
            if not -99 <= percentage <= 100:
                messagebox.showerror("Error", "Percentage must be between -99 and 100")
                return

            outpost = self.outpost_var.get()
            field = self.price_field_var.get()

            if messagebox.askyesno("Confirm", f"Apply {percentage:+.1f}% change to {field} for all merchants in {outpost}?"):
                count = 0
                merchant_count = 0
                traders = self.data.get("economy-override", {}).get("traders", {})

                for trader_name, trader_items in traders.items():
                    if trader_name.startswith(outpost):
                        merchant_count += 1
                        for item in trader_items:
                            if isinstance(item, dict) and field in item and item[field] not in ["null", "-1", ""]:
                                try:
                                    current_price = float(item[field])
                                    new_price = max(1, round(current_price * (1 + percentage / 100)))
                                    item[field] = str(new_price)
                                    count += 1
                                except (ValueError, TypeError):
                                    continue

                self.status_callback(f"Updated {count} items across {merchant_count} merchants in {outpost}")
                self.refresh_callback()
                messagebox.showinfo("Success", f"Successfully updated {count} items across {merchant_count} merchants")

                # Refresh the outpost preview
                self.update_outpost_preview()

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid percentage number")


class FineTuneDialog:
    def __init__(self, parent, data, status_callback, refresh_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback
        self.current_item = None
        self.current_trader = None
        self.current_index = None

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("🔧 Fine Tune Items")
        self.window.geometry("1000x800")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the fine tune interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="🔧 Fine Tune Items",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Description
        desc = ctk.CTkLabel(
            main_frame,
            text="Search and edit individual items with precision",
            font=ctk.CTkFont(size=12)
        )
        desc.pack(pady=5)

        # Search frame
        search_frame = ctk.CTkFrame(main_frame)
        search_frame.pack(fill="x", padx=10, pady=10)

        search_label = ctk.CTkLabel(search_frame, text="🔍 Search Items:")
        search_label.pack(side="left", padx=5)

        self.search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="Enter item code (e.g., Weapon_AK74, Cal_556)"
        )
        self.search_entry.pack(side="left", fill="x", expand=True, padx=5)

        search_btn = ctk.CTkButton(
            search_frame,
            text="Search",
            command=self.search_items,
            width=80
        )
        search_btn.pack(side="right", padx=5)

        # Content frame with two columns
        content_frame = ctk.CTkFrame(main_frame)
        content_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Left column - Search results
        left_frame = ctk.CTkFrame(content_frame)
        left_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))

        results_label = ctk.CTkLabel(left_frame, text="📋 Search Results:")
        results_label.pack(pady=5)

        # Results listbox
        self.results_listbox = tk.Listbox(left_frame, height=20, font=("Arial", 10))
        results_scrollbar = tk.Scrollbar(left_frame, orient="vertical", command=self.results_listbox.yview)
        self.results_listbox.configure(yscrollcommand=results_scrollbar.set)

        self.results_listbox.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        results_scrollbar.pack(side="right", fill="y", pady=5)

        self.results_listbox.bind('<<ListboxSelect>>', self.on_item_select)

        # Right column - Item editor
        right_frame = ctk.CTkFrame(content_frame)
        right_frame.pack(side="right", fill="both", expand=True, padx=(5, 0))

        editor_label = ctk.CTkLabel(right_frame, text="✏️ Item Editor:")
        editor_label.pack(pady=5)

        # Create scrollable frame for fields
        self.editor_scroll = ctk.CTkScrollableFrame(right_frame)
        self.editor_scroll.pack(fill="both", expand=True, padx=10, pady=5)

        # Field entries will be created dynamically
        self.field_entries = {}

        # Buttons frame
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        update_btn = ctk.CTkButton(
            button_frame,
            text="💾 Update Item",
            command=self.update_item
        )
        update_btn.pack(side="left", padx=5)

        reset_btn = ctk.CTkButton(
            button_frame,
            text="🔄 Reset Fields",
            command=self.reset_fields
        )
        reset_btn.pack(side="left", padx=5)

        close_btn = ctk.CTkButton(
            button_frame,
            text="❌ Close",
            command=self.window.destroy
        )
        close_btn.pack(side="right", padx=5)

        # Initial message
        self.show_initial_message()

    def show_initial_message(self):
        """Show initial message in editor"""
        for widget in self.editor_scroll.winfo_children():
            widget.destroy()

        msg_label = ctk.CTkLabel(
            self.editor_scroll,
            text="🔍 Search for items to begin editing\n\nEnter part of an item code in the search box\nand click Search to find matching items.",
            font=ctk.CTkFont(size=14),
            justify="center"
        )
        msg_label.pack(expand=True, pady=50)

    def search_items(self):
        """Search for items matching the search term"""
        search_term = self.search_entry.get().strip().lower()
        if not search_term:
            messagebox.showwarning("Warning", "Please enter a search term")
            return

        # Clear previous results
        self.results_listbox.delete(0, tk.END)

        matches = []
        traders = self.data.get("economy-override", {}).get("traders", {})

        for trader_name, trader_items in traders.items():
            for idx, item in enumerate(trader_items):
                if isinstance(item, dict):
                    code = item.get("tradeable-code", "").lower()
                    if search_term in code:
                        matches.append((trader_name, idx, item))

        if matches:
            for trader_name, idx, item in matches:
                code = item.get("tradeable-code", "Unknown")
                price = item.get("base-purchase-price", "N/A")
                display_text = f"{code} - ${price} ({trader_name})"
                self.results_listbox.insert(tk.END, display_text)

            self.search_matches = matches
            self.status_callback(f"Found {len(matches)} items matching '{search_term}'")
        else:
            self.results_listbox.insert(tk.END, "No items found")
            self.search_matches = []
            self.status_callback(f"No items found matching '{search_term}'")

    def on_item_select(self, event):
        """Handle item selection from search results"""
        selection = self.results_listbox.curselection()
        if not selection or not hasattr(self, 'search_matches'):
            return

        if selection[0] < len(self.search_matches):
            trader_name, idx, item = self.search_matches[selection[0]]
            self.current_trader = trader_name
            self.current_index = idx
            self.current_item = item
            self.create_item_editor(item, trader_name)

    def create_item_editor(self, item, trader_name):
        """Create the item editor interface"""
        # Clear existing editor
        for widget in self.editor_scroll.winfo_children():
            widget.destroy()

        self.field_entries = {}

        # Item info header
        info_frame = ctk.CTkFrame(self.editor_scroll)
        info_frame.pack(fill="x", padx=5, pady=5)

        info_label = ctk.CTkLabel(
            info_frame,
            text=f"Editing: {item.get('tradeable-code', 'Unknown')}\nTrader: {trader_name}",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        info_label.pack(pady=5)

        # Common fields to edit
        common_fields = [
            ("tradeable-code", "Item Code", False),  # Read-only
            ("base-purchase-price", "Purchase Price", True),
            ("base-sell-price", "Sell Price", True),
            ("required-famepoints", "Fame Points Required", True),
            ("can-be-purchased", "Can Be Purchased", True),
        ]

        # Create fields
        for field_key, field_label, editable in common_fields:
            field_frame = ctk.CTkFrame(self.editor_scroll)
            field_frame.pack(fill="x", padx=5, pady=3)

            label = ctk.CTkLabel(
                field_frame,
                text=field_label,
                width=150,
                anchor="w"
            )
            label.pack(side="left", padx=5, pady=5)

            if field_key == "can-be-purchased":
                # Special handling for boolean field
                var = ctk.StringVar(value=item.get(field_key, "true"))
                combo = ctk.CTkComboBox(
                    field_frame,
                    variable=var,
                    values=["true", "false"],
                    width=200,
                    state="normal" if editable else "disabled"
                )
                combo.pack(side="right", padx=5, pady=5)
                self.field_entries[field_key] = var
            else:
                # Regular text field
                entry = ctk.CTkEntry(
                    field_frame,
                    width=200,
                    state="normal" if editable else "disabled"
                )
                entry.pack(side="right", padx=5, pady=5)
                entry.insert(0, str(item.get(field_key, "")))
                self.field_entries[field_key] = entry

        # Additional fields (if any exist in the item)
        additional_fields = [key for key in item.keys() if key not in [f[0] for f in common_fields]]

        if additional_fields:
            # Separator
            sep_frame = ctk.CTkFrame(self.editor_scroll)
            sep_frame.pack(fill="x", padx=5, pady=10)

            sep_label = ctk.CTkLabel(
                sep_frame,
                text="Additional Fields",
                font=ctk.CTkFont(size=12, weight="bold")
            )
            sep_label.pack(pady=5)

            # Additional field entries
            for field_key in additional_fields:
                field_frame = ctk.CTkFrame(self.editor_scroll)
                field_frame.pack(fill="x", padx=5, pady=3)

                label = ctk.CTkLabel(
                    field_frame,
                    text=field_key.replace("-", " ").title(),
                    width=150,
                    anchor="w"
                )
                label.pack(side="left", padx=5, pady=5)

                entry = ctk.CTkEntry(field_frame, width=200)
                entry.pack(side="right", padx=5, pady=5)
                entry.insert(0, str(item.get(field_key, "")))
                self.field_entries[field_key] = entry

    def update_item(self):
        """Update the current item with new values"""
        if not self.current_item or not self.current_trader or self.current_index is None:
            messagebox.showwarning("Warning", "No item selected for editing")
            return

        try:
            # Collect new values
            changes_made = 0
            for field_key, widget in self.field_entries.items():
                if field_key == "tradeable-code":
                    continue  # Skip read-only field

                if isinstance(widget, ctk.StringVar):
                    new_value = widget.get()
                else:
                    new_value = widget.get().strip()

                old_value = self.current_item.get(field_key, "")
                if str(old_value) != new_value:
                    self.current_item[field_key] = new_value
                    changes_made += 1

            if changes_made > 0:
                # Update the item in the data structure
                traders = self.data.get("economy-override", {}).get("traders", {})
                traders[self.current_trader][self.current_index] = self.current_item

                self.status_callback(f"Updated {changes_made} fields for {self.current_item.get('tradeable-code', 'item')}")
                self.refresh_callback()
                messagebox.showinfo("Success", f"Successfully updated {changes_made} fields")
            else:
                messagebox.showinfo("No Changes", "No changes were made to the item")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to update item: {str(e)}")

    def reset_fields(self):
        """Reset all fields to their original values"""
        if not self.current_item:
            return

        if messagebox.askyesno("Confirm Reset", "Reset all fields to original values?"):
            self.create_item_editor(self.current_item, self.current_trader)


class SpreadEditDialog:
    def __init__(self, parent, data, status_callback, refresh_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("📊 Spread Edit Items")
        self.window.geometry("900x700")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the spread edit interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="📊 Spread Edit Items",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Description
        desc = ctk.CTkLabel(
            main_frame,
            text="Apply changes across multiple traders with filters and conditions",
            font=ctk.CTkFont(size=12)
        )
        desc.pack(pady=5)

        # Filter frame
        filter_frame = ctk.CTkFrame(main_frame)
        filter_frame.pack(fill="x", padx=10, pady=10)

        filter_label = ctk.CTkLabel(filter_frame, text="🔍 Item Filters:")
        filter_label.pack(pady=5)

        # Item code filter
        code_frame = ctk.CTkFrame(filter_frame)
        code_frame.pack(fill="x", padx=10, pady=5)

        code_label = ctk.CTkLabel(code_frame, text="Item Code Contains:", width=150, anchor="w")
        code_label.pack(side="left", padx=5)

        self.code_filter = ctk.CTkEntry(
            code_frame,
            placeholder_text="e.g., Weapon, Cal, Crafted (leave empty for all)"
        )
        self.code_filter.pack(side="right", fill="x", expand=True, padx=5)

        # Price range filter
        price_frame = ctk.CTkFrame(filter_frame)
        price_frame.pack(fill="x", padx=10, pady=5)

        price_label = ctk.CTkLabel(price_frame, text="Price Range:", width=150, anchor="w")
        price_label.pack(side="left", padx=5)

        self.min_price = ctk.CTkEntry(price_frame, placeholder_text="Min", width=80)
        self.min_price.pack(side="left", padx=2)

        to_label = ctk.CTkLabel(price_frame, text="to", width=20)
        to_label.pack(side="left", padx=2)

        self.max_price = ctk.CTkEntry(price_frame, placeholder_text="Max", width=80)
        self.max_price.pack(side="left", padx=2)

        # Outpost filter
        outpost_frame = ctk.CTkFrame(filter_frame)
        outpost_frame.pack(fill="x", padx=10, pady=5)

        outpost_label = ctk.CTkLabel(outpost_frame, text="Outpost Filter:", width=150, anchor="w")
        outpost_label.pack(side="left", padx=5)

        self.outpost_filter = ctk.CTkOptionMenu(
            outpost_frame,
            values=["All Outposts"] + self.get_outposts()
        )
        self.outpost_filter.pack(side="right", padx=5)

        # Operation frame
        operation_frame = ctk.CTkFrame(main_frame)
        operation_frame.pack(fill="x", padx=10, pady=10)

        operation_label = ctk.CTkLabel(operation_frame, text="⚙️ Operation Settings:")
        operation_label.pack(pady=5)

        # Price field selection
        field_frame = ctk.CTkFrame(operation_frame)
        field_frame.pack(fill="x", padx=10, pady=5)

        field_label = ctk.CTkLabel(field_frame, text="Price Field:")
        field_label.pack(side="left", padx=5)

        self.price_field_var = ctk.StringVar(value="base-purchase-price")
        field_radio1 = ctk.CTkRadioButton(
            field_frame,
            text="Purchase Price",
            variable=self.price_field_var,
            value="base-purchase-price"
        )
        field_radio1.pack(side="left", padx=10)

        field_radio2 = ctk.CTkRadioButton(
            field_frame,
            text="Sell Price",
            variable=self.price_field_var,
            value="base-sell-price"
        )
        field_radio2.pack(side="left", padx=10)

        # Operation type
        op_type_frame = ctk.CTkFrame(operation_frame)
        op_type_frame.pack(fill="x", padx=10, pady=5)

        op_type_label = ctk.CTkLabel(op_type_frame, text="Operation:")
        op_type_label.pack(side="left", padx=5)

        self.operation_var = ctk.StringVar(value="percentage")
        op_radio1 = ctk.CTkRadioButton(
            op_type_frame,
            text="Percentage Change",
            variable=self.operation_var,
            value="percentage"
        )
        op_radio1.pack(side="left", padx=10)

        op_radio2 = ctk.CTkRadioButton(
            op_type_frame,
            text="Set Fixed Value",
            variable=self.operation_var,
            value="fixed"
        )
        op_radio2.pack(side="left", padx=10)

        # Value input
        value_frame = ctk.CTkFrame(operation_frame)
        value_frame.pack(fill="x", padx=10, pady=5)

        value_label = ctk.CTkLabel(value_frame, text="Value:", width=100, anchor="w")
        value_label.pack(side="left", padx=5)

        self.value_entry = ctk.CTkEntry(
            value_frame,
            placeholder_text="Enter percentage (-99 to +100) or fixed value"
        )
        self.value_entry.pack(side="right", fill="x", expand=True, padx=5)

        # Preview frame
        preview_frame = ctk.CTkFrame(main_frame)
        preview_frame.pack(fill="both", expand=True, padx=10, pady=10)

        preview_label = ctk.CTkLabel(preview_frame, text="📋 Preview Matching Items:")
        preview_label.pack(pady=5)

        self.preview_text = ctk.CTkTextbox(preview_frame, height=200)
        self.preview_text.pack(fill="both", expand=True, padx=10, pady=5)

        # Buttons
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", padx=10, pady=10)

        filter_btn = ctk.CTkButton(
            button_frame,
            text="🔍 Apply Filters",
            command=self.apply_filters
        )
        filter_btn.pack(side="left", padx=5)

        preview_btn = ctk.CTkButton(
            button_frame,
            text="👁️ Preview Changes",
            command=self.preview_changes
        )
        preview_btn.pack(side="left", padx=5)

        apply_btn = ctk.CTkButton(
            button_frame,
            text="✅ Apply Changes",
            command=self.apply_changes
        )
        apply_btn.pack(side="left", padx=5)

        close_btn = ctk.CTkButton(
            button_frame,
            text="❌ Close",
            command=self.window.destroy
        )
        close_btn.pack(side="right", padx=5)

        # Initialize with all items
        self.apply_filters()

    def get_outposts(self):
        """Get list of available outposts"""
        outposts = set()
        traders = self.data.get("economy-override", {}).get("traders", {})
        for trader_key in traders.keys():
            outpost = trader_key.split("_")[0] if "_" in trader_key else "Unknown"
            outposts.add(outpost)
        return sorted(list(outposts))

    def apply_filters(self):
        """Apply filters and show matching items"""
        code_filter = self.code_filter.get().strip().lower()
        min_price_str = self.min_price.get().strip()
        max_price_str = self.max_price.get().strip()
        outpost_filter = self.outpost_filter.get()

        # Parse price filters
        min_price = None
        max_price = None
        try:
            if min_price_str:
                min_price = float(min_price_str)
            if max_price_str:
                max_price = float(max_price_str)
        except ValueError:
            messagebox.showerror("Error", "Invalid price range values")
            return

        # Find matching items
        matches = []
        traders = self.data.get("economy-override", {}).get("traders", {})

        for trader_name, trader_items in traders.items():
            # Check outpost filter
            if outpost_filter != "All Outposts":
                trader_outpost = trader_name.split("_")[0] if "_" in trader_name else "Unknown"
                if trader_outpost != outpost_filter:
                    continue

            for idx, item in enumerate(trader_items):
                if isinstance(item, dict):
                    code = item.get("tradeable-code", "").lower()

                    # Check code filter
                    if code_filter and code_filter not in code:
                        continue

                    # Check price filter
                    price_field = self.price_field_var.get()
                    price_str = item.get(price_field, "")
                    if price_str and price_str not in ["null", "-1"]:
                        try:
                            price = float(price_str)
                            if min_price is not None and price < min_price:
                                continue
                            if max_price is not None and price > max_price:
                                continue
                        except (ValueError, TypeError):
                            continue

                    matches.append((trader_name, idx, item))

        # Display matches
        preview_text = f"Found {len(matches)} items matching filters:\n\n"

        for i, (trader_name, idx, item) in enumerate(matches[:20]):  # Show first 20
            code = item.get("tradeable-code", "Unknown")
            price = item.get(self.price_field_var.get(), "N/A")
            preview_text += f"• {code} - ${price} ({trader_name})\n"

        if len(matches) > 20:
            preview_text += f"\n... and {len(matches) - 20} more items"

        self.preview_text.delete("1.0", "end")
        self.preview_text.insert("1.0", preview_text)

        self.filtered_matches = matches
        self.status_callback(f"Filtered to {len(matches)} items")

    def preview_changes(self):
        """Preview the changes that would be made"""
        if not hasattr(self, 'filtered_matches'):
            messagebox.showwarning("Warning", "Please apply filters first")
            return

        try:
            value_str = self.value_entry.get().strip()
            if not value_str:
                messagebox.showwarning("Warning", "Please enter a value")
                return

            operation = self.operation_var.get()
            field = self.price_field_var.get()

            if operation == "percentage":
                if value_str.startswith('+'):
                    value_str = value_str[1:]
                percentage = float(value_str)
                if not -99 <= percentage <= 100:
                    messagebox.showerror("Error", "Percentage must be between -99 and 100")
                    return
            else:  # fixed value
                fixed_value = float(value_str)
                if fixed_value < 0:
                    messagebox.showerror("Error", "Fixed value must be positive")
                    return

            # Generate preview
            preview_text = f"Preview of {operation} operation on {field}:\n\n"

            count = 0
            for trader_name, idx, item in self.filtered_matches[:10]:  # Show first 10
                if field in item and item[field] not in ["null", "-1", ""]:
                    try:
                        current_price = float(item[field])

                        if operation == "percentage":
                            new_price = max(1, round(current_price * (1 + percentage / 100)))
                        else:  # fixed
                            new_price = int(fixed_value)

                        code = item.get("tradeable-code", "Unknown")
                        preview_text += f"• {code}: ${current_price} → ${new_price} ({trader_name})\n"
                        count += 1
                    except (ValueError, TypeError):
                        continue

            total_applicable = sum(1 for _, _, item in self.filtered_matches
                                 if field in item and item[field] not in ["null", "-1", ""])

            if len(self.filtered_matches) > 10:
                preview_text += f"\n... and {total_applicable - count} more applicable items"

            preview_text += f"\n\nTotal items that will be changed: {total_applicable}"

            self.preview_text.delete("1.0", "end")
            self.preview_text.insert("1.0", preview_text)

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number")

    def apply_changes(self):
        """Apply the spread edit changes"""
        if not hasattr(self, 'filtered_matches'):
            messagebox.showwarning("Warning", "Please apply filters first")
            return

        try:
            value_str = self.value_entry.get().strip()
            if not value_str:
                messagebox.showwarning("Warning", "Please enter a value")
                return

            operation = self.operation_var.get()
            field = self.price_field_var.get()

            if operation == "percentage":
                if value_str.startswith('+'):
                    value_str = value_str[1:]
                percentage = float(value_str)
                if not -99 <= percentage <= 100:
                    messagebox.showerror("Error", "Percentage must be between -99 and 100")
                    return
                operation_desc = f"{percentage:+.1f}% change"
            else:  # fixed value
                fixed_value = float(value_str)
                if fixed_value < 0:
                    messagebox.showerror("Error", "Fixed value must be positive")
                    return
                operation_desc = f"set to ${fixed_value}"

            # Confirm operation
            applicable_count = sum(1 for _, _, item in self.filtered_matches
                                 if field in item and item[field] not in ["null", "-1", ""])

            if messagebox.askyesno("Confirm",
                                 f"Apply {operation_desc} to {field} for {applicable_count} items?"):
                count = 0

                for trader_name, idx, item in self.filtered_matches:
                    if field in item and item[field] not in ["null", "-1", ""]:
                        try:
                            current_price = float(item[field])

                            if operation == "percentage":
                                new_price = max(1, round(current_price * (1 + percentage / 100)))
                            else:  # fixed
                                new_price = int(fixed_value)

                            item[field] = str(new_price)
                            count += 1
                        except (ValueError, TypeError):
                            continue

                self.status_callback(f"Applied {operation_desc} to {count} items")
                self.refresh_callback()
                messagebox.showinfo("Success", f"Successfully updated {count} items")

                # Refresh the preview
                self.apply_filters()

        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number")


class PurchaseSettingsDialog:
    def __init__(self, parent, data, status_callback, refresh_callback):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback

        # Create dialog window
        self.window = ctk.CTkToplevel(parent)
        self.window.title("🛒 Purchase Settings")
        self.window.geometry("800x700")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create the purchase settings interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="🛒 Purchase Settings Manager",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Description
        desc = ctk.CTkLabel(
            main_frame,
            text="Manage item availability and fame point requirements",
            font=ctk.CTkFont(size=12)
        )
        desc.pack(pady=5)

        # Tabview for different operations
        tabview = ctk.CTkTabview(main_frame)
        tabview.pack(fill="both", expand=True, padx=10, pady=10)

        # Add tabs
        tabview.add("🔓 Availability")
        tabview.add("⭐ Fame Points")
        tabview.add("📊 Bulk Operations")

        self.create_availability_tab(tabview.tab("🔓 Availability"))
        self.create_fame_points_tab(tabview.tab("⭐ Fame Points"))
        self.create_bulk_operations_tab(tabview.tab("📊 Bulk Operations"))

        # Close button
        close_btn = ctk.CTkButton(main_frame, text="❌ Close", command=self.window.destroy)
        close_btn.pack(pady=10)

    def create_availability_tab(self, parent):
        """Create availability management tab"""
        # Search frame
        search_frame = ctk.CTkFrame(parent)
        search_frame.pack(fill="x", padx=10, pady=10)

        search_label = ctk.CTkLabel(search_frame, text="🔍 Search Items:")
        search_label.pack(side="left", padx=5)

        self.avail_search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="Enter item code to search..."
        )
        self.avail_search_entry.pack(side="left", fill="x", expand=True, padx=5)

        search_btn = ctk.CTkButton(
            search_frame,
            text="Search",
            command=self.search_availability_items,
            width=80
        )
        search_btn.pack(side="right", padx=5)

        # Results frame
        results_frame = ctk.CTkFrame(parent)
        results_frame.pack(fill="both", expand=True, padx=10, pady=10)

        results_label = ctk.CTkLabel(results_frame, text="📋 Items and Availability:")
        results_label.pack(pady=5)

        # Results listbox
        self.avail_listbox = tk.Listbox(results_frame, height=15, font=("Arial", 10))
        avail_scrollbar = tk.Scrollbar(results_frame, orient="vertical", command=self.avail_listbox.yview)
        self.avail_listbox.configure(yscrollcommand=avail_scrollbar.set)

        self.avail_listbox.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        avail_scrollbar.pack(side="right", fill="y", pady=5)

        # Controls frame
        controls_frame = ctk.CTkFrame(parent)
        controls_frame.pack(fill="x", padx=10, pady=10)

        enable_btn = ctk.CTkButton(
            controls_frame,
            text="✅ Enable Purchase",
            command=lambda: self.toggle_availability(True)
        )
        enable_btn.pack(side="left", padx=5)

        disable_btn = ctk.CTkButton(
            controls_frame,
            text="❌ Disable Purchase",
            command=lambda: self.toggle_availability(False)
        )
        disable_btn.pack(side="left", padx=5)

    def create_fame_points_tab(self, parent):
        """Create fame points management tab"""
        # Search frame
        search_frame = ctk.CTkFrame(parent)
        search_frame.pack(fill="x", padx=10, pady=10)

        search_label = ctk.CTkLabel(search_frame, text="🔍 Search Items:")
        search_label.pack(side="left", padx=5)

        self.fame_search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="Enter item code to search..."
        )
        self.fame_search_entry.pack(side="left", fill="x", expand=True, padx=5)

        search_btn = ctk.CTkButton(
            search_frame,
            text="Search",
            command=self.search_fame_items,
            width=80
        )
        search_btn.pack(side="right", padx=5)

        # Results frame
        results_frame = ctk.CTkFrame(parent)
        results_frame.pack(fill="both", expand=True, padx=10, pady=10)

        results_label = ctk.CTkLabel(results_frame, text="📋 Items and Fame Requirements:")
        results_label.pack(pady=5)

        # Results listbox
        self.fame_listbox = tk.Listbox(results_frame, height=12, font=("Arial", 10))
        fame_scrollbar = tk.Scrollbar(results_frame, orient="vertical", command=self.fame_listbox.yview)
        self.fame_listbox.configure(yscrollcommand=fame_scrollbar.set)

        self.fame_listbox.pack(side="left", fill="both", expand=True, padx=5, pady=5)
        fame_scrollbar.pack(side="right", fill="y", pady=5)

        # Controls frame
        controls_frame = ctk.CTkFrame(parent)
        controls_frame.pack(fill="x", padx=10, pady=10)

        fame_label = ctk.CTkLabel(controls_frame, text="⭐ Set Fame Points:")
        fame_label.pack(side="left", padx=5)

        self.fame_entry = ctk.CTkEntry(
            controls_frame,
            placeholder_text="Enter fame points (0-1000)",
            width=150
        )
        self.fame_entry.pack(side="left", padx=5)

        set_fame_btn = ctk.CTkButton(
            controls_frame,
            text="Set Fame Points",
            command=self.set_fame_points
        )
        set_fame_btn.pack(side="left", padx=5)

    def create_bulk_operations_tab(self, parent):
        """Create bulk operations tab"""
        # Category frame
        category_frame = ctk.CTkFrame(parent)
        category_frame.pack(fill="x", padx=10, pady=10)

        category_label = ctk.CTkLabel(category_frame, text="📂 Select Category:")
        category_label.pack(pady=5)

        self.category_var = ctk.StringVar(value="All Items")
        category_menu = ctk.CTkOptionMenu(
            category_frame,
            variable=self.category_var,
            values=self.get_categories()
        )
        category_menu.pack(pady=5, padx=20, fill="x")

        # Operations frame
        ops_frame = ctk.CTkFrame(parent)
        ops_frame.pack(fill="both", expand=True, padx=10, pady=10)

        ops_label = ctk.CTkLabel(ops_frame, text="🔧 Bulk Operations:")
        ops_label.pack(pady=5)

        # Availability operations
        avail_frame = ctk.CTkFrame(ops_frame)
        avail_frame.pack(fill="x", padx=10, pady=5)

        avail_label = ctk.CTkLabel(avail_frame, text="🔓 Availability Operations:")
        avail_label.pack(pady=5)

        enable_all_btn = ctk.CTkButton(
            avail_frame,
            text="✅ Enable All in Category",
            command=lambda: self.bulk_availability(True)
        )
        enable_all_btn.pack(side="left", padx=5, pady=5)

        disable_all_btn = ctk.CTkButton(
            avail_frame,
            text="❌ Disable All in Category",
            command=lambda: self.bulk_availability(False)
        )
        disable_all_btn.pack(side="left", padx=5, pady=5)

        # Fame points operations
        fame_frame = ctk.CTkFrame(ops_frame)
        fame_frame.pack(fill="x", padx=10, pady=5)

        fame_label = ctk.CTkLabel(fame_frame, text="⭐ Fame Points Operations:")
        fame_label.pack(pady=5)

        self.bulk_fame_entry = ctk.CTkEntry(
            fame_frame,
            placeholder_text="Fame points (0-1000)",
            width=150
        )
        self.bulk_fame_entry.pack(side="left", padx=5, pady=5)

        set_bulk_fame_btn = ctk.CTkButton(
            fame_frame,
            text="Set Fame for Category",
            command=self.bulk_fame_points
        )
        set_bulk_fame_btn.pack(side="left", padx=5, pady=5)

        # Results display
        self.bulk_results = ctk.CTkTextbox(ops_frame, height=100)
        self.bulk_results.pack(fill="x", padx=10, pady=10)

    def get_categories(self):
        """Get list of item categories"""
        categories = set(["All Items"])
        traders = self.data.get("economy-override", {}).get("traders", {})

        for trader_items in traders.values():
            for item in trader_items:
                if isinstance(item, dict):
                    code = item.get("tradeable-code", "")
                    if code and "_" in code:
                        category = code.split("_")[0]
                        categories.add(category)

        return sorted(list(categories))

    def search_availability_items(self):
        """Search items for availability management"""
        search_term = self.avail_search_entry.get().strip().lower()
        if not search_term:
            messagebox.showwarning("Warning", "Please enter a search term")
            return

        self.avail_listbox.delete(0, tk.END)
        matches = []
        traders = self.data.get("economy-override", {}).get("traders", {})

        for trader_name, trader_items in traders.items():
            for idx, item in enumerate(trader_items):
                if isinstance(item, dict):
                    code = item.get("tradeable-code", "").lower()
                    if search_term in code:
                        can_purchase = item.get("can-be-purchased", "true")
                        status = "✅ Available" if can_purchase.lower() == "true" else "❌ Disabled"
                        display_text = f"{item.get('tradeable-code', 'Unknown')} - {status} ({trader_name})"
                        self.avail_listbox.insert(tk.END, display_text)
                        matches.append((trader_name, idx, item))

        self.avail_matches = matches
        self.status_callback(f"Found {len(matches)} items for availability management")

    def search_fame_items(self):
        """Search items for fame points management"""
        search_term = self.fame_search_entry.get().strip().lower()
        if not search_term:
            messagebox.showwarning("Warning", "Please enter a search term")
            return

        self.fame_listbox.delete(0, tk.END)
        matches = []
        traders = self.data.get("economy-override", {}).get("traders", {})

        for trader_name, trader_items in traders.items():
            for idx, item in enumerate(trader_items):
                if isinstance(item, dict):
                    code = item.get("tradeable-code", "").lower()
                    if search_term in code:
                        fame_points = item.get("required-famepoints", "0")
                        display_text = f"{item.get('tradeable-code', 'Unknown')} - {fame_points} FP ({trader_name})"
                        self.fame_listbox.insert(tk.END, display_text)
                        matches.append((trader_name, idx, item))

        self.fame_matches = matches
        self.status_callback(f"Found {len(matches)} items for fame points management")

    def toggle_availability(self, enable):
        """Toggle availability for selected items"""
        if not hasattr(self, 'avail_matches'):
            messagebox.showwarning("Warning", "Please search for items first")
            return

        selection = self.avail_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select items to modify")
            return

        value = "true" if enable else "false"
        action = "enable" if enable else "disable"

        if messagebox.askyesno("Confirm", f"Are you sure you want to {action} purchase for selected items?"):
            count = 0
            for idx in selection:
                if idx < len(self.avail_matches):
                    trader_name, item_idx, item = self.avail_matches[idx]
                    item["can-be-purchased"] = value
                    count += 1

            self.status_callback(f"Updated availability for {count} items")
            self.refresh_callback()
            messagebox.showinfo("Success", f"Successfully {action}d purchase for {count} items")

            # Refresh the search results
            self.search_availability_items()

    def set_fame_points(self):
        """Set fame points for selected items"""
        if not hasattr(self, 'fame_matches'):
            messagebox.showwarning("Warning", "Please search for items first")
            return

        selection = self.fame_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select items to modify")
            return

        try:
            fame_points = int(self.fame_entry.get().strip())
            if not 0 <= fame_points <= 1000:
                messagebox.showerror("Error", "Fame points must be between 0 and 1000")
                return
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number for fame points")
            return

        if messagebox.askyesno("Confirm", f"Set fame points to {fame_points} for selected items?"):
            count = 0
            for idx in selection:
                if idx < len(self.fame_matches):
                    trader_name, item_idx, item = self.fame_matches[idx]
                    item["required-famepoints"] = str(fame_points)
                    count += 1

            self.status_callback(f"Updated fame points for {count} items")
            self.refresh_callback()
            messagebox.showinfo("Success", f"Successfully set fame points to {fame_points} for {count} items")

            # Refresh the search results
            self.search_fame_items()

    def bulk_availability(self, enable):
        """Bulk availability operation"""
        category = self.category_var.get()
        value = "true" if enable else "false"
        action = "enable" if enable else "disable"

        if messagebox.askyesno("Confirm", f"Are you sure you want to {action} purchase for all items in '{category}'?"):
            count = 0
            traders = self.data.get("economy-override", {}).get("traders", {})

            for trader_items in traders.values():
                for item in trader_items:
                    if isinstance(item, dict):
                        code = item.get("tradeable-code", "")
                        if category == "All Items" or (code and code.startswith(category + "_")):
                            item["can-be-purchased"] = value
                            count += 1

            result_text = f"Bulk {action} operation completed.\n{count} items updated in category '{category}'"
            self.bulk_results.delete("1.0", "end")
            self.bulk_results.insert("1.0", result_text)

            self.status_callback(f"Bulk {action}d {count} items in {category}")
            self.refresh_callback()

    def bulk_fame_points(self):
        """Bulk fame points operation"""
        category = self.category_var.get()

        try:
            fame_points = int(self.bulk_fame_entry.get().strip())
            if not 0 <= fame_points <= 1000:
                messagebox.showerror("Error", "Fame points must be between 0 and 1000")
                return
        except ValueError:
            messagebox.showerror("Error", "Please enter a valid number for fame points")
            return

        if messagebox.askyesno("Confirm", f"Set fame points to {fame_points} for all items in '{category}'?"):
            count = 0
            traders = self.data.get("economy-override", {}).get("traders", {})

            for trader_items in traders.values():
                for item in trader_items:
                    if isinstance(item, dict):
                        code = item.get("tradeable-code", "")
                        if category == "All Items" or (code and code.startswith(category + "_")):
                            item["required-famepoints"] = str(fame_points)
                            count += 1

            result_text = f"Bulk fame points operation completed.\n{count} items updated to {fame_points} FP in category '{category}'"
            self.bulk_results.delete("1.0", "end")
            self.bulk_results.insert("1.0", result_text)

            self.status_callback(f"Set fame points to {fame_points} for {count} items in {category}")
            self.refresh_callback()


class CategoryManagementDialog:
    def __init__(self, parent, data, status_callback, refresh_callback, categories):
        self.data = data
        self.status_callback = status_callback
        self.refresh_callback = refresh_callback
        self.categories = categories

        self.window = ctk.CTkToplevel(parent)
        self.window.title("📂 Category Management")
        self.window.geometry("800x600")
        self.window.transient(parent)
        self.window.grab_set()

        self.create_interface()

    def create_interface(self):
        """Create category management interface"""
        # Main frame
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # Title
        title = ctk.CTkLabel(
            main_frame,
            text="📂 Category Management",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title.pack(pady=10)

        # Categories list
        if self.categories:
            categories_frame = ctk.CTkScrollableFrame(main_frame)
            categories_frame.pack(fill="both", expand=True, padx=10, pady=10)

            for category, count in self.categories[:20]:  # Show top 20 categories
                cat_frame = ctk.CTkFrame(categories_frame)
                cat_frame.pack(fill="x", padx=5, pady=2)

                cat_label = ctk.CTkLabel(cat_frame, text=f"{category} ({count} items)", anchor="w")
                cat_label.pack(side="left", padx=10, pady=5)

                edit_btn = ctk.CTkButton(
                    cat_frame,
                    text="Edit",
                    width=80,
                    command=lambda c=category: self.edit_category(c)
                )
                edit_btn.pack(side="right", padx=10, pady=5)
        else:
            no_cat_label = ctk.CTkLabel(main_frame, text="No categories found. Load a JSON file first.")
            no_cat_label.pack(expand=True)

        # Close button
        close_btn = ctk.CTkButton(main_frame, text="Close", command=self.window.destroy)
        close_btn.pack(pady=10)

    def edit_category(self, category):
        """Edit items in a specific category"""
        messagebox.showinfo("Category Edit", f"Category editing for '{category}' coming soon!\n\nUse the CLI version for advanced category operations.")


# Main execution
if __name__ == "__main__":
    app = SCUMEconomyGUI()
    app.root.mainloop()
