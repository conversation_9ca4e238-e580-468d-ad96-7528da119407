#!/usr/bin/env python3
"""
Simple Database Browser for SCUM Economy Test DB
Alternative to VS Code extension if connection fails
"""

import sqlite3
import json
from datetime import datetime

class SimpleDatabaseBrowser:
    def __init__(self, db_path="scum_economy_test.db"):
        self.db_path = db_path
        self.connection = sqlite3.connect(db_path)
        self.connection.row_factory = sqlite3.Row
        print(f"✅ Connected to: {db_path}")
    
    def show_tables(self):
        """Show all tables in the database"""
        cursor = self.connection.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("\n📋 DATABASE TABLES:")
        print("=" * 30)
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) as count FROM {table['name']}")
            count = cursor.fetchone()['count']
            print(f"📦 {table['name']}: {count} records")
    
    def show_schema(self, table_name):
        """Show table schema"""
        cursor = self.connection.cursor()
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        print(f"\n🏗️ SCHEMA FOR {table_name.upper()}:")
        print("=" * 40)
        for col in columns:
            print(f"   {col['name']}: {col['type']} {'(PRIMARY KEY)' if col['pk'] else ''}")
    
    def run_query(self, query, limit=10):
        """Run a custom SQL query"""
        cursor = self.connection.cursor()
        try:
            cursor.execute(query)
            results = cursor.fetchall()
            
            if results:
                print(f"\n🔍 QUERY RESULTS (showing first {min(limit, len(results))} of {len(results)}):")
                print("=" * 60)
                
                # Show column headers
                columns = [description[0] for description in cursor.description]
                header = " | ".join(f"{col[:15]:<15}" for col in columns)
                print(header)
                print("-" * len(header))
                
                # Show data rows
                for i, row in enumerate(results[:limit]):
                    row_data = " | ".join(f"{str(row[col])[:15]:<15}" for col in columns)
                    print(row_data)
                
                if len(results) > limit:
                    print(f"... and {len(results) - limit} more rows")
            else:
                print("No results found.")
                
        except Exception as e:
            print(f"❌ Query error: {e}")
    
    def show_sample_data(self):
        """Show sample data from each table"""
        tables = ['economy_items', 'change_history', 'custom_buckets']
        
        for table in tables:
            print(f"\n📊 SAMPLE DATA FROM {table.upper()}:")
            print("=" * 50)
            self.run_query(f"SELECT * FROM {table} LIMIT 3")
    
    def show_statistics(self):
        """Show database statistics"""
        print("\n📈 DATABASE STATISTICS:")
        print("=" * 30)
        
        # Total items
        cursor = self.connection.cursor()
        cursor.execute("SELECT COUNT(*) as total FROM economy_items")
        total = cursor.fetchone()['total']
        print(f"📦 Total Items: {total}")
        
        # Price statistics
        cursor.execute("""
            SELECT 
                MIN(base_purchase_price) as min_price,
                MAX(base_purchase_price) as max_price,
                AVG(base_purchase_price) as avg_price
            FROM economy_items
        """)
        price_stats = cursor.fetchone()
        print(f"💰 Price Range: ${price_stats['min_price']} - ${price_stats['max_price']}")
        print(f"📈 Average Price: ${price_stats['avg_price']:.2f}")
        
        # F.I.S.H. categories
        cursor.execute("""
            SELECT fish_category, COUNT(*) as count 
            FROM economy_items 
            WHERE fish_category IS NOT NULL 
            GROUP BY fish_category 
            ORDER BY count DESC 
            LIMIT 5
        """)
        categories = cursor.fetchall()
        print(f"\n🐟 TOP F.I.S.H. CATEGORIES:")
        for cat in categories:
            print(f"   {cat['fish_category']}: {cat['count']} items")
    
    def interactive_mode(self):
        """Interactive query mode"""
        print("\n🔍 INTERACTIVE QUERY MODE")
        print("Enter SQL queries (type 'exit' to quit, 'help' for examples)")
        print("=" * 50)
        
        while True:
            try:
                query = input("\nSQL> ").strip()
                
                if query.lower() == 'exit':
                    break
                elif query.lower() == 'help':
                    self.show_help()
                elif query:
                    self.run_query(query)
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def show_help(self):
        """Show example queries"""
        examples = [
            "SELECT * FROM economy_items WHERE tradeable_code LIKE '%AK%' LIMIT 5;",
            "SELECT fish_category, COUNT(*) FROM economy_items GROUP BY fish_category;",
            "SELECT * FROM economy_items ORDER BY base_purchase_price DESC LIMIT 10;",
            "SELECT tradeable_code, base_purchase_price FROM economy_items WHERE tradeable_code LIKE '2H_%';",
            "SELECT * FROM change_history ORDER BY created_at DESC LIMIT 5;",
            "SELECT * FROM custom_buckets;"
        ]
        
        print("\n💡 EXAMPLE QUERIES:")
        for i, example in enumerate(examples, 1):
            print(f"{i}. {example}")
    
    def close(self):
        """Close database connection"""
        self.connection.close()
        print("✅ Database connection closed")

def main():
    """Main function"""
    print("🗄️ SIMPLE DATABASE BROWSER")
    print("=" * 40)
    
    try:
        browser = SimpleDatabaseBrowser()
        
        # Show basic info
        browser.show_tables()
        browser.show_statistics()
        
        # Show schemas
        for table in ['economy_items', 'change_history', 'custom_buckets']:
            browser.show_schema(table)
        
        # Show sample data
        browser.show_sample_data()
        
        # Interactive mode
        print("\n" + "=" * 50)
        choice = input("Enter interactive mode? (y/n): ").lower()
        if choice == 'y':
            browser.interactive_mode()
        
    except Exception as e:
        print(f"❌ Database error: {e}")
    finally:
        if 'browser' in locals():
            browser.close()

if __name__ == "__main__":
    main()
