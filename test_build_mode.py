#!/usr/bin/env python3
"""
Test Build Mode functionality in isolation
"""

import customtkinter as ctk
import tkinter as tk
import time
import json
import os

class SimpleBuildModeTest:
    """Simple test of Build Mode functionality"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("🔧 Build Mode Test")
        self.root.geometry("800x600")
        
        # Build mode state
        self.build_mode_active = False
        self.dragging_widget = None
        self.drag_start_time = 0
        self.snap_grid_size = 10
        
        self.create_test_interface()
        
    def create_test_interface(self):
        """Create simple test interface"""
        # Header
        header = ctk.CTkFrame(self.root)
        header.pack(fill="x", padx=10, pady=5)
        
        title = ctk.CTkLabel(header, text="🔧 Build Mode Test", font=ctk.CTkFont(size=20, weight="bold"))
        title.pack(pady=10)
        
        # Build mode toggle
        self.build_btn = ctk.CTkButton(
            header,
            text="🔧 Activate Build Mode",
            command=self.toggle_build_mode,
            fg_color="#FF6B35",
            hover_color="#CC5429"
        )
        self.build_btn.pack(pady=5)
        
        # Test content
        content = ctk.CTkFrame(self.root)
        content.pack(fill="both", expand=True, padx=10, pady=5)
        
        # Some test widgets to drag
        for i in range(5):
            btn = ctk.CTkButton(content, text=f"Test Button {i+1}", width=120, height=35)
            btn.pack(pady=5, padx=20)
        
        # Status
        self.status = ctk.CTkLabel(self.root, text="Build Mode: OFF")
        self.status.pack(pady=5)
        
        # Bind hotkey
        self.root.bind("<Control-Shift-B>", lambda e: self.toggle_build_mode())
        
    def toggle_build_mode(self):
        """Toggle Build Mode"""
        self.build_mode_active = not self.build_mode_active
        
        if self.build_mode_active:
            self.build_btn.configure(text="✅ Exit Build Mode")
            self.status.configure(text="Build Mode: ON - Long right-click to drag")
            self.bind_drag_events(self.root)
            print("🔧 BUILD MODE ACTIVATED")
        else:
            self.build_btn.configure(text="🔧 Activate Build Mode")
            self.status.configure(text="Build Mode: OFF")
            self.unbind_drag_events(self.root)
            print("✅ BUILD MODE DEACTIVATED")
    
    def bind_drag_events(self, widget):
        """Bind drag events to widget and children"""
        try:
            widget.bind("<Button-3>", self.on_right_click_start)
            widget.bind("<ButtonRelease-3>", self.on_right_click_end)
            widget.bind("<B3-Motion>", self.on_drag_motion)
            
            for child in widget.winfo_children():
                self.bind_drag_events(child)
        except:
            pass
    
    def unbind_drag_events(self, widget):
        """Unbind drag events"""
        try:
            widget.unbind("<Button-3>")
            widget.unbind("<ButtonRelease-3>")
            widget.unbind("<B3-Motion>")
            
            for child in widget.winfo_children():
                self.unbind_drag_events(child)
        except:
            pass
    
    def on_right_click_start(self, event):
        """Start potential drag"""
        if not self.build_mode_active:
            return
            
        self.drag_start_time = time.time()
        self.dragging_widget = event.widget
        
        # Highlight widget
        try:
            self.dragging_widget.configure(highlightbackground="#FF6B35", highlightthickness=3)
        except:
            pass
        
        print(f"🖱️ Right-click start on {type(self.dragging_widget).__name__}")
    
    def on_right_click_end(self, event):
        """End drag"""
        if not self.build_mode_active or not self.dragging_widget:
            return
            
        hold_duration = time.time() - self.drag_start_time
        
        # Remove highlight
        try:
            self.dragging_widget.configure(highlightthickness=0)
        except:
            pass
        
        if hold_duration < 0.5:
            print(f"❌ Hold too short: {hold_duration:.3f}s")
        else:
            print(f"✅ Drag completed: {hold_duration:.3f}s")
        
        self.dragging_widget = None
    
    def on_drag_motion(self, event):
        """Handle drag motion"""
        if not self.build_mode_active or not self.dragging_widget:
            return
            
        hold_duration = time.time() - self.drag_start_time
        if hold_duration < 0.5:
            return
        
        # Try to move widget
        try:
            new_x = self.snap_to_grid(event.x)
            new_y = self.snap_to_grid(event.y)
            self.dragging_widget.place(x=new_x, y=new_y)
            print(f"🔧 Moving to ({new_x}, {new_y})")
        except Exception as e:
            print(f"❌ Move failed: {e}")
    
    def snap_to_grid(self, coordinate):
        """Snap to grid"""
        return round(coordinate / self.snap_grid_size) * self.snap_grid_size
    
    def run(self):
        """Run the test"""
        print("🔧 Starting Build Mode Test...")
        print("Instructions:")
        print("• Click '🔧 Activate Build Mode' or press Ctrl+Shift+B")
        print("• Long right-click (500ms+) on buttons to drag them")
        print("• Elements should snap to 10px grid")
        self.root.mainloop()

if __name__ == "__main__":
    app = SimpleBuildModeTest()
    app.run()
